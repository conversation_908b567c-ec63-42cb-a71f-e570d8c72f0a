import { configure } from 'mobx';
import { AnyObj } from '@weapp/ui';

// visual 
import { menuDesignVisual } from './components-share/menu-design';
import * as ebcoms from './ebcoms';
import { default as compConfigs } from './compConfigs';
import EBTransform from './transform';
import * as doc from './doc';
/* 导出库的样式文件，只在应用内使用不做共享的，请写在 style/index.less */
import './style/lib.less';

/* mobx 配置 */
configure({ enforceActions: 'always' });

/* 常量导出 */
export * as constants from './constants';

// 导出可能单独使用的组件
export { default as MenuDesign } from './components-share/menu-design';
export type { MenuDesignType, MenuDesignProps, MenuDesignDataType } from './components-share/menu-design';

export { default as CommonSetting } from './components-share/common-setting';
export type { CommonSettingType, CommonSettingProps } from './components-share/common-setting';

export { default as Comment } from './components-share/comment';
export type { CommentType, CommentProps } from './components-share/comment';

export { default as Alert } from './components-share/alert';

export { default as MReminderTime } from './components-share/m-reminder-time';
export type { MReminderTimeProps } from './components-share/m-reminder-time';

export const visual: AnyObj = {
  Menu: menuDesignVisual,
};

// 导出EB组件
export { ebcoms, EBTransform, compConfigs };

// 导出文档
export { doc };

// 导出 jsAPI 或者全局共享使用的 store 实例
// 一般很少直接使用 store 实例，或许变更可能性太大，尽量提供 jsAPI
export * from './utils/jsAPI';

/* 版本导出 */
export { default as version } from './libVersion';