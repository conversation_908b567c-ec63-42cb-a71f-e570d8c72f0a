
export const uiPropsDesignAppName = 'weapp-ui-props-design';

/* components-share */
export const ebcomClsPrefix = 'ebcom';

export const uiPropsDesignClsPrefix = 'weapp-ui-props-design';

export const menuDesignClsPrefix = `${uiPropsDesignClsPrefix}-menu-design`;

export const conditionClsPrefix = `${uiPropsDesignClsPrefix}-condition`;

export const commonSettingClsPrefix = `${uiPropsDesignClsPrefix}-common-setting`;

export const iconSelectionConfigClsPrefix = `${uiPropsDesignClsPrefix}-icon-selection-config`;

export const pageSizeConfigClsPrefix = `${uiPropsDesignClsPrefix}-page-size-config`;

export const iconConfigClsPrefix = `${uiPropsDesignClsPrefix}-icon-config`;

export const alertCommonClsPrefix = `${uiPropsDesignClsPrefix}-alert-common`;

export const commentCommonClsPrefix = `${uiPropsDesignClsPrefix}-comment-common`;

export const mReminderTimeClsPrefix = `${uiPropsDesignClsPrefix}-m-reminder-time`;

/* demo */
export const uiPropsDesignDemoClsPrefix = `ui-props-design-demo`;

/* components */
export const browserClsPrefix = `${uiPropsDesignClsPrefix}-browser`;
export const emptyClsPrefix = `${uiPropsDesignClsPrefix}-empty`;
export const breadCrumbClsPrefix = `${uiPropsDesignClsPrefix}-bread-crumb`;
export const alertClsPrefix = `${uiPropsDesignClsPrefix}-alert`;
export const matterAssociationClsPrefix = `${uiPropsDesignClsPrefix}-matter-association`;
export const ebcomCommentClsPrefix = `${ebcomClsPrefix}-comment`;
export const commentClsPrefix = `${uiPropsDesignClsPrefix}-comment`;
export const fastCreateClsPrefix = `${uiPropsDesignClsPrefix}-fast-create`;
export const reminderMethodClsPrefix = `${uiPropsDesignClsPrefix}-reminder-method`;
export const repeatFrequencyClsPrefix = `${uiPropsDesignClsPrefix}-repeat-frequency`;
export const mRepeatFrequencyClsPrefix = `${uiPropsDesignClsPrefix}-repeat-frequency-m`;
