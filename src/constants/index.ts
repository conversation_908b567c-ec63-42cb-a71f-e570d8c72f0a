import { IconNames } from "@weapp/ui";
import { MATTER_ASSOCIATION_EVENT_NAME } from "../components/matter-association/constants";
import { appInfo } from "@weapp/utils";

export * from './prefix';

export const dialogConfig = {
  scale: true,
  closable: true,
  icon: 'Icon-set-up-o' as IconNames,
  destroyOnClose: true,
}

export const LABEL_SPAN = 8;

export const LABEL_SPAN_13 = 13;

export const LABEL_SPAN_LG = 16;

export const EVENT_NAME = {
  MATTER_ASSOCIATION_EVENT_NAME,
}
export const LABEL_SPAN_NONE = 0;

export const appName = appInfo('@weapp/ui').libraryName;
