import React, { Component } from 'react';
import { Route, with<PERSON><PERSON><PERSON>, RouteComponentProps } from 'react-router-dom';
import { fomatParentPath } from '../../utils';
import Main from '../../pages/main';
import Design from '../../pages/main/Design';
import View from '../../pages/main/View';
import EbdDoc from '../../pages/ebddoc';
import MView from '../../pages/main/MView';

class MainRoute extends Component<RouteComponentProps> {
  render() {
    const parentPath: string = fomatParentPath(this.props);
    return (
      <>
        <Route weId={`${this.props.weId || ''}_ujr85h`}
          path={`${parentPath}/ui-props-design`}
          component={Main}
        />
        <Route weId={`${this.props.weId || ''}_81mptp`} 
          path={`${parentPath}/design(/)?:pageId?`}
          component={Design}
        />
        <Route weId={`${this.props.weId || ''}_81mptp`} 
          path={`${parentPath}/pageView(/)?:pageId?`}
          component={View}
        />
        <Route weId={`${this.props.weId || ''}_81mptp`}
          path={`${parentPath}/mPageView(/)?:pageId?`}
          component={MView}
        />
        <Route weId={`${this.props.weId || ''}_81mptp`} 
          path={`${parentPath}/ebddoc`}
          component={EbdDoc}
        />
      </>
    )
  }
}

export default withRouter(MainRoute);