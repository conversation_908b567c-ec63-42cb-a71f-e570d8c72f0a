// ui 转 EB组件
// 方法名称对应Ui库配置的compName  @refs { compName: 'NewButton' }
export const EbButton = (props: any) => {
    const config = {
        name: '按钮',
        btnType: props?.type || 'default',
        size: props?.size || 'middle',
        status: props?.disabled !== false ? 'enable' : 'disable',
        icon: '',
        btnText: '按钮',
        type: 'EbButton',
        layout: {}
    }
    return config;
}
    