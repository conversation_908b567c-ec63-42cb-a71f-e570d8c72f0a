import { Button } from "@weapp/ui";

export default {
    groups: [
        {
          id: 'setting',
          title: '基础设置',
          visible: true,
          custom: false,
        },
      ],
      items: {
        type: {
            groupId: 'setting',
            label: '按钮类型',
            labelSpan: 7,
            itemType: 'SELECT',
            style: { width: '100%' },
            data: [
              { id: 'default', content: '次要按钮', color: '' },
              { id: 'primary', content: '主要按钮', color: 'var(--primary)' },
              { id: 'success', content: '成功按钮', color: 'var(--success)' },
              { id: 'warning', content: '警告按钮', color: 'var(--warning)' },
              { id: 'danger', content: '危险按钮', color: 'var(--danger)' },
              { id: 'link', content: '文字按钮', color: 'btn-link' },
            ],
            value: 'default',
            otherParams: {
            // customRender: (props: any) => {
            //     return {
                    optionType: 'tag',
                customOptionRender: (option: any) => {
                  const { content, id } = option;
                  return (
                    <Button weId="hzswj2" type={id} inline={false}>
                      {content}
                    </Button>
                  );
                },
                className: `ebdcoms-config-button-btnType`,
                },
          },
          size: {
            custom: true,
            groupId: 'setting',
            label: '尺寸',
            labelSpan: 7,
            itemType: 'SELECT',
            data: [
              { id: 'small', content: '小' },
              { id: 'middle', content: '中' },
              { id: 'large', content: '大' },
            ],
            value: 'middle',
          },
          inline: {
            custom: true,
            label: '行内',
          labelSpan: 7,
          itemType: 'SWITCH',
          groupId: 'setting',
          style: {flexDirection: 'row'},
          value: false
        },
        href: {
            custom: true,
            label: '链接地址',
          labelSpan: 7,
          itemType: 'INPUT',
          groupId: 'setting',
          style: {flexDirection: 'row'},
          value: ''
        },
        disabled: {
            groupId: 'setting',
            label: '是否禁用',
            labelSpan: 7,
            itemType: 'SWITCH',
            style: {flexDirection: 'row'},
            value: false
          },
        radius: {
            custom: true,
            label: '圆角',
          labelSpan: 7,
          itemType: 'SWITCH',
          groupId: 'setting',
          style: {flexDirection: 'row'},
          value: false
        },
        title: {
            custom: true,
            label: '提示文字',
          labelSpan: 7,
          itemType: 'INPUT',
          groupId: 'setting',
          style: {flexDirection: 'row'},
          value: ''
        },
      },
}