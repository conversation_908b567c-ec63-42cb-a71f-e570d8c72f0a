import { Icon } from "@weapp/ui";
import IconConfig from "../../../components-share/icon-config";
import ebdcoms from "../../../utils/ebdcoms";
// import { getLocaleValue } from "../../../utils/transform";

const ImgDefault = require('./empty_default.png').default;
export const empty_trans_func = async (props: any) => {
  await ebdcoms.load();
  const { getLocaleValue } = ebdcoms.get();
  const { empty_title, empty_title_404, title, image,
    description, description_404, displayType, ...resProps } = props; // title 被 eb设计器占用
  let transConfig = {};

  const resultTitle = getLocaleValue(displayType === '404' ? empty_title_404 : empty_title);
  const resultDescription = getLocaleValue(displayType === '404' ? description_404 : description);

  if (resultTitle) transConfig = { ...transConfig, title: resultTitle };
  if (resultDescription) transConfig = { ...transConfig, description: resultDescription };

  if (image?.path) {
    transConfig = {
      ...transConfig, image: IconConfig.getIcon(image, {
        size: /^#/.test(image?.path) ? 'md' : 'pure', // 图标大小调整
      })
    }
  } else {
    // 默认图片处理
    transConfig = {
      ...transConfig,
      image: displayType === '404' ? <img src={ImgDefault} alt={resultDescription} /> : (
        <Icon weId={`${props.weId || ''}_02ltef`} name="Icon-No-data-available" style={{ width: 150, height: 150 }} />
      )
    }
  }

  return {
    displayType: 'image',
    ...resProps,
    ...transConfig
  };
}