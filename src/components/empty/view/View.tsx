import { PureComponent } from 'react'
import { ViewProps } from '@weapp/ebdcoms';
import { Empty } from '@weapp/ui';
import ebMiddleware from '../../../eb-middleware';

class EmptyView extends PureComponent<ViewProps> {
  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: ViewProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {
    },
  };

  style = { paddingTop: 20, paddingBottom: 40 }
  
  render() {
    const { className, config } = this.props;
    return (<Empty weId={`${this.props.weId || ''}_391rk9`}
      className={className}
      style={this.style}
      {...config}
    />)
  }
};
export default ebMiddleware('EmptyView', 'View', 'Empty', EmptyView);