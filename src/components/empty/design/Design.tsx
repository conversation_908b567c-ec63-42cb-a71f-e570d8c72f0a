import { PureComponent } from 'react'
import { DesignProps } from '@weapp/ebdcoms';
import { AnyObj, Empty } from '@weapp/ui';
import ebMiddleware from '../../../eb-middleware';

class EmptyDesign extends PureComponent<DesignProps> {
  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: DesignProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {
      title: '空状态',
      displayType: 'image',
      empty_title: '暂无数据',
      description: '',
      empty_title_404: '抱歉！您访问的页面在本服务器上不存在。',
      description_404: '请检查您输入的路径。',
    } as AnyObj,
  };

  style = { paddingTop: 20, paddingBottom: 40 }

  render() {
    const { className, config } = this.props;
    return (<Empty weId={`${this.props.weId || ''}_391rk9`}
      className={className}
      style={this.style}
      {...config}
    />)
  }
};
export default ebMiddleware('EmptyDesign', 'Design', 'Empty', EmptyDesign);