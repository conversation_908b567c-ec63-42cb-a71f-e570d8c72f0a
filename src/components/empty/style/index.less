.@{emptyClsPrefix}-config {
  &-iconSelection {
    .ui-icon {
      cursor: pointer;
      color: var(--regular-fc);

      &:hover {
        color: var(--primary);
      }

      &.active {
        color: var(--primary);
      }

    }

    &-icon {
      margin: var(--v-spacing-md) var(--h-spacing-md);
      padding: var(--v-spacing-md) var(--h-spacing-md);

      &.active {
        background-color: var(--primary);
        border-radius: var(--border-radius-sm);
      }

      &:hover {
        background-color: var(--primary);
        cursor: pointer;
        border-radius: var(--border-radius-sm);
      }
    }
  }

  &-image {
    span {
      font-size: var(--font-size-12);
      padding-left: var(--h-spacing-md);
    }

    .ui-formItem {
      padding: 0;
      &-label-span {
        padding: 0;
      }
      &:first-child {
        padding-bottom: var(--form-item-v-spacing);
      }
    }
  }
  &-imageStyle {
    .ui-formItem {
      padding-bottom: var(--form-item-v-spacing);
    }
  }
}