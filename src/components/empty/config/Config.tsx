import { getLabel } from "@weapp/utils";
import IconConfig from "../../../components-share/icon-config";
import { LABEL_SPAN } from "../../../constants";

const config = {
  /** 是否使用标题配置 */
  title: true,
  /** 是否使用底部区域配置 */
  footer: true,
  /** 是否使用固定区域配置 */
  fixedArea: false,
  groups: [
    {
      id: 'config',
      title: get<PERSON>abel('219086','内容设置'),
      visible: true,
      custom: false,
    },
  ],
  items: {
    empty_title: {
      itemType: 'LOCALEEX',
      filedType: "TEXTAREA",
    },
    description: {
      itemType: 'LOCALEEX',
      filedType: "TEXTAREA",
    },
    empty_title_404: {
      itemType: 'LOCALEEX',
      filedType: "TEXTAREA",
    },
    description_404: {
      itemType: 'LOCALEEX',
      filedType: "TEXTAREA",
    },
    displayType: {
      itemType: 'SELECT',
      data: [
        { id: '404', content: getLabel('219101','404页面未找到') },
        { id: 'image', content: getLabel('219102','无数据') },
      ]
    },
    image: {
      itemType: 'CUSTOM',
      customRender: (props: any) => (<IconConfig
        weId={`${props.weId || ''}_6dgvvw`}
        {...props}
        dataKey="image"
      />)
    }
  },
  layout: [
    [
      {
        id: 'displayType',
        label: getLabel('219103','展示类型'),
        labelSpan: LABEL_SPAN,
        items: ['displayType'],
        groupId: 'config',
        cascadeRulesOuter: {
          displayType: {
            '404': {
              hide: ['empty_title', 'description'],
              show: ['empty_title_404', 'description_404']
            },
            'image': {
              show: ['empty_title', 'description'],
              hide: ['empty_title_404', 'description_404']
            }
          }
        }
      },
    ],
    [
      {
        id: 'image',
        label: getLabel('219104','图片'),
        labelSpan: LABEL_SPAN,
        items: ['image'],
        groupId: 'config',
      },
    ],
    [
      {
        id: 'empty_title',
        label: getLabel('219105','主要文字描述'),
        labelSpan: 24,
        items: ['empty_title'],
        groupId: 'config',
      },
    ],
    [
      {
        id: 'description',
        label: getLabel('219106','次要文字描述'),
        labelSpan: 24,
        items: ['description'],
        groupId: 'config',
      },
    ],
    [
      {
        id: 'empty_title_404',
        label: getLabel('219105','主要文字描述'),
        labelSpan: 24,
        items: ['empty_title_404'],
        groupId: 'config',
        hide: true,
      },
    ],
    [
      {
        id: 'description_404',
        label: getLabel('219106','次要文字描述'),
        labelSpan: 24,
        items: ['description_404'],
        groupId: 'config',
        hide: true,
      },
    ],
  ],
  references: {
    displayType: ['image'],
  },
}

export default config;
