
import React, { memo, ReactText, useMemo, useCallback } from "react";
import { classnames } from "@weapp/utils";
import { Input, Icon, Dialog, IconNames, FormItem, FormValue } from '@weapp/ui';
import { emptyClsPrefix, LABEL_SPAN } from "../../../../constants";
import { CommonConfigCustomProps } from "../../../../types/common";
import ImageStyleConfig from "./ImageStyleConfig";

const iconNames_empty: IconNames[] = ["Icon-empty-Approval", "Icon-empty-Attendance", "Icon-empty-Business-form", "Icon-empty-CRM-statistics", "Icon-empty-Conference-Room", "Icon-empty-Construction-and-application-of-PC", "Icon-empty-CC-to", "Icon-empty-Departmental-budget", "Icon-empty-Display-not-supported", "Icon-empty-Electronic-signature", "Icon-empty-Enterprise-wechat", "Icon-empty-Employee-location", "Icon-empty-External-developments", "Icon-empty-FAQ", "Icon-empty-Free-version", "Icon-empty-Function-R-&-D-plan", "Icon-empty-Function-sharing", "Icon-empty-Header-list", "Icon-empty-Housekeeping", "Icon-empty-IM-message", "Icon-empty-Im-search", "Icon-empty-Join-the-team", "Icon-empty-Invoice-list", "Icon-empty-Leaving-a-message", "Icon-empty-List-of-attachments", "Icon-empty-Management-by-objectives", "Icon-empty-Module-owner", "Icon-empty-New-rule", "Icon-empty-No-download-in-progress", "Icon-empty-Marketing-activities", "Icon-empty-No-permission-notification", "Icon-empty-Operation-record", "Icon-empty-Notes", "Icon-empty-Original-order", "Icon-empty-PC-enterprise-wechat-authorization", "Icon-empty-Planning-Report", "Icon-empty-Notice-notice", "Icon-empty-Post", "Icon-empty-No-permission", "Icon-empty-No-search-results", "Icon-empty-Problem-reporting", "Icon-empty-Price", "Icon-empty-Statistical-report", "Icon-empty-Revenue-and-expenditure", "Icon-empty-Team-sharing", "Icon-empty-Upload-signature", "Icon-empty-Related-matters", "Icon-empty-Work-daily", "Icon-empty-Veriifcation-record", "Icon-empty-a-business-travel", "Icon-empty-capital", "Icon-empty-Work-news", "Icon-empty-clue", "Icon-empty-business-opportunity", "Icon-empty-competitor", "Icon-empty-contract", "Icon-empty-comment", "Icon-empty-contacts", "Icon-empty-customer", "Icon-empty-file", "Icon-empty-demand", "Icon-empty-drafts", "Icon-empty-follow", "Icon-empty-leave", "Icon-empty-No-history-Download", "Icon-empty-label", "Icon-empty-group", "Icon-empty-letterpress-material", "Icon-empty-notice", "Icon-empty-offer", "Icon-empty-performance-appraisal", "Icon-empty-order", "Icon-empty-news", "Icon-empty-personal", "Icon-empty-mail", "Icon-empty-pperformance-appraisal", "Icon-empty-product", "Icon-empty-project", "Icon-empty-purchase", "Icon-empty-recruit", "Icon-empty-sales-contract", "Icon-empty-schedule", "Icon-empty-set-up", "Icon-empty-short-message", "Icon-empty-task", "Icon-Approval-not-set", "Icon-No-data-available", "Icon-Loading-failed", "Icon-empty-Extract-file", "Icon-Enterprise-wechat-authorization", "Icon-Validation-failed", "Icon-empty-scrm03", "Icon-No-data-available@1x", "Icon-Graded-protection", "Icon-Emoticons", "Icon-Cancelled", "Icon-Checkinsucceeded", "Icon-Finished", "Icon-Signedin", "Icon-empty-groupvoting", "Icon-empty-Sentsuccessfully", "Icon-empty-failinsend", "Icon-Groupannouncement-k", "Icon-empty-Financial-voucher", "Icon-empty-Register-interface", "Icon-empty-Service-provider-settings", "Icon-empty-Maildetectioninprogress", "Icon-empty-Cancel-invoice-sharing", "Icon-empty-Invoice-has-been-collected", "Icon-empty-Invoice-sharing-expired", "Icon-Sensitive-words-colour", "Icon-empty-scanning", "Icon-empty-No-invoice-link", "Icon-Videoconferencing"]

/** IconSelection 后续替换为图标库提供的组件 */
interface IconSelectionProps extends CommonConfigCustomProps {
  weId?: string | null;
  value?: IconNames;
  onChange: (value?: IconNames) => void;
}
interface IconSingleProps {
  weId?: string | null;
  name?: IconNames;
  onClick?: (name?: IconNames) => void;
  active?: boolean;
}
const prefixCls = `${emptyClsPrefix}-config-iconSelection`;

const IconSingle = memo(React.forwardRef<{}, IconSingleProps>(
  (props) => {
    const { name, onClick: _onClick, active } = props;
    const Style = useMemo(() => ({ width: 100, height: 100 }), []);
    const onClick = useCallback(() => { _onClick?.(name) }, [_onClick, name]);

    const className = classnames(`${prefixCls}-icon`, { 'active': active });

    return <Icon weId={`${props.weId || ''}_6mcr3c`} name={name} className={className} style={Style} onClick={onClick} />
  }
))

interface IconSelectionState {
  visible: boolean;
}
class IconSelection extends React.Component<IconSelectionProps, IconSelectionState> {
  constructor(props: IconSelectionProps) {
    super(props);
    this.state = {
      visible: false,
    }
  }
  onClose = () => { this.setState({ visible: false }) };
  showDialog = () => { this.setState({ visible: true }) };
  onClick = (name?: IconNames) => {
    const { onChange } = this.props;
    onChange?.(name);
    this.onClose();
  }
  render() {
    const { value } = this.props;
    const { visible } = this.state;
    return (
      <div className={prefixCls}>
        <Icon weId={`${this.props.weId || ''}_ol16pr`} name="Icon-Basic-settings-o" onClick={this.showDialog} />
        {value && <Icon weId={`${this.props.weId || ''}_bi3gn7`} name="Icon-correct01" />}
        <Dialog
          weId={`${this.props.weId || ''}_p7clon`}
          title="选择图标"
          visible={visible}
          onClose={this.onClose}
          closable
        >
          {
            iconNames_empty?.map((icon: IconNames) => (
              <IconSingle weId={`${this.props.weId || ''}_1u06f9@${icon}`} active={value === icon} key={icon} name={icon} onClick={this.onClick} />
            ))
          }
        </Dialog>
      </div>
    )
  }
}

interface ImageConfigState {
  iconName?: IconNames;
  style?: {
    width?: ReactText;
    height?: ReactText;
  };
  imageUrl?: ReactText;
}
class ImageConfig extends React.Component<CommonConfigCustomProps, ImageConfigState>{
  constructor(props: CommonConfigCustomProps) {
    super(props);
    this.state = {}
  }
  componentDidMount(){
    const { config } = this.props;
    const { displayType, image, imageStyle } = config || {};
    let nState = null;
    if (displayType === 'icon' && image) {
      nState = { iconName: image.props?.name, style: image.props?.style }
    } else if (displayType === 'image') {
      nState = { imageUrl: image, style: imageStyle };
    }
    nState && this.setState(nState);
  }
  getSnapshotBeforeUpdate(prevProps: Readonly<CommonConfigCustomProps>) {
    const { displayType } = this.props.config || {};
    const { displayType: _displayType } = prevProps.config || {};
    if (_displayType !== displayType) {
      this.setState({ imageUrl: '', iconName: undefined, style: { width: '', height: ''} });
      this.props.onFormChange?.({ imageStyle: {}, image: '' })
    }
  }
  onInputChange = (value: ReactText) => {
    this.setState({ imageUrl: value });
  }
  onChangeIcon = (value?: IconNames) => {
    this.setState({
      iconName: value
    })
    this.props.onFormChange?.({
      image: <Icon weId={`${this.props.weId || ''}_tkrfx7`} name={value} style={this.state.style} />
    })
  }
  onChangeStyle = (key: string, value?: ReactText) => {
    value && this.setState({
      style: {
        ...this.state.style,
        [`${key}`]: typeof value === 'string' ? parseInt(value) : value,
      }
    })
  }
  onBlur = () => {
    const { config, onFormChange } = this.props;
    const { displayType } = config || {};
    let image = null;
    if (displayType === 'icon') {
      image = <Icon weId={`${this.props.weId || ''}_tkrfx7`} name={this.state.iconName} style={this.state.style} />
    } else {
      image = this.state.imageUrl;
    }
    image && onFormChange?.({ image });
  }
  onBlurStyle = () => {
    const { config, onFormChange } = this.props;
    const { displayType } = config || {};
    const { style } = this.state;
    if (displayType === 'icon') {
      onFormChange?.({ image: <Icon weId={`${this.props.weId || ''}_tkrfx7`} name={this.state.iconName} style={style} /> });
    } else {
      onFormChange?.({ imageStyle: style as FormValue })
    }
  }
  render() {
    const { style, iconName } = this.state;
    const { config, value } = this.props;
    const { displayType } = config || {};
    const label = displayType === 'icon' ? "选择图标" : "图片地址";
    let content = <IconSelection weId={`${this.props.weId || ''}_nhx1qg`} value={iconName} onChange={this.onChangeIcon} />;
    if (displayType === 'image') content = <Input weId={`${this.props.weId || ''}_qfe0lr`} value={value} onChange={this.onInputChange} onBlur={this.onBlur} />;
    // 等图标库支持选择空类型图标的组件后替换
    return <div className={`${emptyClsPrefix}-config-image`}>
      <FormItem weId={`${this.props.weId || ''}_9bp56a`} label={label} labelSpan={LABEL_SPAN}>
        {content}
      </FormItem>
      <ImageStyleConfig weId={`${this.props.weId || ''}_0sutik`}
        width={style?.width}
        height={style?.height}
        onChange={this.onChangeStyle}
        onBlur={this.onBlurStyle}
        displayType={displayType}
      />
    </div>
  }
}

export default ImageConfig;