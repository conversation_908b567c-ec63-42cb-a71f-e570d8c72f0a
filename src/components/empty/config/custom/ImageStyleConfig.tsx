import React, { ReactText } from "react";
import { Input, FormItem } from "@weapp/ui";
import { emptyClsPrefix, LABEL_SPAN } from "../../../../constants";

const { InputNumber } = Input;

interface ImageStyleConfigProps {
  width?: ReactText;
  height?: ReactText;
  onChange?: (key: string, value: string | number) => void;
  displayType: 'image' | 'icon';
  onBlur?: () => void;
}

class ImageStyleConfig extends React.Component<ImageStyleConfigProps>{

  onChangeWidth = (width: ReactText) => this.props.onChange?.('width', width )
  onChangeHeight = (height: ReactText) => this.props.onChange?.('height', height )
  render() {
    const { displayType, onBlur } = this.props;
    const { width, height } = this.props;

    return (
      <div className={`${emptyClsPrefix}-config-imageStyle`}>
        {
          displayType === 'icon' && (
            <FormItem weId={`${this.props.weId || ''}_sb9yh1`} label={"宽"} labelSpan={LABEL_SPAN}>
              <InputNumber weId={`${this.props.weId || ''}_cclqcd`} value={width} onChange={this.onChangeWidth} onBlur={onBlur} />
              <span>px</span>
            </FormItem>
          )
        }
        <FormItem weId={`${this.props.weId || ''}_sb9yh1`} label={"高"} labelSpan={LABEL_SPAN}>
          <InputNumber weId={`${this.props.weId || ''}_cclqcd`} value={height} onChange={this.onChangeHeight} onBlur={onBlur} />
          <span>px</span>
        </FormItem>
      </div>
    )
  }
}
export default ImageStyleConfig;