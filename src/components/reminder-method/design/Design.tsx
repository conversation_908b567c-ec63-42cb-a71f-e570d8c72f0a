import { PureComponent } from 'react'
//@ts-ignore
import { FormDefaultOptsType } from  '@weapp/formbuilder';
import MainContent from '../common';
import { designPrefixCls, ReminderMethodDefaultDesignProps } from '../constant';
import { CustomRenderContentProps, ReminderMethodDesignProps, ReminderMethodViewState } from '../types';
import { transValue } from '../utils';

const customHide = () => {
  return !window.enableUiPropsDesign_ReminderMethod;
}
export default class ReminderMethodDesign extends PureComponent<ReminderMethodDesignProps, ReminderMethodViewState> {
  static defaultOpts: FormDefaultOptsType = {
    form: {
      describe: false,
      title: false,
      wrapperClassNames: (className: string) => `${className} ${designPrefixCls}-wrap`,
    },
    compList: {
      customHide,
    }
  };

  //@ts-ignore
  static defaultProps: ReminderMethodDesignProps = ReminderMethodDefaultDesignProps;

  constructor(props: ReminderMethodDesignProps) {
    super(props);
    // @ts-ignore
    const widgetValue = props?.getWidgetValue?.()?.contentObj || {};
    this.state = {
      value: widgetValue,
    } as any
  }

  componentDidMount() {
    // this.props.events?.on('reminder-content-config-changed', this.onConfigChange)
  }
  
  componentWillUnmount() {
    // this.props.events?.off('reminder-content-config-changed', this.onConfigChange)
  }

  onConfigChange = (id: string, value: any) => {
    const val = transValue(id, {}, value?.config);
    this.setState({
      value: {
        ...this.state.value,
        ...val,
      }
    })
  }
  
  onChange = (id: string, value: any, flag?: boolean) => {
    const { value: widgetValue } = this.state;
    const contentObj = flag ? value : {
      ...widgetValue,
      [id]: value,
    };
    this.setState({ value: contentObj });
  }
  
  render() {
    // const { value } = this.state;
    const value = this.props.config?.defaultFieldData?.contentObj || {};
    return <MainContent weId={`${this.props.weId || ''}_gglvmv`}
      {...this.props}
      prefixClassName={designPrefixCls}
      onChange={this.onChange}
      value={value}
      isDesign
    />
  }
};
