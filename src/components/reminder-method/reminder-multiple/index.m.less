@import "../../../style/prefix.less";

.@{reminderMethodClsPrefix}-view-m-reminder-multiple , .@{reminderMethodClsPrefix}-design-m-reminder-multiple {
  &-container {
    background: var(--m-bg-base);
    &-content {
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      height: calc(100% - var(--m-dialog-button-group-height));
    }
  }
  &-enable {
    .ui-formItem-wrapper {
      justify-content: flex-end;
    }
  }
  &-content {
    &-top {
      display: flex;
      justify-content: space-between;
      padding: var(--v-spacing-lg) var(--h-spacing-lg);
      padding-bottom: var(--v-spacing-md);
      font-size: var(--font-size-14);
      &-title {
        color: var(--regular-fc);
      }
      &-btn {
        color: var(--primary);
      }
    }
    &-body {
      .ui-formSwitch-PLUGIN-MB .ui-formSwitch-requiredMark-container-mobile {
        align-items: center;
      }
      .ui-formSwitch-error-mobile-content {
        text-align: right;
      }
    }
  }
  &-add {
    padding: var(--v-spacing-md) var(--h-spacing-md);
    color: var(--primary);
    &-title {
      padding: calc(2* var(--hd)) var(--h-spacing-sm);
      font-size: var(--font-size-14);
    }
  }
  &-tip {
    color: var(--main-fc);
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.15;
  }
}

.@{reminderMethodClsPrefix}-view-m-reminder-multiple.readOnly {
  .ui-formItem-item-icon {
    display: none;
  }
}