import { forwardRef, useCallback, useEffect, useState } from "react";
import { ReminderSingleProps, ReminderSingleRef } from "../types";
import { Form, FormDatas, FormStore, FormStoreType } from "@weapp/ui";
import { getFormConfig } from "./utils";
import { getLabel } from "@weapp/utils";

const ReminderSingle = forwardRef<ReminderSingleRef, ReminderSingleProps>(
  (props) => {
    const { data, supportCustomReminderType, index, prefixClassName, onDelete: _onDelete, onChange, isMobile, getInstance, formItems, reminderTypeAuth } = props;
    const [store] = useState<FormStoreType>(new FormStore());

    const validate = useCallback(async () => {
      return store.validate?.();
    }, [store]);

    useEffect(() => {
      if (data?.id) {
        getInstance?.(`${data.id}`, { validate: validate as any });
      }
      return () => {
        getInstance?.(`${data.id}`, { validate: validate as any }, true);
      }
    }, []);

    useEffect(() => {
      const formConfig = getFormConfig(data, supportCustomReminderType, isMobile, { formItems, reminderTypeAuth });
      if (!store?.isFormInit) {
        const dataRes = {
          ...formConfig?.data, ...data, isMobile,
          remindTimeDaySingle_desc: getLabel('261601','天'),
          remindTimeMinSingle_desc: getLabel('261565','分钟'),
          remindTimeHourSingle_desc: getLabel('261564','小时'),
          reminderTimeTypeTip: getLabel('261580','开始时间'),
          remindCountCircle_desc: getLabel('261570','循环提醒'),
          remindCountCircle_descs: getLabel('261602','次'),
        };
        if (isMobile) {
          store.initForm({...formConfig, data: {
            ...dataRes,
            remindCategory: dataRes.remindCategory,
            remindType: dataRes.remindType,
            mRemindTimeSingle: {
              remindTimeSingle: dataRes.remindTimeSingle,
              remindTimeDaySingle: dataRes?.remindTimeDaySingle || 0,
              remindTimeDaySingle_desc: getLabel('261601','天'),
              remindTimeHourSingle: dataRes?.remindTimeHourSingle || 0,
              remindTimeHourSingle_desc: getLabel('261564','小时'),
              remindTimeMinSingle: dataRes?.remindTimeMinSingle || 0,
              remindTimeMinSingle_desc: getLabel('261565','分钟'),
            },
            mRemindTimeCircle: {
              remindTimeCircle: '0',
              remindCountCircle: 1,
              remindCountCircle_desc: getLabel('261570','循环提醒'),
              remindCountCircle_descs: getLabel('261602','次'),
            },
          } });
        } else {
          store.initForm({...formConfig, data: dataRes });
        }
        onChange?.(data?.id as string, dataRes );
      }
    }, [store, data, onChange, supportCustomReminderType, isMobile, formItems, reminderTypeAuth]);

    const doChange = useCallback((value?: FormDatas) => {
      const formDatas = store?.getFormDatas();
      const resultCom = { ...formDatas, ...value };
      let allDatas: any = {};
      if (isMobile) {
        allDatas = {
          ...resultCom,
          ...resultCom?.mRemindTimeSingle,
          ...resultCom?.mRemindTimeCircle,
          remindCategory: resultCom?.remindCategory,
          remindType: resultCom?.remindType,
        }
      } else {
        allDatas = resultCom;
      }
      let result: any = {
        remindCategory: allDatas?.remindCategory,
        remindType: allDatas?.remindType,
        id: allDatas?.id,
      }
      const remindCategory = allDatas?.remindCategory;
      if (remindCategory === '0') {
        // 单次提醒
        result = {
          ...result,
          remindTimeSingle: allDatas.remindTimeSingle,
          remindTimeDaySingle: allDatas.remindTimeDaySingle,
          remindTimeDaySingle_desc: allDatas.remindTimeDaySingle_desc,
          remindTimeHourSingle: allDatas.remindTimeHourSingle,
          remindTimeHourSingle_desc: allDatas.remindTimeHourSingle_desc,
          remindTimeMinSingle: allDatas.remindTimeMinSingle,
          remindTimeMinSingle_desc: allDatas.remindTimeMinSingle_desc,
        }
      } else {
        // 循环提醒
        result = {
          ...result,
          remindTimeCircle: allDatas.remindTimeCircle,
          remindCountCircle: allDatas.remindCountCircle,
          remindCountCircle_desc: allDatas?.remindCountCircle_desc,
          remindCountCircle_descs: allDatas?.remindCountCircle_descs,
        }
      }
      onChange?.(data?.id as string, result);
    }, [data?.id, isMobile, onChange, store]);
    const onDelete = useCallback(() => {
      _onDelete?.(index);
    }, [_onDelete, index]);

    return (
      <div className={`${prefixClassName}-content`}>
        <div className={`${prefixClassName}-content-top`}>
          <span className={`${prefixClassName}-content-top-title`}>{getLabel('261603','提醒')}{index+1}</span>
          <span className={`${prefixClassName}-content-top-btn`} onClick={onDelete}>{getLabel('207017','删除')}</span>
        </div>
        <div className={`${prefixClassName}-content-body`}>
          <Form weId={`${props.weId || ''}_evfpx4`} store={store}
            onChange={doChange} isMobile={isMobile}
            notDefaultLayout={!isMobile}
          />
        </div>
      </div>
    )
  }
);

export default ReminderSingle;