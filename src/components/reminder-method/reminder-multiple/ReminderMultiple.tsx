import { ReactText, forwardRef, useCallback, useMemo } from "react";
import { classnames, getLabel } from "@weapp/utils";
import { Button, Dialog, FormItem, Icon, Select, Switch, Input, AnyObj, SelectValueType } from "@weapp/ui";
import { ReminderMultipleProps } from "../types";
import { Options, getItems, getTip, labelSpan, OptionsType } from "./utils";
import './index.less';
import ReminderSingle from "./ReminderSingle";
import useDataControl from "./useDataControl";

const item = getItems();
const { InputNumber } = Input;
const ReminderFrequency = (props: AnyObj) => {
  const { prefixClassName, onChange, datas, id, readOnly } = props;
  const { remindTimeSingle, defaultValueSingleType, remindTimeDaySingle, remindTimeHourSingle, remindTimeMinSingle} = (datas?.[0] || {}) as any;
  const onChangeDefaultSingleType = useCallback((value: boolean) => {
    onChange?.(id, { enable: true, value: [{...datas?.[0], defaultValueSingleType: value ? 'enable' : 'disabled'}]});
  }, [datas, id, onChange]);

  const onChangeRemindTimeSingle = useCallback((value: SelectValueType) => {
    onChange?.(id, { enable: true, value: [{...datas?.[0], remindTimeSingle : value }]});
  }, [datas, id, onChange]);

  const onChangeRemindDaySingle = useCallback((value: ReactText, event: any) => {
    onChange?.(id, { enable: true, value: [{...datas?.[0], remindTimeDaySingle : value, remindTimeDaySingle_desc: getLabel('261601','天') }]});
  }, [datas, id, onChange]);

  const onChangeRemindHourSingle = useCallback((value: ReactText, event: any) => {
    onChange?.(id, { enable: true, value: [{...datas?.[0], remindTimeHourSingle : value, remindTimeHourSingle_desc: getLabel('261564','小时') }]});
  }, [datas, id, onChange]);

  const onChangeRemindMinSingle = useCallback((value: ReactText, event: any) => {

    onChange?.(id, { enable: true, value: [{...datas?.[0], remindTimeMinSingle: value, remindTimeMinSingle_desc: getLabel('261565','分钟') }]});
  }, [datas, id, onChange]);

  return <div className={`${prefixClassName}-reminder-multiple reminder-frequency`}>
    <div className="reminder-frequency-switch">
      <Switch weId={`${props.weId || ''}_xskyqe`} value={defaultValueSingleType === "enable"} onChange={onChangeDefaultSingleType} readOnly={readOnly} />
    </div>
    {
      defaultValueSingleType === "enable" && (
        <>
          <Select weId={`${props.weId || ''}_82fyq3`} data={Options[OptionsType.remindTimeSingleOptions]} value={remindTimeSingle} onChange={onChangeRemindTimeSingle} readOnly={readOnly} />
          {
            remindTimeSingle === '9' && (
              <>
                <InputNumber weId={`${props.weId || ''}_keosdt`} value={remindTimeDaySingle} onChange={onChangeRemindDaySingle} readOnly={readOnly} />
                <span>{getLabel('261601','天')}</span>
                <InputNumber weId={`${props.weId || ''}_keosdt`} value={remindTimeHourSingle} onChange={onChangeRemindHourSingle} readOnly={readOnly} />
                <span>{getLabel('261564','小时')}</span>
                <InputNumber weId={`${props.weId || ''}_keosdt`} value={remindTimeMinSingle} onChange={onChangeRemindMinSingle} readOnly={readOnly} />
                <span>{getLabel('261565','分钟')}</span>
              </>
            )
          }
        </>
      )
    }
  </div>
}
const ReminderMultiple = forwardRef<{}, ReminderMultipleProps>(
  (props) => {
    const { prefixClassName, title, hideTip, supportCustomReminderType, id, value, onChange, reminderFrequency, reminderTypeAuth, defaultValue, readOnly } = props;
    const { 
      datas, enable, visible,
      add, onDelete,
      showDialog, closeDialog,
      onSave, onChangeSingle, updateEnable,
      getInstance,
    } = useDataControl({ id, value, onChange, item, defaultValue, supportCustomReminderType, readOnly });
    
    const prefixCls = `${prefixClassName}-reminder-multiple`;
    const iconCls = classnames({ active: enable, readOnly });

    const footer = useMemo(() => ([
      <Button weId={`${props.weId || ''}_nurnan@sure`} key="sure" type="primary" onClick={onSave}>{getLabel('261599','确定')}</Button>,
    ]), [onSave, props.weId]);

    if (reminderFrequency === 'single') {
      return <ReminderFrequency weId={`${props.weId || ''}_dtcoco`} {...props} datas={value?.value} id={id} onChange={onChange} prefixCls={prefixCls} />
    }
    return <div className={prefixCls}>
      <Icon weId={`${props.weId || ''}_m76f26`}
        name={'Icon-set-up-o'}
        size="sm"
        className={iconCls}
        onClick={showDialog}
      />
      {
        !hideTip && (
          <span className={`${prefixCls}-tip`}>{getTip(value, supportCustomReminderType, false, { reminderTypeAuth })}</span>
        )
      }
      <Dialog
        weId={`${props.weId || ''}_d0e6mt`}
        title={title}
        visible={visible}
        icon="Icon-eb"
        closable
        mask
        onClose={closeDialog}
        footer={footer}
        height={600}
        width={800}
        destroyOnClose
      >
        <FormItem weId={`${props.weId || ''}_xm5xs4`} labelSpan={labelSpan} label={getLabel('207004','启用')} className={`${prefixCls}-enable`}>
          <Switch weId={`${props.weId || ''}_9e3tgh`} value={enable} onChange={updateEnable} />
        </FormItem>
        {
          enable && datas?.map((data, index) => (
            <ReminderSingle weId={`${props.weId || ''}_yrjxrd`} key={(data?.id as string) || index} data={data}
              index={index} prefixClassName={prefixCls} onDelete={onDelete} onChange={onChangeSingle}
              supportCustomReminderType={supportCustomReminderType} getInstance={getInstance} reminderTypeAuth={reminderTypeAuth} />
          ))
        }
        {
          enable && (
            <div className={`${prefixCls}-add`} onClick={add}>
              <Icon weId={`${props.weId || ''}_1v9zh7`} name="Icon-add-to01" />
              <span className={`${prefixCls}-add-title`}>{getLabel('261600','添加提醒')}</span>
            </div>
          )
        }
      </Dialog>
    </div>
  }
);

export default ReminderMultiple;