import { Loadable } from "@weapp/ui";
import { designPrefixCls } from "../constant";

const ReminderMultiple = Loadable({
  appName: 'weappUiPropsDesign',
  name: "ReminderMultiple",
  loader: () => import(
    /* webpackChunkName: "reminder-method-common" */
    './Main')
})

ReminderMultiple.defaultProps = {
  prefixClassName: designPrefixCls,
  reminderFrequency: 'multiple',
}
export default ReminderMultiple;