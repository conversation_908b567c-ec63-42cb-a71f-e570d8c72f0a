import { forwardRef, useMemo } from "react";
import { FormItem, Icon, MDialog, MSwitch, utils } from "@weapp/ui";
import { MReminderTime, MReminderTimeProps } from "../../../lib";
import { MReminderMultipleProps, ReminderMultipleValue } from "../types";
import useDataControl from "./useDataControl";
import "./index.m.less";
import { getItems, getTip, labelSpan, getRemindTimeSingleOptions } from "./utils";
import ReminderSingle from "./ReminderSingle";
import { observer } from "mobx-react";
import { classnames, getLabel } from "@weapp/utils";
import React from "react";
import { withRouter } from "react-router-dom";

const { Item } = FormItem;
const { ButtonGroup } = MDialog;
const { formatParentPath } = utils;
const item = getItems(true);
const options = getRemindTimeSingleOptions();
interface ReminderFrequencyProps extends MReminderTimeProps {
  onFormChange?: (id: string, value: ReminderMultipleValue) => void;
  id: string;
  readOnly?: boolean;
}
class ReminderFrequency extends React.Component<ReminderFrequencyProps> {
  onChange = (value: any) => {
    const { onFormChange, id } = this.props;
    onFormChange?.(id, { enable: true, value: [value] });
  }  
  render() {
    const { onFormChange, prefixCls, ...resProps } = this.props;
    return (
      <MReminderTime weId={`${this.props.weId || ''}_i0v699`} {...resProps} options={options} onChange={this.onChange} />
    )
  }
  
}

const MReminderFrequency = withRouter(ReminderFrequency);
const MReminderMultiple = observer(forwardRef<{}, MReminderMultipleProps>(
  (props) => {
    const { prefixClassName, title, supportCustomReminderType, id, value, onChange, mReminderMultipleId, history, reminderFrequency, reminderTypeAuth, defaultValue, readOnly } = props;
    let parentPath = formatParentPath(props as any);
    const path = `${parentPath}/reminder/${mReminderMultipleId}`;
    const { 
      datas, enable,
      add, onDelete,
      showDialog,
      getInstance,
      onSave, onChangeSingle, updateEnable
    } = useDataControl({ id, value, onChange, path, history, item, isMobile: true, defaultValue, supportCustomReminderType, readOnly });
    
    const prefixCls = `${prefixClassName}-reminder-multiple`;
    const btns = useMemo(() => [
      {
        id: 'sure',
        content: getLabel('261599','确定'),
        onClick: onSave,
        type: 'primary',
      }
    ] as any[], [onSave]);
    if (reminderFrequency === 'single') {
      return (
        <div className={prefixCls}>
          <div className={`${prefixCls}-container-content`}>
            <MReminderFrequency weId={`${props.weId || ''}_90jt5w`} value={value?.value?.[0]} id={id} onFormChange={onChange} readOnly={readOnly} />
          </div>
        </div>
      )
    }
    const cls = classnames(`${prefixCls}`, {
      readOnly,
    })
    return <div className={cls}>
      <Item weId={`${props.weId || ''}_oxchmt`} placeholder={getLabel('261563','请选择')} onClick={showDialog}>
        <span className={`${prefixCls}-tip`}>
          {getTip(value, supportCustomReminderType, true, { reminderTypeAuth })}
        </span>
      </Item>
      <MDialog
        weId={`${props.weId || ''}_ce159q`}
        isRouteLayout
        path={path}
        className={`${prefixCls}-container`}
        title={title}
      >
        <div className={`${prefixCls}-container-content`}>
          <FormItem weId={`${props.weId || ''}_5ypcmh`} labelSpan={labelSpan} label={getLabel('207004','启用')} className={`${prefixCls}-enable`} isMobile >
            <MSwitch weId={`${props.weId || ''}_5xgncj`} value={enable} onChange={updateEnable} isHoldRight />
          </FormItem>
          {
            enable && datas?.map((data, index) => (
              <ReminderSingle weId={`${props.weId || ''}_usgk46@${data?.id}`}  key={(data?.id as string) || index} data={data}
                index={index} prefixClassName={prefixCls} onDelete={onDelete} onChange={onChangeSingle}
                supportCustomReminderType={supportCustomReminderType} getInstance={getInstance}
                isMobile reminderTypeAuth={reminderTypeAuth}
              />
            ))
          }
          {
            enable && (
              <div className={`${prefixCls}-add`} onClick={add}>
                <Icon weId={`${props.weId || ''}_1v9zh7`} name="Icon-add-to01" />
                <span className={`${prefixCls}-add-title`}>{getLabel('261600','添加提醒')}</span>
              </div>
            )
          }
        </div>
        <ButtonGroup weId={`${props.weId || ''}_cjcwem`} datas={btns} />
      </MDialog>
    </div>
  }
));

export default MReminderMultiple;
