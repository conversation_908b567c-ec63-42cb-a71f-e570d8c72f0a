import { forwardRef } from "react";
import {  ReminderMultipleProps } from "../types";
import ReminderMultiple from "./ReminderMultiple";
import MMain from "./MMain";

const Main = forwardRef<{}, ReminderMultipleProps>(
  (props) => {
    const { isMobile } = props;
    if (isMobile) {
      return <MMain weId={`${props.weId || ''}_mdvdff`}  {...props} />
    }
    return <ReminderMultiple weId={`${props.weId || ''}_cjq9l0`} {...props} />
  }
);

export default Main;
