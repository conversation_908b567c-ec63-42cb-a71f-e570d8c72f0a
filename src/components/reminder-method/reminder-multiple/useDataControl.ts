import { useCallback, useEffect, useState } from "react";
import { FormDatas, utils, Dialog, MDialog, AnyObj, FormStoreType } from "@weapp/ui";
import { ReminderMultipleValue } from "../types";
import { getLabel } from "@weapp/utils";

interface DataControlProps {
  id: string;
  value?: ReminderMultipleValue;
  onChange?: (id: string, value: ReminderMultipleValue) => void;
  path?: string;
  history?: any;
  isMobile?: boolean;
  item?: AnyObj;
  defaultValue?: AnyObj;
  supportCustomReminderType?: boolean;
  readOnly?: boolean;
}

const { getRandom, isNil } = utils;
const { confirm } = Dialog;
const { prompt } = MDialog;

const instances = new Map();

const useDataControl = ({ id, value, onChange, path, history, isMobile, item, defaultValue, readOnly } : DataControlProps) => {
  const [enable, updateEnable] = useState<boolean>(value?.enable || false);
  const [visible, updateVisible] = useState<boolean>(false);
  const [datas, updateDatas] = useState<FormDatas[]>(value?.value || []);

  useEffect(() => {
    updateEnable(value?.enable || false);
    updateDatas(value?.value || []);
  }, [value]);
  
  const getInstance = useCallback((id: string, store: FormStoreType, remove?: boolean) => {
    if (id && store) {
      instances.set(id, store);
    }
    if (id && remove) {
      instances.delete(id);
    }
  }, []);

  const init = useCallback(() => {
    updateEnable(value?.enable || false);
    updateDatas((value?.value || []) as any);
  }, [value?.enable, value?.value]);
  
  const add = useCallback(() => {
    updateDatas([...datas,{
      id: getRandom(),
      remindTimeSingle: '0',
      remindCategory: '0',
      ...defaultValue,
    }, ]);
  }, [datas, defaultValue]);

  const onDelete = useCallback((index: number) => {
    const result = [...datas];
    result?.splice(index, 1);
    updateDatas(result);
  }, [datas]);

  const showDialog = useCallback(() => {
    if (readOnly) return;
    if (path && history) {
      history.push(path);
    } else {
      updateVisible(true);
    }
    if (!isNil(value?.enable)) {
      updateEnable(value?.enable || false);
      !isNil(value?.value) && updateDatas(value?.value as any);
    } else {
      add();
    }
  }, [add, history, path, readOnly, value?.enable, value?.value]);

  const closeDialog = useCallback(() => {
    updateEnable(false);
    updateDatas([]);
    if (path && history) {
      history.go(-1);
    } else {
      updateVisible(false);
    }
  }, [history, path]);

  const onSave = useCallback(() => {
    if (enable && (datas?.length || 0) <= 0) {
      isMobile ? prompt({
        prompt: false,
        children: getLabel('261608','确认要继续保存嘛？当前未设置提醒内容，保存后将关闭开关，不发送提醒'),
        onOk: () => {
          onChange?.(id, {
            enable: false, value: [],
          })
          updateEnable(false);
          updateDatas([]);
          closeDialog();
        }
      }) : confirm({
        content: getLabel('261608','确认要继续保存嘛？当前未设置提醒内容，保存后将关闭开关，不发送提醒'),
        onOk: () => {
          onChange?.(id, {
            enable: false, value: [],
          })
          updateEnable(false);
          updateDatas([]);
          closeDialog();
        }
      })
      return;
    }
    const instancesPromises: any[] = [];
    instances?.forEach((instance) => {
      instancesPromises.push(instance.validate());
    })
    Promise.all(instancesPromises).then((res: AnyObj[]) => {
      const pass = !res.find((re) => (re.errors && Object.keys(re.errors)?.length > 0));
      if (pass) {
        onChange?.(id, {
          enable: enable, value: datas,
        })
        closeDialog();
      }
    })
  }, [closeDialog, datas, enable, id, isMobile, onChange]);
  
  const onChangeSingle = useCallback((id: string, data?: FormDatas) => {
    let res = {};
    data && Object.keys(data).forEach(key => {
      const itemType = item?.[key]?.itemType;
      switch (itemType) {
        case "PLUGIN": res = {
          ...res,
          ...(data[key] || {}) as AnyObj,
        }; break;
        default: res = {
          ...res,
          [key]: data[key]
        }
      }
    });
    const result = datas?.map((da) => {
      if (da.id === id) {
        return res;
      }
      return da;
    })
    updateDatas(result);
  }, [datas, item]);

  return {
    datas, enable, visible,
    init, add, onDelete,
    showDialog, closeDialog, getInstance,
    onSave, onChangeSingle, updateEnable
  }
}

export default useDataControl;