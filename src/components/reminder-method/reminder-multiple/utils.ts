import { AnyObj, FormDatas, utils } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import { ReminderTypeOptions } from "../types";
const { isNil } = utils;
export const labelSpan = 6;

export const getRemindCategoryOptions = () => [
  { "id": "0", "content": getLabel('261568','单次提醒'), "disabled": false, "separator": false },
  { "id": "1", "content": getLabel('261570','循环提醒'), "disabled": false, "separator": false }
]

export const getRemindTimeSingleOptions = () => [
  { "id": "0", "content": getLabel('261552','正点'), "disabled": false, "separator": false },
  { "id": "1", "content": getLabel('261553','5分钟前'), "disabled": false, "separator": false },
  { "id": "2", "content": getLabel('261554','10分钟前'), "disabled": false, "separator": false },
  { "id": "3", "content": getLabel('261555','15分钟前'), "disabled": false, "separator": false },
  { "id": "4", "content": getLabel('261556','30分钟前'), "disabled": false, "separator": false },
  { "id": "5", "content": getLabel('261557','1小时前'), "disabled": false, "separator": false },
  { "id": "6", "content": getLabel('261559','2小时前'), "disabled": false, "separator": false },
  { "id": "7", "content": getLabel('261560','1天前'), "disabled": false, "separator": false },
  { "id": "8", "content": getLabel('261561','2天前'), "disabled": false, "separator": false },
  { "id": "9", "content": getLabel('261562','自定义'), "disabled": false, "separator": false }
]

export enum OptionsType {
  remindCategoryOptions = 'remindCategoryOptions',
  remindTimeSingleOptions = 'remindTimeSingleOptions',
};

export const Options = {
  [OptionsType.remindCategoryOptions]: getRemindCategoryOptions(),
  [OptionsType.remindTimeSingleOptions]: getRemindTimeSingleOptions()
}

export const remindTimeSingleKeys = ["remindTimeSingle", "remindTimeDaySingle", "remindTimeDaySingle_desc",
"remindTimeHourSingle", "remindTimeHourSingle_desc", "remindTimeMinSingle",
"remindTimeMinSingle_desc"
];


export const getItems = (isMobile?: boolean, otherParams?: AnyObj) => {
  let remindTypeOpts = [];
  if (otherParams) {
    if (!(!otherParams?.reminderTypeAuth?.enableSMS && otherParams?.formConfigData?.remindType?.indexOf('2') < 0)) {
      remindTypeOpts.push(
        { id: "2", content: getLabel('261547','短信提醒') },
      );
    }
    if (!(!otherParams?.reminderTypeAuth?.enableEmail && otherParams?.formConfigData?.remindType?.indexOf('3') < 0)) {
      remindTypeOpts.push(
        { id: "3", content: getLabel('261548','邮件提醒') },
      );
    }
    if (!(!otherParams?.reminderTypeAuth?.enableIM && otherParams?.formConfigData?.remindType?.indexOf('6') < 0)) {
      remindTypeOpts.push(
        { id: "6", content: getLabel('261549','IM消息提醒') },
      );
    }
  } else {
    remindTypeOpts = [
      { id: "2", content: getLabel('261547','短信提醒') },
      { id: "3", content: getLabel('261548','邮件提醒') },
      { id: "6", content: getLabel('261549','IM消息提醒') },
    ]
  }
  const commonItems = {
    remindCategory: {
      itemType: "SELECT",
      options: getRemindCategoryOptions(),
      otherParams: {
        isRouteLayout: true,
        isHoldRight: true,
        allowCancel: false,
        placeholder: getLabel('261563','请选择'),
      },
      value: '0'
    },
    remindType: {
      itemType: isMobile ? "SELECT" : "CHECKBOX",
      options: remindTypeOpts,
      value: '2',
      required: true,
      otherParams: {
        placeholder: getLabel('214701','请输入'),
        isHoldRight: true,
        multiple: true,
      }
    },
  }
  if (isMobile) {
    return {
      ...commonItems,
      mRemindTimeSingle: {
        itemType: "PLUGIN",
        required: true,
        value: {
          remindTimeSingle: '0',
        },
        pluginParams: {
          packageName: '@weapp/ui-props-design',
          compName: 'MReminderTime',
          options: getRemindTimeSingleOptions(),
        }
      },
      mRemindTimeCircle: {
        itemType: "PLUGIN",
        required: true,
        value: {
          remindTimeCircle: '0',
        },
        pluginParams: {
          packageName: '@weapp/ui-props-design',
          compName: 'MReminderTime',
          options: [
            { "id": "0", "content": getLabel('261618','每15分钟提醒1次'), "disabled": false, "separator": false },
            { "id": "1", "content": getLabel('261619','每30分钟提醒1次'), "disabled": false, "separator": false },
            { "id": "2", "content": getLabel('261620','每1小时提醒1次'), "disabled": false, "separator": false },
            { "id": "3", "content": getLabel('261621','每2小时提醒1次'), "disabled": false, "separator": false },
            { "id": "4", "content": getLabel('261622','每1天提醒1次'), "disabled": false, "separator": false },
            { "id": "5", "content": getLabel('261623','每2天提醒1次'), "disabled": false, "separator": false }
          ],
          type: 'multiple',
        }
      }
    }
  }
  return {
    ...commonItems,
    remindTimeSingle: {
      itemType: "SELECT",
      options: getRemindTimeSingleOptions(),
      required: true,
      otherParams: {
        isRouteLayout: true,
        isHoldRight: true,
        allowCancel: false,
        placeholder: getLabel('261563','请选择'),
      },
      value: "0"
    },
    remindTimeHourSingle: {
      itemType: "INPUTNUMBER",
      required: true,
      otherParams: {
        isRouteLayout: true,
        min: 0,
        isHoldRight: true,
        hideOps: true,
        max: 999,
        allowCancel: true,
        placeholder: getLabel('214701','请输入'),
        style: { width: 80 },
      },
      value: 0
    },
    remindCountCircle_desc: {
      itemType: "DESCRIPTION",
      otherParams: {
        isRouteLayout: true,
        isHoldRight: true,
        allowCancel: true,
      },
      value: getLabel('261570','循环提醒'),
    },
    remindTimeCircle: {
      itemType: "SELECT",
      options: [
        { "id": "0", "content": getLabel('261618','每15分钟提醒1次'), "disabled": false, "separator": false },
        { "id": "1", "content": getLabel('261619','每30分钟提醒1次'), "disabled": false, "separator": false },
        { "id": "2", "content": getLabel('261620','每1小时提醒1次'), "disabled": false, "separator": false },
        { "id": "3", "content": getLabel('261621','每2小时提醒1次'), "disabled": false, "separator": false },
        { "id": "4", "content": getLabel('261622','每1天提醒1次'), "disabled": false, "separator": false },
        { "id": "5", "content": getLabel('261623','每2天提醒1次'), "disabled": false, "separator": false }
      ],
      required: true,
      otherParams: {
        isRouteLayout: true,
        isHoldRight: true,
        allowCancel: false,
        placeholder: getLabel('261563','请选择'),
      },
      value: "0"
    },
    remindTimeDaySingle: {
      itemType: "INPUTNUMBER",
      required: true,
      otherParams: {
        "isRouteLayout": true,
        "min": 0,
        "isHoldRight": true,
        "hideOps": true,
        "max": 999,
        "allowCancel": true,
        placeholder: getLabel('214701','请输入'),
      },
      value: 0
    },
    remindTimeDaySingle_desc: {
      itemType: "DESCRIPTION",
      otherParams: {
        "isRouteLayout": true,
        "isHoldRight": true,
        "allowCancel": true,
        "placeholder": ""
      },
      value: getLabel('261601','天'),
    },
    remindCountCircle_descs: {
      itemType: "DESCRIPTION",
      otherParams: {
        "isRouteLayout": true,
        "isHoldRight": true,
        "allowCancel": true,
      },
      value: getLabel('261602','次'),
    },
    remindTimeMinSingle: {
      itemType: "INPUTNUMBER",
      required: true,
      otherParams: {
        "isRouteLayout": true,
        "min": 0,
        "isHoldRight": true,
        "hideOps": true,
        "max": 999,
        "allowCancel": true,
        placeholder: getLabel('214701','请输入'),
      },
      value: 0
    },
    remindTimeMinSingle_desc: {
      itemType: "DESCRIPTION",
      otherParams: {
        "isRouteLayout": true,
        "isHoldRight": true,
        "allowCancel": true,
        "placeholder": ""
      },
      value: getLabel('261565','分钟'),
    },
    remindTimeHourSingle_desc: {
      itemType: "DESCRIPTION",
      otherParams: {
        "isRouteLayout": true,
        "isHoldRight": true,
        "allowCancel": true,
        "placeholder": ""
      },
      value: getLabel('261564','小时'),
    },
    remindCountCircle: {
      itemType: "INPUTNUMBER",
      required: true,
      otherParams: {
        "isRouteLayout": true,
        "min": 0,
        "isHoldRight": true,
        "hideOps": true,
        "max": 5,
        "allowCancel": true,
        placeholder: getLabel('214701','请输入'),
      },
      value: 1
    },
  } as AnyObj;
} 
export const remindTimeCircleKeys = ["remindTimeCircle", "remindCountCircle_desc", "remindCountCircle", "remindCountCircle_descs"];

export const getContentById = (id: string, options: any[]): string => {
  const content = options?.find((da) => da.id === id)?.content || '';
  return content;
}

export const transValue = (key: string, data: FormDatas): string => {
  const item = getItems();
  const itemType = item[key]?.itemType;
  if (itemType === 'SELECT' && item[key]?.options) {
    return getContentById(data[key] as string, item[key]?.options);
  }
  if (itemType === 'DESCRIPTION') {
    const description = {
      remindTimeDaySingle_desc: getLabel('261601','天'),
      remindTimeMinSingle_desc: getLabel('261565','分钟'),
      remindTimeHourSingle_desc: getLabel('261564','小时'),
      reminderTimeTypeTip: getLabel('261580','开始时间'),
      remindCountCircle_desc: getLabel('261570','循环提醒'),
      remindCountCircle_descs: getLabel('261602','次'),
    } as AnyObj;
    if (description[key]) return description[key];
  }
  return `${isNil(data[key]) ? '' : data[key]}`;
}

export const getFormConfig = (initData: FormDatas, supportCustomReminderType?: boolean, isMobile?: boolean, otherParams?: AnyObj) => {
  return {
    groups: [],
    layout: [
      [ { id: 'remindCategory', label: getLabel('261624','提醒类型'), labelSpan, hide: false, items: ["remindCategory"],
          cascadeRulesOuter: {
            remindCategory: {
              '0': {
                show: ['remindTimeSingle'],
                hide: ['remindTimeCircle']
              },
              '1': {
                show: ['remindTimeCircle'],
                hide: ['remindTimeSingle']
              },
            }
          }
      } ],
      [ { id: "remindTimeSingle", label: getLabel('261577','提醒时间'), labelSpan, hide: (initData?.remindCategory || '0') !== '0',
        items: isMobile ? ['mRemindTimeSingle'] : initData?.remindTimeSingle === '9' ? remindTimeSingleKeys : ['remindTimeSingle'],
        cascadeRules: isMobile ? {} : {
          remindTimeSingle: {
            '0': [], '1': [], '2': [], '3': [], '4': [], '5': [],
            '6': [], '7': [], '8': [],
            '9': remindTimeSingleKeys?.filter((k) => k !== 'remindTimeSingle'),
          }
        } }],
      [ { id: "remindTimeCircle", label: getLabel('261577','提醒时间'), labelSpan, hide: initData?.remindCategory !== '1', items: isMobile ? ['mRemindTimeCircle'] : remindTimeCircleKeys }],
      [ { id: 'remindType', label: getLabel('261543','提醒方式'), labelSpan, hide: !supportCustomReminderType, items: ['remindType'] }]
    ],
    items: getItems(isMobile, {...otherParams, formConfigData: initData }),
    data: {
      id: "",
      ...initData,
    }
  } as any;
}

export const getTip = (value?: AnyObj, supportCustomReminderType?: boolean, isMobile?: boolean, otherParams?: AnyObj) => {
  const item = getItems(isMobile);
  let tip = '';
  if (value?.enable) {
    const _data = value?.value || [];
    _data?.forEach((data: any, dataIndex: number) => {
      let tempTip = '';
      if (data.remindCategory === '0') {
        // 单次提醒
        tempTip += transValue('remindTimeSingle', data);
        if (data?.remindTimeSingle === '9') {
          tempTip += '(';
          const keys = remindTimeSingleKeys;
          keys?.forEach((key, index) => {
            if (index !== 0) {
              tempTip += transValue(key, data)
            }
          })
          tempTip += ')';
        }
      } else {
        // 循环提醒
        remindTimeCircleKeys?.forEach((key) => { tempTip += transValue(key, data) })
      }
      if (supportCustomReminderType) {
        const remindType = data?.remindType;
        if (remindType) {
          const types: string[] = [];
          remindType.forEach?.((val: string) => {
            const v = getContentById(val, item['remindType']?.options);
            v && (types.push(v));
          });
          if (typeof remindType === 'string') {
            const v = getContentById(remindType, item['remindType']?.options);
            v && (types.push(v));
          }
          if (types.length > 0) {
            types?.length > 0 && (
              tempTip += `(${types?.join('/')})`
            )
          } else {
            tempTip = ''
          }
        }
      }
      if (dataIndex !== _data?.length - 1 && tempTip) tempTip += ';'
      tip += tempTip;
    })
    if (!tip) tip = getLabel('261625','不提醒');
  } else {
    tip = getLabel('261625','不提醒');
  }
  return tip;
}

export const getRemindTypeData = () => {
  return [
    {id: ReminderTypeOptions.SMS, content: getLabel('261547','短信提醒') },
    {id: ReminderTypeOptions.EMAIL, content: getLabel('261548','邮件提醒') },
    {id: ReminderTypeOptions.IM, content: getLabel('261549','IM消息提醒') },
  ];
}


export const getContent = (id: string, value: any, contentObj: AnyObj, _stateContent: AnyObj) => {
  let stateContent = {
    ..._stateContent,
  }
  // 立即提醒
  if (id === 'reminderType' || id === 'reminderImmediately') {
    if (contentObj?.reminderImmediately) {
      stateContent['reminderImmediately'] = getRemindTypeData()?.filter((data) => contentObj?.reminderType?.indexOf(data.id) > - 1)?.map((da) => da.content)?.join('、');
    } else {
      stateContent['reminderImmediately'] = '';
    }
  } else if (id === 'reminderBeforeStart' || id === 'reminderBeforeEnd') {
    if (contentObj?.[id]?.enable) {
      stateContent = {
        ...stateContent,
        [id]: getTip(contentObj?.[id], true)
      }
    }
  }
  return stateContent;
}