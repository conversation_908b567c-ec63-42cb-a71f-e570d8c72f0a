import React from "react";
import { withRouter } from "react-router-dom";
import { utils } from "@weapp/ui";
import { MReminderMultipleRouteProps } from "../types";
import MReminderMultiple from "./MReminderMultiple";

const { getRandom } = utils;

class MMain extends React.Component<MReminderMultipleRouteProps> {
  mReminderMultipleId = `mReminderMultipleId_dialog_${getRandom()}`
  render() {
    return <MReminderMultiple weId={`${this.props.weId || ''}_a4oyou`} {...this.props} mReminderMultipleId={this.mReminderMultipleId} />
  }
}

export default withRouter(MMain);
