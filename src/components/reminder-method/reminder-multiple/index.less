@import "../../../style/prefix.less";

.@{reminderMethodClsPrefix}-view-reminder-multiple , .@{reminderMethodClsPrefix}-design-reminder-multiple {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  .ui-icon:not(.readOnly) {
    color: var(--regular-fc);
    cursor: pointer;
  }
  &-tip {
    font-size: var(--font-size-12);
    color: var(--secondary-fc);
    padding-left: var(--h-spacing-md);
    color: var(--main-fc);
    align-self: center;
  }
  &-enable {
    border: var(--border-solid);
    background: var(--base-white);
  }
  &-content {
    &-top {
      padding: var(--v-spacing-md) 0;
      display: flex;
      font-size: var(--font-size-12);
      justify-content: space-between;

      &-title {
        color: var(--main-fc);
      }

      &-btn {
        color: var(--primary);
        cursor: pointer;
      }
    }
    &-body {
      .ui-formSwitch-description-wrapper {
        font-size: var(--font-size-12);
      }
    }
  }
  &-add {
    color: var(--primary);
    font-size: var(--font-size-12);
    padding: var(--v-spacing-md) 0;
    display: inline-flex;
    align-items: flex-start;
    cursor: pointer;
    &-title {
      padding: calc(2 * var(--hd)) var(--h-spacing-sm);
    }
  }
  &.reminder-frequency {
    display: flex;
    align-items: center;
    .ui-input.ui-input-number {
      max-width: calc(65 * var(--hd));
    }
    & > span, & > input, & > .ui-select {
      margin-left: var(--h-spacing-sm);
      font-size: var(--font-size-12);
    }
    .reminder-frequency-switch {
      display: inline-flex;
      align-items: center;
      min-height: calc(30 * var(--hd));
      padding: calc(5 * var(--hd)) 0;  
    }
  }
  &:not(.reminder-frequency) {
    display: inline-flex;
    align-items: center;
    min-height: calc(30 * var(--hd));
    line-height: calc(30 * var(--hd));
  }
}
