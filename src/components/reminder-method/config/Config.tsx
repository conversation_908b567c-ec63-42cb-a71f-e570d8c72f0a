import { getLabel } from '@weapp/utils';
import { ReminderMethodDefaultDesignProps, configPrefixCls } from '../constant';
import ReminderContent from './reminder-content';
import ReminderTypes from './reminder-types';
import './style/index.less';
import { reminderMethodClsPrefix } from '../../../constants';
import { AnyObj } from '@weapp/ui';
import { getContent } from '../reminder-multiple/utils';

const _transContent = (stateContent: AnyObj) => {
  let content = '';
  if (stateContent.reminderImmediately) {
    content += `${getLabel('261544','立即提醒')}(${stateContent.reminderImmediately})`;
  }
  if (stateContent.reminderBeforeStart) {
    if (content) content += `;`;
    content += `${getLabel('263866','开始前')}(${stateContent.reminderBeforeStart})`;
  }
  if (stateContent.reminderBeforeEnd) {
    if (content) content += `;`;
    content += `${getLabel('263867','结束前')}(${stateContent.reminderBeforeEnd})`;
  }
  return content;
}
export const transContent = (widgetValue: AnyObj) => {
  let stateContent = {};
  Object.keys(widgetValue).forEach((key) => {
    stateContent = getContent(key, widgetValue[key], widgetValue, stateContent);
  });
  const content = _transContent(stateContent) || getLabel('261625','不提醒');
  return content;
}

const config = {
  defaultConfig: {
    ...ReminderMethodDefaultDesignProps?.config,
  },
  data: {},
  layout: [
    [{ id: 'reminderTypes', label: getLabel('261543','提醒方式'), hide: false, className: `${reminderMethodClsPrefix}-config-reminderTypes-label`,
      items: ['reminderTypes'], labelSpan: 24, groupId: 'field' }],
    [{ id: 'reminderContent', label: getLabel('261582','提醒内容'), hide: false, className: `${reminderMethodClsPrefix}-config-reminderContent-label`,
      items: ['reminderContent'], labelSpan: 0, groupId: 'reminderContent' }]
  ],
  items: {
    reminderTypes: {
      itemType: 'CUSTOM',
      customRender: ReminderTypes,
      prefixClassName: configPrefixCls,
      wrapClassName: `${configPrefixCls}-reminderTypes`,
      valueItems: ['reminderTypes', 'reminderContent']
    },
    reminderContent: {
      label: getLabel('261582','提醒内容'),
      labelSpan: 0,
      itemType: 'CUSTOM',
      groupId: 'reminderContent',
      customRender: ReminderContent,
      prefixClassName: configPrefixCls,
      wrapClassName: `${configPrefixCls}-reminderContent`,
      valueItems: ['reminderTypes', 'reminderContent']
    },
  },
  groups: [
    { id: 'reminderContent', title: getLabel('261582','提醒内容'), visible: true, custom: false },
  ]
};

export default config;