import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { ReminderMethodTypesOptions, ReminderTypesProps } from "../../types";
import React from "react";
import { getLabel, request } from "@weapp/utils";
import Single from './Single';
import { AnyObj, Dialog } from "@weapp/ui";
import { configPrefixCls } from "../../constant";
import { transContent } from "../Config";
import { getValue } from "../../common/useReminderTypesControl";

const { message } = Dialog;
const ReminderTypes = memo(React.forwardRef<{}, ReminderTypesProps>(
  (props) => {
    const { wrapClassName = `${configPrefixCls}-reminderTypes`, config, onConfigChange, onChange: _onChange, value: propsValue, onFormChange } = props;
    const [loadingAuth, updateLoadingAuth] = useState<boolean>(true);
    const [reminderTypeAuth, updateReminderTypeAuth] = useState<AnyObj>({});
    useEffect(() => {
      request({
        url: '/api/bs/mc/handle/config/getStartChannels',
        method: 'POST',
        data: {},
      }).then((result) => {
        updateReminderTypeAuth({
          enableIM: result?.data?.indexOf(1) >= 0,
          enableSMS: result?.data?.indexOf(4) >= 0,
          enableEmail: result?.data?.indexOf(3) >= 0,
        })
        updateLoadingAuth(false);
      }).catch(() => {
        updateReminderTypeAuth({
          enableIM: (window.TEAMS?.isPurchasedModule?.('emmsg') || window.TEAMS?.isPurchasedModule?.('im')),
          enableSMS: window.TEAMS?.isPurchasedModule?.('sms'),
          enableEmail: window.TEAMS?.isPurchasedModule?.('email'),
        })
        updateLoadingAuth(false);
      })
    }, []);

    const originValue = useMemo(() => ({
      email : "notSelect",
      im: "notSelect",
      sms: "notSelect"
    }), []);
    const [value, updateValue] = useState<AnyObj>(config?.reminderTypes || propsValue?.reminderTypes || originValue);
    
    const datas = useMemo(() => {
      const res = [];
      reminderTypeAuth?.enableSMS && res.push({
        id: 'sms', content: getLabel('261547','短信提醒')
      });
      reminderTypeAuth?.enableEmail && res.push({
        id: 'email', content: getLabel('261548','邮件提醒')
      });
      reminderTypeAuth?.enableIM && res.push({
        id: 'im', content: getLabel('261549','IM消息提醒')
      });
      return res;
    }, [reminderTypeAuth]);

    const onChange = useCallback((id?: string, val?: AnyObj, key?: string) => {
      if (!id) return;
      const res = {...value} as ReminderMethodTypesOptions;
      if (val?.enable) {
        res[id] = val?.selected;
      } else {
        delete res[id];
      }
      if (key === 'enable') {
        const keys = Object.keys(res)?.filter((k) => datas?.find((d) => d.id === k));
        if (keys.length === 0) {
          message({
            type: 'info',
            content: getLabel('282316','至少启用一项提醒方式'),
          })
          return;
        }
      }
      updateValue(res);
      // _onChange?.(res)
      const contentObj = getValue({
        ...props?.value,
        reminderTypes: res,
      });
      const content = transContent?.(contentObj);
      onFormChange?.({ 
        reminderTypes: res,
        isDefault: true,
        defaultFieldData: {
          contentObj,
          content,
        }
      })
      // onConfigChange?.({
      //   reminderTypes: res,
      // })
    }, [datas, onFormChange, props?.value, value]);
    if (loadingAuth) return null;
    const tip = getLabel('281934','请在后台管理中心 > 消息推送设置 > 基本设置 > 消息渠道设置界面，启用消息渠道');
    if (!(reminderTypeAuth?.enableEmail || reminderTypeAuth?.enableIM || reminderTypeAuth?.enableSMS)) return <div className={wrapClassName}>
      <span className={`${wrapClassName}-reminder-tip`}>
        {tip}
      </span>
    </div>
    return <div className={wrapClassName}>
      {
        datas?.map((data) => {
          return <Single weId={`${props.weId || ''}_6hlweg@${data.id}`}
            key={data?.id}
            data={data}
            prefixClassName={`${wrapClassName}-single`}
            enable={value && Object.keys(value).find((k) => k === data?.id) ? true : false}
            selected={value?.[data?.id]}
            onChange={onChange}
          />
        })
      }
    </div>
  }
));

export default ReminderTypes;