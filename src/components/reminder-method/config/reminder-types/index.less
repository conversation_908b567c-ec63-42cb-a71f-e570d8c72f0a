.@{reminderMethodClsPrefix}-config-reminderTypes {
  &-single {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--h-spacing-md);
    flex-wrap: wrap;

    &-enable {
      min-height: calc(30 * var(--hd));
      margin-right: 0;
    }
    &-select {
      margin-left: calc(var(--checkbox-size-base) + calc(var(--hd)* 4));
      &.ui-select {
        width: auto!important;
      }
      .ui-select-input-wrap .ui-select-input {
        width: auto;
      }
    }
  }
  &-reminder-tip {
    font-size: 12px;
    display: flex;
    justify-content: center;
    padding: var(--v-spacing-sm) 0;
    color: var(--danger);
  }
}