import React, { useCallback, useMemo, useState } from "react";
import { memo } from "react";
import { ReminderSingleTypeProps } from "../../types";
import { Checkbox, CheckboxValueType, Select, SelectValueType } from "@weapp/ui";
import { getLabel } from "@weapp/utils";

const Single = memo(React.forwardRef<{}, ReminderSingleTypeProps>(
  (props) => {
    const { prefixClassName, data, onChange, selected, enable } = props;
    const [selectValue, updateSelectValue] = useState<SelectValueType>(selected || 'notSelect'); 
    const options = useMemo(() => {
      return [
        {id: 'notSelect', content: getLabel('281935','默认不选中')},
        {id: 'select', content: getLabel('281936','默认选中')},
      ]
    }, []);

    const onChangeSelect = useCallback((value: SelectValueType) => {
      updateSelectValue(value);
      onChange?.(data?.id, { enable, selected: value });
    }, [data?.id, enable, onChange]);

    const onChangeEnable = useCallback((value: CheckboxValueType) => {
      onChange?.(data?.id, { enable: value, selected: selectValue }, 'enable');
    }, [data?.id, onChange, selectValue]);
    
    return <div className={prefixClassName}>
      <Checkbox weId={`${props.weId || ''}_79bd3d`} className={`${prefixClassName}-enable`} value={enable} onChange={onChangeEnable}>{data?.content}</Checkbox>
      {
        enable && (
          <Select weId={`${props.weId || ''}_w7lwts`} className={`${prefixClassName}-select`} data={options} value={selectValue} onChange={onChangeSelect} />
        )
      }
    </div>
  }
));

export default Single;