.@{reminderMethodClsPrefix}-config-reminderContent {
  .ui-scroller__bar {
    display: none;
  }
  &-list-container {
    .ui-list-body div:not(:last-child) .ui-list-content {
      border-bottom: 0;
    }
    .ui-scroller__bar {
      display: none;
    }
    .ui-icon.my-handle {
      padding-right: var(--h-spacing-md);
    }
  }
  &-setting {
    &-icon {
      padding-left: var(--h-spacing-md);
      color: var(--regular-fc);
      cursor: pointer;
    }
    .ui-formSwitch-description-wrapper {
      font-size: var(--font-size-12);
    }
    .flex-no-wrap .ui-formItem-wrapper {
      flex-wrap: nowrap;
    }
    .@{reminderMethodClsPrefix}-design-reminder-multiple-tip {
      line-height: 1;
    }
    .ui-formSwitch .ui-input-number {
      max-width: calc(65 * var(--hd));
    }
    .defaultValueMultipleType {
      display: inline-flex;
      align-items: center;
      .@{reminderMethodClsPrefix}-design-reminder-multiple {
        min-height: calc(20 * var(--hd));
      }
    }
  }
  &-label {
    .ui-formItem-label-col {
      display: none;
    }
  }
}
.@{reminderMethodClsPrefix}-config-description {
  font-size: var(--font-size-12);
  line-height: calc(30 * var(--hd));
}
