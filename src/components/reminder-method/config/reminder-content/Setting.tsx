import React, { memo, useCallback, useMemo, useState } from "react";
import { AnyObj, Button, Dialog, Form, FormDatas, FormStore, FormStoreType, Icon } from "@weapp/ui";
import { ReminderContentSettingProps, ReminderMultipleValue } from "../../types";
import { classnames, getLabel } from "@weapp/utils";
import { getFormConfig } from "./utils";
import ReminderMultiple from "../../reminder-multiple";

const ReminderContentSetting = memo(React.forwardRef<{}, ReminderContentSettingProps>(
  (props) => {
    const { prefixClassName, type, title: _title, onChange, reminderContentConfig, pageId } = props;
    const [store] = useState<FormStoreType>(new FormStore());
    const [visible, updateVisible] = useState<boolean>(false);
    const [active] = useState<boolean>(false);
    const iconName = active ? "Icon-Setup-complete" : "Icon-set-up-o";
    const iconCls = classnames(`${prefixClassName}-reminderContent-setting-icon`, {
      active,
    })

    const title = reminderContentConfig?.fieldName || _title;

    const init = useCallback((justInit?: boolean) => {
      const datas = justInit && type !== 'reminderImmediately' ? { fieldName: title } : { fieldName: title, ...reminderContentConfig };
      store.initForm(getFormConfig(type, datas as FormDatas, { pageId }));
      store.setState({ validateHiddenField: false })
    }, [pageId, reminderContentConfig, store, title, type]);

    const showDialog = useCallback(() => { updateVisible(true); init(); }, [init]);
    const closeDialog = useCallback(() => updateVisible(false), []);
    const onSave = useCallback(() => {
      store.validate()?.then((errors) => {
        const pass = !(errors?.errors && Object.keys(errors?.errors).length > 0);
        if (pass) {
          const datas = store.getFormDatas();
          onChange?.(type, datas);
          closeDialog();
        }
      });
    }, [closeDialog, onChange, store, type]);
    const onDelete = useCallback(() => {
      // 恢复默认值
      init(true);
    }, []);
    const footer = useMemo(() => ([
      <Button weId={`${props.weId || ''}_e9sw3h@save`} key="save" type="primary" onClick={onSave}>{getLabel('223931','保存')}</Button>,
      <Button weId={`${props.weId || ''}_ytx3ba@delete}`} key="delete" onClick={onDelete}>{getLabel('263881','重置')}</Button>,
      <Button weId={`${props.weId || ''}_utly21@cancel}`} key="cancel" onClick={closeDialog}>{getLabel('223917','取消')}</Button>,
    ]), [closeDialog, onDelete, onSave, props.weId]);

    const onChangeReminderMultiple = useCallback((id: string, data: ReminderMultipleValue) => {
      store.updateDatas({
        [id]: data
      })
    }, [store]);

    const customRenderFormSwitch = useCallback((id: string, props: AnyObj) => {
      const formDatas = store?.getFormDatas();
      switch (id) {
        case 'defaultValueMultipleType': return <ReminderMultiple weId={`${props.weId || ''}_jrvr0l`}
          title={title as string} onChange={onChangeReminderMultiple} id={id}
          value={formDatas[id] as ReminderMultipleValue} supportCustomReminderType={false}
        />
      }
      return <div></div>
    }, [onChangeReminderMultiple, store, title]);

    return <>
      <Icon weId={`${props.weId || ''}_m76f26`}
        name={iconName}
        size="sm"
        className={iconCls}
        onClick={showDialog}
      />
      <Dialog weId={`${props.weId || ''}_g3otyc`}
        title={getLabel('261551','提醒内容设置')}
        visible={visible}
        icon="Icon-eb"
        closable
        mask
        onClose={closeDialog}
        footer={footer}
        height={600}
        width={840}
        destroyOnClose
        wrapClassName={`${prefixClassName}-reminderContent-setting`}
      >
        <Form weId={`${props.weId || ''}_ke3nek`}
          store={store}
          customRenderFormSwitch={customRenderFormSwitch}
          notDefaultLayout
        />
      </Dialog>
    </>
  }
));

export default ReminderContentSetting;