import { AnyObj, FormDatas, FormInitAllDatas } from "@weapp/ui";
import { ReminderContentTypeOptions } from "../../types";
import { configPrefixCls } from "../../constant";
import { getLabel } from "@weapp/utils";

const labelSpan = 6;
const getRemindTimeSingleOptions = () => [
  { "id": "0", "content": getLabel('261552','正点'), "disabled": false, "separator": false },
  { "id": "1", "content": getLabel('261553','5分钟前'), "disabled": false, "separator": false },
  { "id": "2", "content": getLabel('261554','10分钟前'), "disabled": false, "separator": false },
  { "id": "3", "content": getLabel('261555','15分钟前'), "disabled": false, "separator": false },
  { "id": "4", "content": getLabel('261556','30分钟前'), "disabled": false, "separator": false },
  { "id": "5", "content": getLabel('261557','1小时前'), "disabled": false, "separator": false },
  { "id": "6", "content": getLabel('261559','2小时前'), "disabled": false, "separator": false },
  { "id": "7", "content": getLabel('261560','1天前'), "disabled": false, "separator": false },
  { "id": "8", "content": getLabel('261561','2天前'), "disabled": false, "separator": false },
  { "id": "9", "content": getLabel('261562','自定义'), "disabled": false, "separator": false }
]
const getRemindTimeSingleItems = (type: ReminderContentTypeOptions) => {
  if (type === ReminderContentTypeOptions.IMMEDIATELY) return {};
  return {
    remindTimeSingle: {
      itemType: "SELECT",
      options: getRemindTimeSingleOptions(),
      required: true,
      otherParams: {
        isRouteLayout: true,
        isHoldRight: true,
        allowCancel: false,
        placeholder: getLabel('261563','请选择'),
      },
      value: "0"
    },
    remindTimeHourSingle: {
      itemType: "INPUTNUMBER",
      otherParams: {
        isRouteLayout: true,
        min: 0,
        isHoldRight: true,
        hideOps: true,
        max: 999,
        allowCancel: true,
        placeholder: getLabel('214701','请输入'),
      },
      value: 0
    },
    remindTimeDaySingle: {
      itemType: "INPUTNUMBER",
      otherParams: {
        "isRouteLayout": true,
        "min": 0,
        "isHoldRight": true,
        "hideOps": true,
        "max": 999,
        "allowCancel": true,
        placeholder: getLabel('214701','请输入'),
      },
      value: 0
    },
    remindTimeDaySingle_desc: {
      itemType: "DESCRIPTION",
      otherParams: {
        "isRouteLayout": true,
        "isHoldRight": true,
        "allowCancel": true,
        "placeholder": ""
      },
      value: getLabel('261601','天'),
    },
    remindTimeMinSingle: {
      itemType: "INPUTNUMBER",
      otherParams: {
        "isRouteLayout": true,
        "min": 0,
        "isHoldRight": true,
        "hideOps": true,
        "max": 999,
        "allowCancel": true,
        placeholder: getLabel('214701','请输入'),
      },
      value: 0
    },
    remindTimeMinSingle_desc: {
      itemType: "DESCRIPTION",
      otherParams: {
        "isRouteLayout": true,
        "isHoldRight": true,
        "allowCancel": true,
        "placeholder": ""
      },
      value: getLabel('261565','分钟'),
    },
    remindTimeHourSingle_desc: {
      itemType: "DESCRIPTION",
      otherParams: {
        "isRouteLayout": true,
        "isHoldRight": true,
        "allowCancel": true,
        "placeholder": ""
      },
      value: getLabel('261564','小时'),
    },
  } as AnyObj;
} 
export const getCustomFormConfig = (type: ReminderContentTypeOptions, data?: FormDatas, otherParams?: AnyObj): FormInitAllDatas => {
  const commonDatas = {
    reminderTimeType: 'whenSaving',
    reminderFrequency: 'multiple',
    defaultValueSingleEnable: true,
    defaultValueMultipleEnable: true,
    defaultValueSingleType: 'disabled',
    supportCustomReminderType: true,
    remindTimeSingle: '0',
    ...data,
    remindTimeDaySingle_desc: getLabel('261601','天'),
    remindTimeMinSingle_desc: getLabel('261565','分钟'),
    remindTimeHourSingle_desc: getLabel('261564','小时'),
    reminderTimeTypeTip: getLabel('261580','开始时间'),
    remindCountCircle_desc: getLabel('261570','循环提醒'),
    remindCountCircle_descs: getLabel('261602','次'),
  };
  const commonConfig: FormInitAllDatas = {
    groups: [],
    data: commonDatas,
    items: {
      reminderTimeType: {
        itemType: 'SELECT',
        options: [
          { id: "whenSaving", content: getLabel('261566','保存时') },
          { id: "formFields", content: getLabel('261567','表单字段') },
        ]
      },
      reminderFrequency: {
        itemType: "SELECT",
        options: [
          { id: "single", content: getLabel('261568','单次提醒') },
          { id: "multiple", content: getLabel('261569','多次提醒') },
        ]
      },
      supportCustomReminderType: {
        itemType: 'CHECKBOX',
        children: getLabel('261574','支持自定义提醒方式'),
      },
      defaultValueSingleEnable: {
        itemType: 'SWITCH',
      },
      defaultValueSingleType: {
        itemType: "SELECT",
        options: [
          { id: "disabled", content: getLabel('261576','默认禁用') },
          { id: "enable", content: getLabel('261575','默认启用') },
        ]
      },
      defaultValueMultipleEnable: {
        itemType: 'SWITCH',
      },
      defaultValueMultipleType: {
        itemType: "CUSTOM",
        wrapClassName: 'defaultValueMultipleType',
      },
      ...getRemindTimeSingleItems(type),
    },
    layout: [
      [{ id: 'reminderTimeType', label: getLabel('261577','提醒时间'), labelSpan, hide: false, items: ['reminderTimeType'], }],
      [{ id: 'reminderFrequency', label: getLabel('261578','提醒次数'), labelSpan, hide: false, items: commonDatas?.reminderFrequency === 'single' ? ['reminderFrequency'] : ['reminderFrequency', 'supportCustomReminderType'],
         cascadeRules: {
          reminderFrequency: {
            'single': [],
            'multiple': ['supportCustomReminderType']
          }
         },
         cascadeRulesOuter: {
          reminderFrequency: {
            'single': {
              show: ['defaultValueSingle'],
              hide: ['defaultValueMultiple']
            },
            'multiple': {
              show: ['defaultValueMultiple'],
              hide: ['defaultValueSingle']
            },
          }
         }
      }],
      [{ id: 'defaultValueSingle', label: getLabel('261579','字段默认值'), labelSpan, hide: commonDatas?.reminderFrequency === 'multiple',
        wrapClassName: commonDatas?.reminderFrequency === 'multiple' ? '' : 'flex-no-wrap',
        items: commonDatas?.defaultValueSingleEnable !== true ? ['defaultValueSingleEnable'] : 
          type !== ReminderContentTypeOptions.IMMEDIATELY ? 
          commonDatas?.remindTimeSingle === '9' ? 
          ['defaultValueSingleEnable', 'defaultValueSingleType', 'remindTimeSingle', "remindTimeDaySingle", "remindTimeDaySingle_desc",
          "remindTimeHourSingle", "remindTimeHourSingle_desc", "remindTimeMinSingle",
          "remindTimeMinSingle_desc"]
         : ['defaultValueSingleEnable', 'defaultValueSingleType', 'remindTimeSingle']
         : ['defaultValueSingleEnable', 'defaultValueSingleType'],
        cascadeRules: {
          defaultValueSingleEnable: {
            false: [],
            true: ['defaultValueSingleType', 'remindTimeSingle']
          },
          remindTimeSingle: {
            '0': [], '1': [], '2': [], '3': [], '4': [], '5': [],
            '6': [], '7': [], '8': [],
            '9': ["remindTimeDaySingle", "remindTimeDaySingle_desc",
            "remindTimeHourSingle", "remindTimeHourSingle_desc", "remindTimeMinSingle",
            "remindTimeMinSingle_desc"],
          }
        }
      }],
      [{ id: 'defaultValueMultiple', label: getLabel('261579','字段默认值'), labelSpan, hide: commonDatas?.reminderFrequency === 'single', items: ['defaultValueMultipleEnable', 'defaultValueMultipleType'],
        wrapClassName: commonDatas?.reminderFrequency === 'single' ? '' : 'flex-no-wrap',
        cascadeRules: {
        defaultValueMultipleEnable: {
          false: [],
          true: ['defaultValueMultipleType']
        }
      }
    }],
    ]
  }
  switch (type) {
    case ReminderContentTypeOptions.IMMEDIATELY: return {
      ...commonConfig,
      items: {
        ...commonConfig.items,
        reminderTimeType: {
          ...commonConfig?.items?.reminderTimeType,
          disabled: true,
        },
        reminderFrequency: {
          ...commonConfig?.items?.reminderFrequency,
          disabled: true,
        }
      }
    };
    case ReminderContentTypeOptions.BEFORESTART: 
    return {
      ...commonConfig,
      layout: commonConfig.layout?.map((row, index) => {
        if (index === 0) {
          return [{ id: 'reminderTimeType', label: getLabel('261577','提醒时间'), labelSpan, hide: false, items: ['reminderTimeType', 'reminderTimeTypeTip'] }];
        }
        return row;
      }),
      data: {
        ...commonConfig?.data,
        reminderTimeType: 'formFields',
      },
      items: {
        ...commonConfig?.items,
        reminderTimeType: {
          ...commonConfig?.items?.reminderTimeType,
          disabled: true,
        },
        reminderTimeTypeTip: {
          itemType:'DESCRIPTION',
          value: getLabel('261580','开始时间'),
          className: `${configPrefixCls}-description`,
        }
      }
    };
    case ReminderContentTypeOptions.BEFOREEND: return {
      ...commonConfig,
      layout: commonConfig.layout?.map((row, index) => {
        if (index === 0) {
          return [{ id: 'reminderTimeType', label: getLabel('261577','提醒时间'), labelSpan, hide: false, items: ['reminderTimeType', 'reminderTimeTypeTip'] }];
        }
        return row;
      }),
      data: {
        ...commonConfig?.data,
        reminderTimeType: 'formFields',
      },
      items: {
        ...commonConfig?.items,
        reminderTimeType: {
          ...commonConfig?.items?.reminderTimeType,
          disabled: true,
        },
        reminderTimeTypeTip: {
          itemType:'DESCRIPTION',
          value: getLabel('261581','结束时间'),
          className: `${configPrefixCls}-description`,
        }
      }
    };
    default: return {
      groups: [],
      data: {},
      items: {},
      layout: [],
    };
  }
}
export const getFormConfig = (type: ReminderContentTypeOptions, datas?: FormDatas, otherParams?: AnyObj) => {
  const formConfig = getCustomFormConfig(type, datas, otherParams);
  return {
    groups: [
      ...formConfig?.groups,
    ],
    layout: [
      [{ id: 'fieldName', label: getLabel('213305','字段名称'), labelSpan, hide: false, items: ['fieldName'] }],
      [{ id: 'describe', label: getLabel('214720','描述'), labelSpan, hide: false, items: ['describe'] }],
      ...formConfig?.layout,
    ],
    items: {
      fieldName: {
        required: true,
        itemType: "PLUGIN",
        pluginParams: {
          pageId: otherParams?.pageId,
          packageName: '@weapp/ebdcoms',
          compName: 'LocaleEx',
        }
      },
      describe: {
        itemType: "PLUGIN",
        pluginParams: {
          pageId: otherParams?.pageId,
          packageName: '@weapp/ebdcoms',
          compName: 'LocaleEx',
        }
      },
      ...formConfig?.items,
    },
    data: {
      ...formConfig?.data,
      ...datas,
      remindTimeDaySingle_desc: getLabel('261601','天'),
      remindTimeMinSingle_desc: getLabel('261565','分钟'),
      remindTimeHourSingle_desc: getLabel('261564','小时'),
      reminderTimeTypeTip: getLabel('261580','开始时间'),
      remindCountCircle_desc: getLabel('261570','循环提醒'),
      remindCountCircle_descs: getLabel('261602','次'),
    },
  } as FormInitAllDatas;
}