import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { memo } from "react";
import { FormDatas, Icon, List, ListData } from "@weapp/ui";
import { ReminderContentProps, ReminderContentTypeOptions, ReminderMethodSingleContent, ReminderMethodTypeOptions } from "../../types";
import LocaleEx from "../../../../ebdcoms/locale-ex";
import ReminderContentSetting from "./Setting";
import { getLabel } from "@weapp/utils";
import { configPrefixCls } from "../../constant";
import { transContent } from "../Config";
import { getValue } from "../../common/useReminderTypesControl";

const ReminderContent = memo(React.forwardRef<{}, ReminderContentProps>(
  (props) => {
    const orginDatas = useMemo(() => ([
      { id: ReminderContentTypeOptions.IMMEDIATELY, content: getLabel('261544','立即提醒') },
      { id: ReminderContentTypeOptions.BEFORESTART, content: getLabel('261545','开始前提醒') },
      { id: ReminderContentTypeOptions.BEFOREEND, content: getLabel('261546','结束前提醒') },
    ] as ReminderMethodSingleContent[]), []);

    const { config, prefixClassName = configPrefixCls, onFormChange, pageId, events } = props;
    const { reminderType, reminderContent } = config || {};
    const [selectedRowKeys, updateSelecedRowKeys] = useState<string[]>([]);
    const reminderContentDatas = useMemo(() => config?.reminderContent || orginDatas, [config?.reminderContent, orginDatas]);
    
    useEffect(() => {
      updateSelecedRowKeys(reminderType || []);
    }, [reminderType]);

    const onChangeReminderSetting = useCallback((id: string, datas: FormDatas) => {
      let value: any = {};
      const reminderContentRes: ReminderMethodSingleContent[] = [];
      reminderContent?.forEach((data) => {
        if (data.id === id) {
          value = {
            ...data,
            config: datas,
          }
          reminderContentRes.push(value)
        } else reminderContentRes.push(data);
      })
      // _onChange?.(reminderContentRes);
      const contentObj = getValue({
        ...props?.value,
        reminderContent: reminderContentRes,
      });
      const content = transContent?.(contentObj);
      onFormChange?.({
        reminderContent: reminderContentRes,
        isDefault: true,
        defaultFieldData: {
          contentObj,
          content,
        }
      })
      events?.emit('reminder-content-config-changed', id, value);
    }, [events, onFormChange, props?.value, reminderContent]);
    
    const customRenderContent = useCallback((item: ListData) => {
      let name = item?.config?.fieldName?.nameAlias;
      if (!name && item?.config?.fieldName && typeof item.config?.fieldName === 'string') {
        name = item.config?.fieldName;
      }
      return (<>
        <Icon weId={`${props.weId || ''}_zg9i5c`}  name="Icon-move" className="my-handle" />
        <LocaleEx weId={`${props.weId || ''}_qyl4t2`}
          value={name || item?.content}
          disabled
        />
        <ReminderContentSetting
          weId={`${props.weId || ''}_yrd869`}
          type={item?.id as any} 
          prefixClassName={prefixClassName} 
          title={item?.content}
          onChange={onChangeReminderSetting}
          reminderContentConfig={item?.config}
          pageId={pageId}
        />
      </>)
    }, [onChangeReminderSetting, pageId, prefixClassName, props.weId]);

    const onRowSelect = useCallback((keys: string[] | ListData[]) => {
      updateSelecedRowKeys(keys as string[]);
      const contentObj = getValue({
        ...props?.value,
        reminderType: keys as ReminderMethodTypeOptions[],
      });
      const content = transContent?.(contentObj);
      onFormChange?.({
        reminderType: keys as ReminderMethodTypeOptions[],
        isDefault: true,
        defaultFieldData: {
          contentObj,
          content,
        }
      });
    }, [onFormChange, props?.value]);

    const onSortEnd = useCallback((datas: string[] | ListData[]) => {
      const contentObj = getValue({
        ...props?.value,
        reminderContent: [...datas] as any,
      });
      const content = transContent?.(contentObj);
      onFormChange?.({
        reminderContent: [...datas] as ReminderMethodSingleContent[],
        isDefault: true,
        defaultFieldData: {
          contentObj,
          content,
        }
      });
    }, [onFormChange, props?.value]);

    return (
      <List weId={`${props.weId || ''}_zglenf`}
        className={`${prefixClassName}-reminderContent-list-container`}
        showCheck
        sortable
        multipleCheck
        horizontalScroll={false}
        customRenderContent={customRenderContent}
        data={reminderContentDatas}
        selectedRowKeys={selectedRowKeys}
        onRowSelect={onRowSelect}
        onSortEnd={onSortEnd}
      />
    )
  }
))

export default ReminderContent;