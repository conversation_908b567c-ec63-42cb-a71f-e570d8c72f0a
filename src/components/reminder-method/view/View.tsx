import { PureComponent } from 'react';
import { observer } from 'mobx-react';
import { getLabel } from '@weapp/utils';
import { AnyObj } from '@weapp/ui';
//@ts-ignore
import { FormDefaultOptsType } from  '@weapp/formbuilder';
import MainContent from '../common';
import { viewPrefixCls } from '../constant';
import { ReminderMethodViewProps, ReminderMethodViewState } from '../types';

import { getContent } from '../reminder-multiple/utils';


@observer
class ReminderMethodView extends PureComponent<ReminderMethodViewProps, ReminderMethodViewState> {
  static defaultOpts: FormDefaultOptsType = {
    form: {
      describe: false,
      title: false,
      wrapperClassNames: (className: string) => `${className} ${viewPrefixCls}-wrap`,
    }
  };

  componentDidMount() {
    this.props?.onFormComMount?.();
  }
  
  transContent = (stateContent: AnyObj) => {
    let content = '';
    if (stateContent.reminderImmediately) {
      content += `${getLabel('261544','立即提醒')}(${stateContent.reminderImmediately})`;
    }
    if (stateContent.reminderBeforeStart) {
      if (content) content += `;`;
      content += `${getLabel('263866','开始前')}(${stateContent.reminderBeforeStart})`;
    }
    if (stateContent.reminderBeforeEnd) {
      if (content) content += `;`;
      content += `${getLabel('263867','结束前')}(${stateContent.reminderBeforeEnd})`;
    }
    return content;
  }

  onChange = (id: string, value: any, flag?: boolean) => {
    //@ts-ignore
    const widgetValue = this.props?.getWidgetValue?.()?.contentObj || {};
    let _stateContent = {};
    Object.keys(widgetValue).forEach((key) => {
      _stateContent = getContent(key, widgetValue[key], widgetValue, _stateContent);
    });
    const contentObj = flag ? value : {
      ...widgetValue,
      [id]: value,
    };
    const stateContent = getContent(id, value, contentObj, _stateContent);
    const content = this.transContent(stateContent) || getLabel('261625','不提醒');
    this.setState({ value: contentObj, content: stateContent });
    // @ts-ignore 更新控件数据
    this.props.onChange?.({ content, contentObj });
    // @ts-ignore onChangeComplete 会执行数据联动
    this.props.onChangeComplete?.({ content, contentObj });
  }
  render() {
    // @ts-ignore
    const value = this.props?.getWidgetValue?.()?.contentObj || {};
    return <MainContent weId={`${this.props.weId || ''}_gglvmv`} {...this.props}
      prefixClassName={viewPrefixCls}
      onChange={this.onChange}
      value={value}
    />
  }
}
export default ReminderMethodView;