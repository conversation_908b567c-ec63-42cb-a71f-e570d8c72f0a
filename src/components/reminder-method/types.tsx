import { AnyObj, BaseProps, FormDatas } from "@weapp/ui";
// @ts-ignore
import { IFormItemViewProps } from "@weapp/formbuilder";
import { RouteComponentProps } from "react-router-dom";
/* common */

export enum ReminderTypeOptions {
  SMS = '2',
  EMAIL = '3',
  IM = '6',
  BRIDGE = 'bridge'
};

export enum ReminderContentTypeOptions {
  IMMEDIATELY = 'reminderImmediately',
  BEFORESTART = 'reminderBeforeStart',
  BEFOREEND = 'reminderBeforeEnd',
};

export interface CommonProps extends BaseProps {
  weId?: any;
  config?: ReminderMethodConfigType;
  prefixClassName?: string;  
  isMobile?: boolean;
}

/* context */

export interface ReminderMethodContextProps extends CommonProps {};

/* config */
export interface ReminderMethodTypesOptions {
  sms: 'select' | 'notSelect';
  email: 'select' | 'notSelect';
  im: 'select' | 'notSelect';
  [key: string]: 'select' | 'notSelect';
}
export type ReminderMethodTypeOptions = ReminderContentTypeOptions.IMMEDIATELY | ReminderContentTypeOptions.BEFORESTART | ReminderContentTypeOptions.BEFOREEND;
export interface ReminderMethodSingleContent {
  id: ReminderMethodTypeOptions; // 唯一值
  content: string; // 名称
  config: FormDatas; // 设置项
}
export interface ReminderMethodConfigType {
  reminderContent?: ReminderMethodSingleContent[];
  reminderType?: ReminderMethodTypeOptions[];
  reminderTypes?: ReminderMethodTypesOptions;
  [key: string]: any;
}

export interface ReminderContentProps extends CommonProps {
  onConfigChange?: (config: ReminderMethodConfigType) => void;
  onChange?: (config: ReminderMethodConfigType) => void;
  componentList?: AnyObj[]; // 表单设计器上所有表单字段
  pageId?: string;
  onFormChange?: (config: ReminderMethodConfigType) => void;
  events?: any;
  value?: AnyObj;
}

export interface ReminderContentSettingProps extends CommonProps {
  type: ReminderMethodTypeOptions;
  title?: string;
  reminderContentConfig?: FormDatas; //自定义设置项
  componentDateList?: AnyObj[]; // 表单设计器上所有日期表单字段
  onChange?: (id: string, value: FormDatas) => void;
  pageId?: string;
}

/* design + view */
export interface MainContentProps extends CommonProps {
  isMobile?: boolean;
  page?: AnyObj;
  id?: string;
  store?: any;
  onChange?: (key: string, value: any, flag?: boolean) => void;
  value?: AnyObj;
  isDesign?: boolean;
  viewType?: string;
  readOnly?: boolean;
  formProps?: AnyObj;
}

export interface FormItemProps extends CommonProps {
  formConfig?: AnyObj;
  formProps?: AnyObj;
  title?: string;
  id?: string;
  describe?: string;
  isExcel?: boolean;
  hideDivider?: boolean;
}

export interface ReminderTypeProps extends CommonProps {
  onChange?: (key: string, value: any, isFirst?: boolean) => void;
  dataId: string;
  value?: any;
  reminderTypeAuth?: AnyObj;
  readOnly?: boolean;
  defaultValue?: any;
  isDesign?: boolean;
}

export interface ReminderImmediatelyProps extends CommonProps {
  onChange?: (key: string, value: any) => void;
  dataId: string;
  value?: any;
  readOnly?: boolean;
}
export interface ReminderMultipleValue {
  /* 多次提醒配置 */
  value: FormDatas[];
  /* 启用 */
  enable: boolean;
}
export interface ReminderMultipleProps extends CommonProps {
  title?: string;
  hideTip?: boolean;
  /* 自定义提醒方式 */
  supportCustomReminderType?: boolean;
  value?: ReminderMultipleValue;
  onChange?: (id: string, value: ReminderMultipleValue) => void;
  id: string;
  reminderFrequency?: 'single' | 'multiple';
  reminderTypeAuth?: AnyObj;
  defaultValue?: AnyObj;
  readOnly?: boolean;
}

export interface MReminderMultipleProps extends ReminderMultipleProps {
  mReminderMultipleId: string;
  history: any;
}

export interface MReminderMultipleRouteProps extends ReminderMultipleProps, RouteComponentProps {

}
export interface ReminderSingleProps extends CommonProps {
  data: FormDatas;
  index: number;
  onDelete?: (index: number) => void;
  /* 自定义提醒方式 */
  supportCustomReminderType?: boolean;
  /* 配置更新回调 */
  onChange?: (id: string, data?: FormDatas) => void;
  getInstance?: (id: string, _store: any, remove?: boolean) => void;
  formItems?: string[];
  reminderTypeAuth?: AnyObj;
}

export interface ReminderSingleRef {
  validate?: () => Promise<AnyObj>;
}
export interface ReminderTimeProps extends CommonProps {
  value?: FormDatas;
  /* 配置更新回调 */
  onChange?: (id: string, data?: FormDatas) => void;
}
/* design */
export interface ReminderMethodDesignProps extends IFormItemViewProps<ReminderMethodConfigType> {
  events?: any;
}

export type CustomRenderContentProps = IFormItemViewProps & {
  // 组件内容
  content: React.ReactElement
  // 必填标识
  requiredMark: React.ReactElement
}

export interface ReminderMethodViewProps extends IFormItemViewProps {
  onFormComMount?: () => void;
}

export interface ReminderMethodViewState {
  value: AnyObj;
  content: AnyObj;
}

export interface ReminderTypesProps extends CommonProps {
  onConfigChange?: (config: ReminderMethodConfigType) => void;
  onFormChange?: (config: ReminderMethodConfigType) => void;
  wrapClassName?: string;
  value?: AnyObj;
  onChange?: (value?: AnyObj) => void;
}

export interface ReminderSingleTypeProps extends CommonProps {
  data: AnyObj;
  enable: boolean;
  selected: 'select' | 'notSelect';
  onChange?: (id?: string, value?: AnyObj, key?: string) => void;
}