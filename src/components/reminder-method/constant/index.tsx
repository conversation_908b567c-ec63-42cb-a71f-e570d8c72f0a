import { getLabel } from "@weapp/utils";
import { reminderMethodClsPrefix } from "../../../constants";
import { ReminderContentTypeOptions } from "../types";

export const configPrefixCls = `${reminderMethodClsPrefix}-config`;

export const designPrefixCls = `${reminderMethodClsPrefix}-design`;

export const viewPrefixCls = `${reminderMethodClsPrefix}-view`;

export const mDesignPrefixCls = `${reminderMethodClsPrefix}-design-m`;

export const mViewPrefixCls = `${reminderMethodClsPrefix}-view-m`;

export const getReminderMethodDefaultConfig = () => ({
  type: "ReminderMethod",
  isDefault: true,
  // fieldId、formId为表单固定属性
  fieldId: "",
  formId: "",
  title: getLabel('261543','提醒方式'),
  placeholder: getLabel('214701','请输入'),
  reminderType: [ReminderContentTypeOptions.IMMEDIATELY, ReminderContentTypeOptions.BEFORESTART, ReminderContentTypeOptions.BEFOREEND],
  reminderContent: [
    { id: ReminderContentTypeOptions.IMMEDIATELY, content: getLabel('261544','立即提醒'),
        config: {
          fieldName: getLabel('261544','立即提醒'), reminderTimeType: 'whenSaving', reminderFrequency: 'single',
          defaultValueSingleType: 'disabled', defaultValueSingleEnable: true,
        }
    },
    { id: ReminderContentTypeOptions.BEFORESTART, content: getLabel('261545','开始前提醒'),
      config: {
        fieldName: getLabel('261545','开始前提醒'), reminderTimeType: 'formFields', reminderFrequency: 'multiple',
        defaultValueMultipleEnable: true, supportCustomReminderType: true,
      }
    },
    { id: ReminderContentTypeOptions.BEFOREEND, content: getLabel('261546','结束前提醒'),
      config: {
        fieldName: getLabel('261546','结束前提醒'), reminderTimeType: 'formFields', reminderFrequency: 'multiple',
        defaultValueMultipleEnable: true, supportCustomReminderType: true,
      }
    },
  ],
  reminderTypes: {
    email : "notSelect",
    im: "notSelect",
    sms: "notSelect"
  }
})

export const ReminderMethodDefaultDesignProps = {
  config: getReminderMethodDefaultConfig(),
  events: {},
}