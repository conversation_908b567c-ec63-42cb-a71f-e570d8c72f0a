import { forwardRef, memo, useMemo } from "react";
import { FormItemProps } from "../types";
import { CorsComponent } from "@weapp/ui";
import { classnames } from "@weapp/utils";

const FormItem = memo(forwardRef<{}, FormItemProps>(
  (props) => {
    const { id, children, formConfig: _config, title, formProps, className, isExcel, isMobile, hideDivider, prefixClassName } = props;

    let describe = id === 'reminderType' ? _config?.describe : '';
    if (id !== 'reminderType') {
      const _con = _config?.reminderContent?.find((_c: { id: string | undefined; }) => _c.id === id);
      if (_con) {
        describe = _con?.config?.describe?.nameAlias || '';
      }
    }

    const config = useMemo(() => ({
      ..._config,
      title,
      titleLayout: 'field-hoz',
      describe: !(isExcel && id === 'reminderType') && !isMobile ? describe : '',
    }), [_config, describe, id, isExcel, isMobile, title])

    const cls = classnames(className, id);
    
    return <div className={cls}>
      {
        isMobile && !hideDivider && <div className={`${prefixClassName}-divider`} />
      }
      {
        isExcel && id === 'reminderType' && describe && (
          <div className={`weapp-form-widget-desc ${className}-describe excel`}>{describe}</div>
        )
      }
      <CorsComponent weId={`${props.weId || ''}_gc0bg2`}
        app="@weapp/formbuilder"
        compName="FormItemContainer"
        childNode={children}
        formProps={formProps}
        config={config}
      />
      {
        isMobile && describe && (
          <div className={`weapp-form-widget-desc ${className}-describe`}>{describe}</div>
        )
      }
    </div>
  }
));

export default FormItem;