import { forwardRef, memo, useCallback, useMemo } from "react";
import { ReminderTypeOptions, ReminderTypeProps } from "../types";
import { Checkbox, MSelect } from "@weapp/ui";
import { getLabel } from "@weapp/utils";

const ReminderType = memo(forwardRef<{}, ReminderTypeProps>(
  (props) => {
    const { prefixClassName, isMobile, onChange, dataId, value, reminderTypeAuth, readOnly } = props;
    
    const data = useMemo(() => {
      const datas = [];
      reminderTypeAuth?.enableSMS && datas.push(
        {id: ReminderTypeOptions.SMS, content: getLabel('261547','短信提醒') }
      );
      reminderTypeAuth?.enableEmail && datas.push(
        {id: ReminderTypeOptions.EMAIL, content: getLabel('261548','邮件提醒') }
      );
      reminderTypeAuth?.enableIM && datas.push(
        {id: ReminderTypeOptions.IM, content: getLabel('261549','IM消息提醒') },
      )
      return datas;
    }, [reminderTypeAuth]);

    const doChange = useCallback((value: any) => {
      onChange?.(dataId, value);
    }, [dataId, onChange]);

    const mselectValue = useMemo(() => {
      return value?.slice?.(0);
    }, [value]);
    
    if (data?.length <= 0) return <></>
    return (
      <div className={`${prefixClassName}-reminder-type`}>
        {
          isMobile ? (
            <MSelect weId={`${props.weId || ''}_t5mglw`}
              multiple
              data={data}
              onChange={doChange}
              value={mselectValue}
              readOnly={readOnly}
            />
          ) : (
            <Checkbox weId={`${props.weId || ''}_ks27ew`}
              data={data}
              onChange={doChange}
              value={value}
              readOnly={readOnly}
            />
          )
        }
      </div>
    )
  }
))

export default ReminderType;