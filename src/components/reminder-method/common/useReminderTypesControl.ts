import { useEffect, useState } from "react";
import { utils, AnyObj } from '@weapp/ui';
import { ReminderMethodConfigType } from "../types";
import { transValue } from "../utils";

interface ReminderTypesControlProps extends ReminderMethodConfigType {
  onChange?: (key: string, value: any, isFirst?: boolean) => void;
  isDesign?: boolean;
}

const { isEqual } = utils;
export const getValue = ({ reminderTypes, reminderContent }: ReminderTypesControlProps) => {
  let reminderType: string[] = [], reminderImmediately = false,
    reminderBeforeStart: AnyObj = {}, reminderBeforeEnd: AnyObj = {};
  if (reminderTypes) {
    Object.keys(reminderTypes).forEach(key => {
      if (reminderTypes[key] === 'select') {
        switch(key) {
          case 'sms': reminderType.push('2');break;
          case 'email': reminderType.push('3');break;
          case 'im': reminderType.push('6');break;
        }
      }
    });
    if (reminderContent) {
      reminderContent?.forEach(c => {
        if (c.id === "reminderImmediately" && c?.config?.defaultValueSingleEnable) {
          reminderImmediately = true;
        } else if (c.id === 'reminderBeforeStart') {
          reminderBeforeStart = transValue('value', {}, c?.config);
        } else if (c.id === 'reminderBeforeEnd') {
          reminderBeforeEnd = transValue('value', {}, c?.config);
        }
      })
    }
  }
  return {
    reminderType,
    reminderImmediately,
    reminderBeforeStart: reminderBeforeStart?.value,
    reminderBeforeEnd: reminderBeforeEnd?.value,
  };
}
const useReminderTypesControl = ({ reminderType, reminderTypes, onChange, isDesign }: ReminderTypesControlProps) => {
  const [value, updateValue] = useState<string[]>([]);
  useEffect(() => {
    if (reminderTypes && isDesign) {
      const res: any[] = []
      Object.keys(reminderTypes).forEach(key => {
        if (reminderTypes[key] === 'select') {
          switch(key) {
            case 'sms': res.push('2');break;
            case 'email': res.push('3');break;
            case 'im': res.push('6');break;
          }
        }
      });
      if (!isEqual(res, value)) {
        updateValue(res);
        if (!isEqual(res, reminderType)) {
          onChange?.('reminderType', res);
        }
      }
    }
  }, [isDesign, onChange, reminderType, reminderTypes, value])

  return {
    value
  }
}

export default useReminderTypesControl;