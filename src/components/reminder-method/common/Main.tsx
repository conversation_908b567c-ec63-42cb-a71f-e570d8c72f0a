import { forwardRef, memo, useCallback, useEffect, useMemo, useState } from "react";
import { classnames, getLabel, request, isEmpty, getLang } from "@weapp/utils";
import { AnyObj, utils } from "@weapp/ui";
import { MainContentProps, ReminderContentTypeOptions } from "../types";
import FormItem from "./Item";
import ReminderType from "./ReminderType";
import ReminderMultiple from "../reminder-multiple/index";
import ReminderImmediately from "./ReminderImmediately";
import { transValue } from "../utils";
import useReminderTypesControl from "./useReminderTypesControl";

const { isNil } = utils;

const MainContent = memo(forwardRef<{}, MainContentProps>(
  (props) => {
    const [loadingAuth, updateLoadingAuth] = useState<boolean>(true);
    const [reminderTypeAuthState, updateReminderTypeAuth] = useState<AnyObj>({});
    const [localeName, updateLocalName] = useState<AnyObj>();
    const { prefixClassName, config, page, id, store, isMobile, onChange: doChange, value, formProps, readOnly, isDesign } = props;
    const { viewType } = formProps || {};
    const isPrint = viewType === 'printPage' || viewType === 'printTemplate';
    const styleConfig = store?.getStyleComs?.()?.get(id);
    const { reminderType = [], reminderContent, reminderTypes } = config || {};
    const isChinese = (getLang() || 'zh-CN').indexOf('zh') !== -1;

    useEffect(() => {
      request({
        url: '/api/bs/mc/handle/config/getStartChannels',
        method: 'POST',
        data: {},
      }).then((result) => {
        updateReminderTypeAuth({
          enableIM: result?.data?.indexOf(1) >= 0,
          enableSMS: result?.data?.indexOf(4) >= 0,
          enableEmail: result?.data?.indexOf(3) >= 0,
        })
        updateLoadingAuth(false);
      }).catch(() => {
        updateReminderTypeAuth({
          enableIM: (window.TEAMS?.isPurchasedModule?.('emmsg') || window.TEAMS?.isPurchasedModule?.('im')),
          enableSMS: window.TEAMS?.isPurchasedModule?.('sms'),
          enableEmail: window.TEAMS?.isPurchasedModule?.('email'),
        })
        updateLoadingAuth(false);
      })
    }, []);
    const nameAliasConfig = useMemo(() => {
      return reminderContent?.filter((r: any) => (r?.config?.fieldName as any)?.nameAliasLabelId)?.map((r: any) => r.config?.fieldName?.nameAliasLabelId);
    }, [reminderContent]);

    useEffect(() => {
      if (!isChinese && (nameAliasConfig?.length || 0) > 0) {
        request({
          url: '/api/bs/i18n/labelmanage/getHtmlLabelNames',
          method: 'POST',
          data: nameAliasConfig,
        }).then((res) => {
          updateLocalName(res?.data || {});
        })
      }
    }, [isChinese, nameAliasConfig]);

    const reminderTypeAuth = useMemo(() => (isNil(reminderTypes) ? reminderTypeAuthState : {
      enableIM: reminderTypeAuthState.enableIM && reminderTypes?.im ? true : false,
      enableSMS: reminderTypeAuthState.enableSMS && reminderTypes?.sms ? true : false,
      enableEmail: reminderTypeAuthState.enableEmail && reminderTypes?.email ? true : false,
    }), [reminderTypeAuthState, reminderTypes]);
  
    const onChange = useCallback((id: string, v: any) => {
      doChange?.(id, v);
    }, [doChange]);

    const onChangeReminderType = useCallback((id: string, typeVal: any, isFirst?: boolean) => {
      let val = {...value, [id]: typeVal } || { [id]: typeVal };
      // 新建状态，首次选中提醒方式时处理
      if (Object.keys(value || {}).length === 0) {
        if (isNil(val?.reminderImmediately)) {
          const immediatelyConfig = reminderContent?.find((v) => v.id === ReminderContentTypeOptions.IMMEDIATELY)?.config;
          if (immediatelyConfig?.defaultValueSingleEnable) {
            val = {
              ...val,
              reminderImmediately: immediatelyConfig?.defaultValueSingleType === 'enable',
            }
          }
        }
        if (isNil(val?.reminderBeforeStart)) {
          const _config = reminderContent?.find((v) => v.id === ReminderContentTypeOptions.BEFORESTART)?.config;
          val = transValue('reminderBeforeStart', val, _config);
        }
        if (isNil(val?.reminderBeforeEnd)) {
          const _config = reminderContent?.find((v) => v.id === ReminderContentTypeOptions.BEFOREEND)?.config;
          val = transValue('reminderBeforeEnd', val, _config);
        }
        doChange?.('', val, true);
      } else if (!isFirst) {
        doChange?.(id, typeVal)
      }
    }, [doChange, reminderContent, value]);

    const reminderTypeValLength = value?.reminderType?.length || 0;
    const getName = useCallback((item?: AnyObj) => {
      const nameAliasLabelId = item?.config?.fieldName?.nameAliasLabelId;
      let name = item?.config?.fieldName?.nameAlias;
      if (!name && item?.config?.fieldName && typeof item?.config?.fieldName === 'string') {
        name = item?.config?.fieldName;
      }
      // 多语言场景兼容
      if (!isChinese && nameAliasLabelId && localeName?.[nameAliasLabelId]) name = localeName?.[nameAliasLabelId];
      if (!name) {
        switch(item?.id) {
          case ReminderContentTypeOptions.IMMEDIATELY: name = getLabel('261544','立即提醒');break;
          case ReminderContentTypeOptions.BEFORESTART: name = getLabel('261545','开始前提醒');break;
          case ReminderContentTypeOptions.BEFOREEND: name = getLabel('261546','结束前提醒');break;
        }
      }
      return name;
    }, [isChinese, localeName]);

    const getOtherConfig = useCallback((data) => {
      if (data.id === ReminderContentTypeOptions.IMMEDIATELY) {
        return {
          show: reminderType.indexOf(ReminderContentTypeOptions.IMMEDIATELY) >= 0 && reminderTypeValLength > 0,
        }
      } else if (data.id === ReminderContentTypeOptions.BEFORESTART) {
        return {
          show: reminderType.indexOf(ReminderContentTypeOptions.BEFORESTART) >= 0 && reminderTypeValLength > 0,
          supportCustomReminderType: data?.config?.supportCustomReminderType,
          reminderFrequency: data?.config?.reminderFrequency,
        }
      } else {
        return {
          show: reminderType.indexOf(ReminderContentTypeOptions.BEFOREEND) >= 0 && reminderTypeValLength > 0,
          supportCustomReminderType: data?.config?.supportCustomReminderType,
          reminderFrequency: data?.config?.reminderFrequency,
        }
      }
    }, [reminderType, reminderTypeValLength]);
    const formConfig = useMemo(() => {
      const reminderContentConfig = reminderContent?.map((data) => {
        return {
          id: data.id,
          title: getName(data),
          className: `${prefixClassName}-reminder-mainContent-container`,
          type: data.id,
          ...getOtherConfig(data),
        }
      }) || [];
      return [
        {
          id: 'reminderType',
          title: config?.title || getLabel('261543','提醒方式'),
          className: `${prefixClassName}-reminder-mainContent-container`,
          type: 'ReminderType',
          show: true,
        },
        ...reminderContentConfig,
      ]
    }, [reminderContent, config?.title, prefixClassName, getName, getOtherConfig]);
    
    const defaultReminderValue = useMemo(() => {
      return {
        remindType: (value?.reminderType || [])?.filter((v: string) => {
          return !(
            (v === '2' && !reminderTypeAuth?.enableSMS) ||
            (v === '3' && !reminderTypeAuth?.enableEmail) ||
            (v === '6' && !reminderTypeAuth?.enableIM)
          )
        }),
      }
    }, [value?.reminderType, reminderTypeAuth]);

    // useReminderTypesControl({ isDesign: isDesign || (!isDesign && (isNil(value) || isEmpty(value))), reminderType: value?.reminderType, reminderTypes, onChange: onChangeReminderType });

    const getContent = useCallback((config: AnyObj) => {
      switch(config?.type) {
        case 'ReminderType': return (
          <ReminderType
            weId={`${props.weId || ''}_yf4po9@${config?.id}`}
            prefixClassName={prefixClassName}
            isMobile={isMobile}
            onChange={onChangeReminderType}
            value={value?.reminderType}
            dataId="reminderType"
            reminderTypeAuth={reminderTypeAuth}
            readOnly={readOnly || isPrint}
            isDesign={isDesign}
          />
        );
        case ReminderContentTypeOptions.IMMEDIATELY: return (
          <ReminderImmediately
            weId={`${props.weId || ''}_exgdkq`}
            prefixClassName={prefixClassName}
            isMobile={isMobile}
            onChange={onChange}
            dataId="reminderImmediately"
            value={value?.reminderImmediately}
            readOnly={readOnly || isPrint}
          />
        )
        case ReminderContentTypeOptions.BEFORESTART: return (
          <ReminderMultiple
            weId={`${props.weId || ''}_3o1v4m`}
            prefixClassName={prefixClassName}
            id={config?.type}
            title={config?.title}
            supportCustomReminderType={config?.supportCustomReminderType}
            reminderFrequency={config?.reminderFrequency}
            isMobile={isMobile}
            onChange={onChange}
            value={value?.[config?.type]}
            reminderTypeAuth={reminderTypeAuth}
            defaultValue={defaultReminderValue}
            readOnly={readOnly || isPrint}
          />
        )
        case ReminderContentTypeOptions.BEFOREEND: return (
          <ReminderMultiple
            weId={`${props.weId || ''}_3o1v4m`} 
            prefixClassName={prefixClassName} 
            id={config?.type} 
            title={config?.title} 
            supportCustomReminderType={config?.supportCustomReminderType}
            reminderFrequency={config?.reminderFrequency}
            isMobile={isMobile} 
            onChange={onChange}
            value={value?.[config?.type]}
            reminderTypeAuth={reminderTypeAuth}
            defaultValue={defaultReminderValue}
            readOnly={readOnly || isPrint}
          />
        )
        default: return <></>
      }
    }, [props.weId, prefixClassName, isMobile, onChangeReminderType, value, reminderTypeAuth, readOnly, isPrint, isDesign, onChange, defaultReminderValue]);
    const isExcel = page?.layoutType === 'EXCEL';
    const wrapCls = classnames(prefixClassName, {
      hasDescribe: config?.describe,
      excelLayout: isExcel,
      [styleConfig?.textAlign]: styleConfig?.textAlign && isExcel,
    })
    if (loadingAuth) return null;
    const tip = getLabel('281934','请在后台管理中心 > 消息推送设置 > 基本设置 > 消息渠道设置界面，启用消息渠道');
    if (!(reminderTypeAuth?.enableEmail || reminderTypeAuth?.enableIM || reminderTypeAuth?.enableSMS)) return <div className={wrapCls}>
      <span className={`${prefixClassName}-reminder-tip`}>
        {tip}
      </span>
    </div>
    return (
      <div className={wrapCls}>
        {
          formConfig?.filter((f) => f.show)?.map((formconfig, formConfigIndex) => {
            return (
              <FormItem 
                key={formconfig?.id}
                weId={`${props.weId || ''}_bqg1m4@${config?.id}`}
                id={formconfig?.id}
                title={formconfig?.title}
                className={formconfig?.className}
                formConfig={config}
                isExcel={isExcel}
                isMobile={isMobile}
                hideDivider={formConfigIndex === 0}
                prefixClassName={prefixClassName}
              >
                <div className={`${prefixClassName}-reminder-mainContent`}>
                  {
                    viewType === 'distribute' ? (
                      <div className={`${prefixClassName}-distribute`}>{getLabel('281937','当前状态该字段不可用！')}</div>
                    ) : getContent(formconfig)
                  }
                </div>
              </FormItem>
            )
          })
        }
      </div>
    )
  }
));

export default MainContent;