@import "../../../../style/prefix.less";

.@{reminderMethodClsPrefix}-view-m-wrap,
.@{reminderMethodClsPrefix}-design-m-wrap {
  padding: 0;

  & > .weapp-form-widget-wrapper > .weapp-form-widget-title {
    display: none;
  }
  & > .weapp-form-widget-desc {
    display: none;
  }
}

.@{reminderMethodClsPrefix}-view-m-reminder-mainContent-container,
.@{reminderMethodClsPrefix}-design-m-reminder-mainContent-container {
  & > .weapp-form-widget.weapp-form-widget--hoz {
    padding: 0;
    & > .weapp-form-widget-title {
      justify-content: flex-start;
    }
  }
  .weapp-form-widget-content-container, .@{reminderMethodClsPrefix}-view-m-reminder-mainContent, .@{reminderMethodClsPrefix}-design-m-reminder-mainContent {
    height: 100%;
  }
  &.reminderImmediately {
    & > .weapp-form-widget.weapp-form-widget--hoz .weapp-form-widget-content-container > div {
      height: 100%;
    }
  }
}

.@{reminderMethodClsPrefix}-design-m, .@{reminderMethodClsPrefix}-view-m {
  width: 100%;
}

.@{reminderMethodClsPrefix}-design-m-divider, .@{reminderMethodClsPrefix}-view-m-divider {
  border-top: 1px solid var(--diviling-line-color);
}

.@{reminderMethodClsPrefix}-view-m-reminder-immediately, .@{reminderMethodClsPrefix}-design-m-reminder-immediately {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .ui-m-switch-btn {
    margin-right: 0;
  }
}
// excel布局
.@{reminderMethodClsPrefix}-view-m-wrap.weapp-form-widget__excel {
  .@{reminderMethodClsPrefix}-view-m-reminder-mainContent-container {
    .weapp-form-widget > .weapp-form-widget-title {
      align-items: center;
    }
    .@{reminderMethodClsPrefix}-view-m-reminder-immediately {
      min-height: calc(44 * var(--hd));
    }
    .@{reminderMethodClsPrefix}-view-m-reminder-type {
      .ui-m-select.is-multiple .ui-m-input-control.is-readonly > span {
        white-space: pre-wrap;
        word-break: break-word;
        overflow-wrap: break-word;
        line-height: 1.15;
      }
    }
    .@{reminderMethodClsPrefix}-view-m-reminder-multiple-tip {
      white-space: pre-wrap;
      word-break: break-word;
      overflow-wrap: break-word;
      line-height: 1.15;
    }
    .@{reminderMethodClsPrefix}-view-m-reminder-multiple .ui-formItem-item  {
      line-height: 1.15;
      min-height: calc(44* var(--hd));
      .ui-formItem-item-placeholder {
        padding: calc(2 * var(--hd)) 0;
      }
    }
  }
}
//外发
.@{reminderMethodClsPrefix}-view-m-distribute {
  font-size: var(--font-size-14);
  padding: calc(15 * var(--hd));
  color: var(--danger);
}