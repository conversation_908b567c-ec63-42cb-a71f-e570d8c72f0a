@import "../../../../style/prefix.less";

.@{reminderMethodClsPrefix}-view-wrap.weapp-form-field.weapp-form-widget,
.@{reminderMethodClsPrefix}-design-wrap.weapp-form-field.weapp-form-widget {
  padding: 0;
  display: block;

  & > .weapp-form-widget-title {
    display: none;
  }
}
.@{reminderMethodClsPrefix}-view-wrap .weapp-formbuilder-conflict-message {
  padding-left: var(--f-form-item-h-gap);
}

.@{reminderMethodClsPrefix}-view-reminder-mainContent-container,
.@{reminderMethodClsPrefix}-design-reminder-mainContent-container {
  .weapp-form-widget-content-container {
    min-height: calc(30 * var(--hd));
    display: flex;
    // align-items: flex-end;
    align-items: center;
  }
  .@{reminderMethodClsPrefix}-view-reminder-multiple,
  .@{reminderMethodClsPrefix}-design-reminder-multiple {
    align-items: flex-end;
  }
  .@{reminderMethodClsPrefix}-view-reminder-type,
  .@{reminderMethodClsPrefix}-design-reminder-type {
    min-height: calc(30 * var(--hd));
    display: flex;
    align-items: center;
    .ui-checkbox .ui-checkbox-wrapper {
      min-height: calc(30 * var(--hd));
    }
  }
}

.@{reminderMethodClsPrefix}-view-reminder-mainContent {
  min-height: calc(30 * var(--hd));
}

.@{reminderMethodClsPrefix}-view-reminder-immediately {
  min-height: calc(30 * var(--hd));
  display: inline-flex;
  align-items: center;
  padding: calc(5 * var(--hd)) 0;
}

// excel 布局

.@{reminderMethodClsPrefix}-view.excelLayout {

  .weapp-form-widget .weapp-form-widget-title {
    justify-content: flex-start;
    align-items: center;
    width: calc(150 * var(--hd));
    color: var(--main-fc);
  }
  .weapp-form-widget-content-container {
    align-items: center;
  }
  .weapp-form-widget .weapp-form-widget-desc {
    border-bottom: 0;
    margin-bottom: 0;
  }
  .@{reminderMethodClsPrefix}-view-reminder-mainContent-container-describe {
    border-bottom: calc(1 * var(--hd)) dashed var(--border-color);
    &.excel {
      padding-left: var(--h-spacing-md);
    }
  }
  &.center {
    .weapp-form-widget .weapp-form-widget-title, .weapp-form-widget .weapp-form-widget-content-container {
      justify-content: center;
    }
  }
  &.right {
    .weapp-form-widget .weapp-form-widget-title, .weapp-form-widget .weapp-form-widget-content-container {
      justify-content: right;
    }
  }
}

// 外发
.@{reminderMethodClsPrefix}-view-distribute {
  color: var(--danger);
  font-size: var(--font-size-12);
}

.@{reminderMethodClsPrefix}-design-reminder-tip, .@{reminderMethodClsPrefix}-view-reminder-tip{
  font-size: 12px;
  display: flex;
  justify-content: center;
  padding: var(--v-spacing-sm) 0;
  color: var(--danger);
}