import { forwardRef, memo, useCallback } from "react";
import { ReminderImmediatelyProps } from "../types";
import { Switch, MSwitch } from "@weapp/ui";

const ReminderImmediately = memo(forwardRef<{}, ReminderImmediatelyProps>(
  (props) => {
    const { prefixClassName, isMobile, onChange, value, dataId, readOnly } = props;
    
    const doChange = useCallback((value: any) => {
      onChange?.(dataId, value);
    }, [dataId, onChange]);

    return (
      <div className={`${prefixClassName}-reminder-immediately`}>
        {
          isMobile ? (
            <MSwitch weId={`${props.weId || ''}_u6y75d`} size="md" onChange={doChange} value={value} readOnly={readOnly} />
          ) : (
            <Switch weId={`${props.weId || ''}_v97njf`} onChange={doChange} value={value} readOnly={readOnly} />
          )
        }
      </div>
    )
  }
))

export default ReminderImmediately;