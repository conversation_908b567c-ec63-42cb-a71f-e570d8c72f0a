import React from "react";
import { ReminderMethodContextProps } from "../types";
import { AnyObj, utils } from "@weapp/ui";
const { getRandom } = utils;

export const ReminderMethodContext = React.createContext<ReminderMethodContextProps>({});

export const transValue = (key: string, _value: AnyObj, config?: AnyObj) => {
  let val = _value || {};
  const { reminderFrequency, defaultValueSingleEnable, defaultValueSingleType, defaultValueMultipleEnable, defaultValueMultipleType } = config || {};
  if (reminderFrequency === 'single' && defaultValueSingleEnable) {
    const { remindTimeSingle, remindTimeDaySingle, remindTimeDaySingle_desc, remindTimeHourSingle,
      remindTimeHourSingle_desc, remindTimeMinSingle, remindTimeMinSingle_desc } = config || {};
    val = {
      ...val,
      [key]: {
        enable: true,
        value: [
          {
            id: getRandom(),
            defaultValueSingleType,
            remindCategory: '0',
            remindTimeSingle,
            remindTimeDaySingle,
            remindTimeDaySingle_desc,
            remindTimeHourSingle,
            remindTimeHourSingle_desc,
            remindTimeMinSingle,
            remindTimeMinSingle_desc,
          }
        ],
      }
    }
  } else if (reminderFrequency === 'multiple' && defaultValueMultipleEnable) {
    val = {
      ...val,
      [key]: {
        ...defaultValueMultipleType,
        value: defaultValueMultipleType?.value?.map((v: any) => {
          // 默认值或者不开启支持自定义提醒方式的时候
          return {
            ...v,
            remindType: val?.reminderType,
            _reminderMethodDefaultValue: true,
          }
        })
      }
    }
  }
  return val;
}