import { CorsComponent } from '@weapp/ui';

const config = {
	groups: [
		{
			id: 'setting',
			title: '基础设置',
			visible: true,
			custom: false,
		},
	],
	items: {
		placement: {
			groupId: 'setting',
			label: '气泡框位置',
			labelSpan: 7,
			itemType: 'CUSTOM',
			customRender: (props: any, content: any) => {
				return (
					<CorsComponent weId={`${props.weId || ''}_7idko9`}
						app="@weapp/ebdcoms"
						compName="HelpPlacement"
						{...props}
					/>
				)
			},
		},
		title: {
			groupId: 'setting',
			label: '提示文字',
			labelSpan: 7,
			itemType: 'TEXTAREA',
		},
		hasArrow: {
			groupId: 'setting',
			label: '是否有箭头',
			labelSpan: 7,
			itemType: 'SWITCH',
			value: true,
		},
		isCenter: {
			groupId: 'setting',
			label: '内部文字是否居中',
			labelSpan: 7,
			itemType: 'SWITCH',
		},
	},
}

export default config;