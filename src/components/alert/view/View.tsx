import { PureComponent } from 'react'
import { ViewProps } from '@weapp/ebdcoms';
import { Alert } from '@weapp/ui';
import ebMiddleware from '../../../eb-middleware';

class AlertView extends PureComponent<ViewProps> {
  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: ViewProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {
    },
  };

  render() {
    const { className, config } = this.props;
    // className ebcoms 复写了alert样式，不可传递下去
    const type = config?.type as any;
    return (<Alert
      weId={`${this.props.weId || ''}_wnprwx`}
      {...config}
      type={type}
      // className={className}
    />)
  }
};

export default ebMiddleware('AlertView', 'View', 'Alert', AlertView);

