import { LABEL_SPAN, LABEL_SPAN_LG, uiPropsDesignClsPrefix } from "../../../constants";
import IconConfig from "../../../components-share/icon-config";
import { getLabel } from "@weapp/utils";

const config = {
  /** 是否使用标题配置 */
  title: true,
  /** 是否使用底部区域配置 */
  footer: false,
  /** 是否使用固定区域配置 */
  fixedArea: false,
  groups: [
    {
      id: 'config',
      title: getLabel('219086','内容设置'),
      visible: true,
      custom: false,
    },
  ],
  items: {
    alert_type: {
      itemType: 'SELECT',
      data: [
        { id: 'info', content: getLabel('219087','提示类型') },
        { id: 'success', content: getLabel('219088','成功类型') },
        { id: 'error', content: getLabel('219089','错误类型') },
        { id: 'warning', content: getLabel('219090','警告类型') },
      ],
    },
    message: {
      itemType: 'LOCALEEX',
    },
    description: {
      itemType: 'LOCALEEX',
      filedType: "TEXTAREA",
    },
    showIcon: {
      itemType: 'SWITCH',
      size: 'sm',
    },
    icon: {
      itemType: 'CUSTOM',
      customRender: (props: any) => (<IconConfig weId={`${props.weId || ''}_hcs94y`}
        {...props}
        dataKey="icon"
      />),
    },
    closable: {
      itemType: 'SWITCH',
      size: 'sm',
    },
    banner: {
      itemType: 'SWITCH',
      size: 'sm',
    },
  },
  /** 关联字段，如{a: ['b']}，每次a字段值发生改变时，强制渲染b字段组件 */
  references: {
    /** filteredCom值更新之后，触发comName渲染 */
    // showIcon: ['icon'],
  },
  layout: [
    [
      {
        id: 'alert_type',
        items: ['alert_type'],
        groupId: 'config',
        label: getLabel('219087','提示类型'),
        labelSpan: LABEL_SPAN,
      },
    ],
    [
      {
        id: 'message',
        items: ['message'],
        label: getLabel('219091','提示内容'),
        labelSpan: LABEL_SPAN,
        groupId: 'config',
      },
    ],
    [
      {
        id: 'description',
        items: ['description'],
        label: getLabel('219092','辅助性文字介绍'),
        labelSpan: 24,
        groupId: 'config',
      },
    ],
    [
      {
        id: 'showIcon',
        items: ['showIcon'],
        label: getLabel('219093','显示图标'),
        labelSpan: LABEL_SPAN_LG,
        groupId: 'config',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'icon',
        items: ['icon'],
        label: getLabel('219094','自定义图标'),
        labelSpan: LABEL_SPAN_LG,
        groupId: 'config',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'closable',
        items: ['closable'],
        label: getLabel('219095','显示关闭图标'),
        labelSpan: LABEL_SPAN_LG,
        groupId: 'config',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'banner',
        items: ['banner'],
        label: getLabel('219096','用作顶部公告'),
        labelSpan: LABEL_SPAN_LG,
        groupId: 'config',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
  ]
};

export default config;