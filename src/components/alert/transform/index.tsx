import IconConfig from "../../../components-share/icon-config";
import ebdcoms from "../../../utils/ebdcoms";
// import { getLocaleValue } from "../../../utils/transform";
// ui 转 EB组件 映射关系
export const alert_trans_config = {
  type: 'alert_type',
}

export const alert_trans_func = async (props: any) => {
  await ebdcoms.load();
  const { getLocaleValue } = ebdcoms.get();
  const { alert_type, message, description, icon, ...resProps } = props;
  let transConfig = {};

  if (icon?.path) {
    transConfig = {
      ...transConfig, icon: IconConfig.getIcon(icon)
    }
  }

  const config = {
    ...resProps,
    type: alert_type,
    message: getLocaleValue(message),
    description: getLocaleValue(description),
    ...transConfig,
  }
  return config;
}