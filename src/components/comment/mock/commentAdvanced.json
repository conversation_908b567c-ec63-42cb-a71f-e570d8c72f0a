{"msg": "", "status": true, "code": 200, "data": {"actionMsg": {"code": 0, "message": "成功"}, "page": {"pageNo": 2, "pageSize": 10, "result": [{"id": "3575224169782458815", "tenantKey": "tf288yacf3", "deleteType": 0, "commentor": {"id": "8513938760306875576", "name": "人员姓名", "employeeId": "8513938760306875576", "username": "人员姓名", "avatar": {"previewPath": "/api/file/preview?type=redirect&fileId=", "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=", "name": "人员姓名", "gender": "female"}, "loginCount": 0, "fansCount": 0, "followCount": 0, "blogCount": 0, "onlySuperior": 0, "secLevel": 0, "hasInvUser": false, "birthdayFlag": false, "classifyAdmin": false, "setting": false, "permanentlyDelete": false, "type": "inside", "extra": false, "markread": false, "tenantKey4Im": "tf288yacf3", "containRels": false, "moduleAdmin": false, "followAndSub": false, "sysAdmin": false}, "mock": 1, "targetId": "3573031474186044663", "targetName": "log", "content": "自己用建模做了一套crm但是不好用，业务部门要改，需要我们去做，具体约时间见面聊，我们在半导体行业有很多客户案例", "originContent": "自己用建模做了一套crm但是不好用，业务部门要改，需要我们去做，具体约时间见面聊，我们在半导体行业有很多客户案例", "addTime": 1739844186000, "module": "document", "privy": false, "longitude": "121.48053886", "latitude": "31.23592904", "address": "位置", "client": "评论来源", "userData": "[]", "external": false, "merge": false, "noCreateOattend": false, "replyImMsgFlag": false, "anonymous": false, "upvoteStatus": {"deleteType": 0, "upvoted": false, "downvoted": false, "upvote_count": 0, "downvote_count": 0, "upvoters": []}, "disableMessage": false, "hasMte": false, "isInclude": false, "hasRootId": false, "hasChild": false, "commentLogRight": 0, "editFlag": 0, "desensitization": false, "secondAuth": false, "rawContent": "自己用建模做了一套crm但是不好用，业务部门要改，需要我们去做，具体约时间见面聊，我们在半导体行业有很多客户案例", "flowType": 0, "showComment": true, "attachments": [], "edit": false, "forceDelete": false, "pushBlogMessage": true, "flowLog": false}, {"id": "3575224125243058810", "tenantKey": "tf288yacf3", "deleteType": 0, "commentor": {"id": "8513938760306875576", "name": "人员姓名", "employeeId": "8513938760306875576", "username": "人员姓名", "avatar": {"previewPath": "/api/file/preview?type=redirect&fileId=", "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=", "name": "人员姓名", "gender": "male"}, "loginCount": 0, "fansCount": 0, "followCount": 0, "blogCount": 0, "onlySuperior": 0, "secLevel": 0, "hasInvUser": false, "birthdayFlag": false, "classifyAdmin": false, "setting": false, "permanentlyDelete": false, "type": "inside", "extra": false, "markread": false, "tenantKey4Im": "tf288yacf3", "containRels": false, "moduleAdmin": false, "followAndSub": false, "sysAdmin": false}, "mock": 1, "targetId": "3573031474186044663", "targetName": "log", "content": "自己用建模做了一套crm但是不好用，业务部门要改，需要我们去做，具体约时间见面聊，我们在半导体行业有很多客户案例", "originContent": "自己用建模做了一套crm但是不好用，业务部门要改，需要我们去做，具体约时间见面聊，我们在半导体行业有很多客户案例", "addTime": "评论时间", "module": "document", "privy": false, "client": "评论来源", "userData": "[]", "external": false, "merge": false, "noCreateOattend": false, "replyImMsgFlag": false, "anonymous": false, "upvoteStatus": {"deleteType": 0, "upvoted": false, "downvoted": false, "upvote_count": 0, "downvote_count": 0, "upvoters": []}, "disableMessage": false, "hasMte": false, "isInclude": false, "hasRootId": false, "hasChild": false, "commentLogRight": 0, "editFlag": 0, "desensitization": false, "secondAuth": false, "rawContent": "自己用建模做了一套crm但是不好用，业务部门要改，需要我们去做，具体约时间见面聊，我们在半导体行业有很多客户案例", "flowType": 0, "showComment": true, "attachments": [], "edit": false, "forceDelete": false, "pushBlogMessage": true, "flowLog": false}], "totalCount": 2, "hasNext": true, "hasPre": true, "prePage": 1, "totalPages": 3, "nextPage": 3, "first": 11}, "recordsTotal": 0, "recordsFiltered": 0, "draw": 0, "version": 2, "data": [], "hasNext": false}}