import { ComConfigData, FormItems } from "@weapp/ebdcoms";
import { CommentConfigKeys } from "../types";
import { commentDefConfig } from "../constant/defConfig";

const getDefaultValue = (key: CommentConfigKeys) => {
  return commentDefConfig[key];
}

const createConfig = (config: ComConfigData) => {

  let { items = {} } = config;

  const _items: FormItems = {};
  Object.keys(items).forEach(key => {
    const item = items[key];
    const defalutValue = getDefaultValue(key);
    _items[key] = {
      ...item,
      defalutValue,
    }
  })

  config = {
    ...config,
    items: _items
  };

  return config;
}

export default createConfig;