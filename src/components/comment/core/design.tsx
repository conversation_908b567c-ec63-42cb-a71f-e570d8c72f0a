import React, { ReactNode } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { DesignOptions } from "@weapp/designer";
import { AnyObj } from '@weapp/ui';

export type IReactComponent<P = AnyObj> =
  React.FC<P> |
  React.ComponentType<P>;

export type IEBComponent<P extends AnyObj = AnyObj> = IReactComponent<P> & {
  defaultOpts?: DesignOptions;
  renderTitle?: (self: any, nextProps: P) => ReactNode;
}

export default function design<P extends AnyObj>(Component: IEBComponent<P>): any {
  class ComponentDesign extends React.PureComponent<any & RouteComponentProps> {
    static defaultOpts?: DesignOptions = Component.defaultOpts;

    static renderTitle? = Component.renderTitle;

    static defaultProps? = Component.defaultProps;

    /** 标题内有数量需要跑逻辑，所以暂时先通过不渲染dom方式去触发 */
    renderNoDom = () => {
      const { config } = this.props;
      if (!config.titleEnabled) {
        return <>{ComponentDesign.renderTitle?.(null, this.props)}</>;
      }
      return null;
    };

    render() {
      return (
        <>
          <Component weId={`${this.props.weId || ''}_0j2vt7`} { ...this.props as P }  />
          {this.renderNoDom()}
        </>
      );
    }
  }

  return ComponentDesign;
}
