import { getLabel } from "@weapp/utils";
import { commentDefProps, mCommentDefProps } from "../constant";
import { initActionConfig, initOptionConfig, initReplySettingConfig } from "../utils/initData";
import { CommentDesignProps, CommentOptionObj, CommentViewProps, CommonConfigData, FloorActionConfig, I18nContentType } from "../types";
import modules from "../constant/modules";
import { AnyObj, ReplyOption, utils } from "@weapp/ui";
import { getDisplayConfig, html2text } from "../utils";
import topic from "../mock/topic.json";
import { EventTo } from "../../../ebConfig/operation-btns/types";
import { ReplyActionConfigOption } from "../config/items/option-config/contants";
import { designHideOptions, sortOptions } from "../config/items/option-config";

class CommentConfig {

  props!: CommentViewProps | CommentDesignProps;
  isDesign: boolean = false;

  constructor(props: CommentViewProps | CommentDesignProps, isDesign: boolean = false) {
    this.props = props;
    this.isDesign = isDesign;
  }

  getTopicData = (data: ReplyOption) => {
    const { id, targetId, content, module } = data;

    const topicType = (topic as any)[module] || 113;

    if (!topicType) {
      return null;
    }

    const richExpReg = /^_weaverMte_(.*)/;
    const _content = content.replace(richExpReg, "$1");
    const commentValue = html2text(_content);
    const minfoObj = {
      type: 8, // 事项类型
      entityId: targetId, // 事项ID
      entityName: commentValue, // 事项名称
      module, // 事项模块标识
      imType: topicType // im中事项类型标识
    }
    return {
      name: commentValue,     // 创建的默认群名称 (非必填, 事项的时候为事项名字, 事项评论的时候为评论内容)
      mid: id,        // 事项id (必填)
      smid: targetId,       // 事项评论id (不是评论可以不填)
      type: topicType,        // 模块系统消息type (必填)
      minfo: JSON.stringify(minfoObj),      // 模块对应的跳转信息 (必填)
      module: module     // 事项类型 (必填)
    }
  }

  getTopicProps = () => {
    return {
      topicConfig: this.getTopicData
    }
  }

  getModuleInfo = () => {
    const { moduleValue } = this.props.config;
    const moduleObj = modules.find(item => item.id === moduleValue)! || {};
    const { module = "share", urlModule = "bcw" } = moduleObj;
    return {
      module,
      objectModule: urlModule,
    }
  }

  getAssociativeProps = () => {
    const { config } = this.props;
    const { enableModule } = config;
    const associativeProps: AnyObj = {};
    if (enableModule) {
      const keysStr = enableModule.map(item => item.id).join(",");
      associativeProps.combinationParams = {
        optionIds: keysStr,
      }
    }

    return { associativeProps };
  }

  getFloorActionConfig = (optionConfig: CommonConfigData[]) => {
    const { page, config } = this.props;
    const { client } = page || {};
    const {
      replyActionConfig,
      usePrivated,
      useRichText,
      usePhrase,
      usePosition,
      useSignature,
      useSignApprove,
      useAnonymous,
      allowAnonymousSetting,
      defaultAnonymousByCheck
    } = optionConfig.find(item => item.id === 'reply')?.settingData || {}
    const {
      usePrivated: _usePrivated,
      useRichText: _useRichText,
      useAnonymous: _useAnonymous,
      allowAnonymousSetting: _allowAnonymousSetting,
      defaultAnonymousByCheck: _defaultAnonymousByCheck,
    } = config
    const { configData = [], enableModule, optionVal } = replyActionConfig || {}
    const floorActionConfig: FloorActionConfig = {}
    if (optionVal === ReplyActionConfigOption.DIFF) { // 单独配置
      let _actionConfig = configData?.filter((item: any) => item.enable)?.map((item: any) => {
        const { customName, name, showName, enable, customMessage, id, ...resItem } = item;
        const { nameAlias: customNameAlias } = customName! || {};
        const { nameAlias: customMessageAlias } = customMessage! || {};
        const btnName = showName ? (customNameAlias || name) : "";
        const btnMessage = customMessageAlias || name;
        // 移动端content为按钮名称
        const content = client === "MOBILE" ? btnName : btnMessage;
        return {
          ...resItem,
          id: id.indexOf('split-line__') > -1 ? '|' : id,
          name: btnName,
          content,
          // hideTitle: !showName,
        }
      }) || [];
      // 从数组队首开始遍历，找到第一个非 '|' 索引
      let firstIndex = 0, lastIndex = _actionConfig.length - 1;
      while (firstIndex < _actionConfig.length && _actionConfig[firstIndex].id === '|') {
        firstIndex++;
      }
      // 从数组末尾开始遍历，找到第一个非 '|' 索引
      while (lastIndex >= firstIndex && _actionConfig[lastIndex].id === '|') {
        lastIndex--;
      }
      // 去除队首和末尾的 '|' 索引 数据
      if (firstIndex > 0 || lastIndex < _actionConfig.length - 1) {
        _actionConfig = _actionConfig.slice(firstIndex, lastIndex + 1);
      }

      floorActionConfig.actionConfig = _actionConfig

      const associativeProps: AnyObj = {};
      if (enableModule) {
        const keysStr = enableModule.map((item: any) => item.id).join(",");
        associativeProps.combinationParams = {
          optionIds: keysStr,
        }
      }
      if (client === "MOBILE") {
        floorActionConfig.usePhrase = usePhrase
        floorActionConfig.usePosition = usePosition
        floorActionConfig.useSignature = useSignature
        floorActionConfig.useSignApprove = useSignApprove
      }
      return {
        ...floorActionConfig,
        associativeProps,
        usePrivated,
        useRichText,
        useAnonymous,
        allowAnonymousSetting,
        defaultAnonymousByCheck
      }
    } else {
      return {
        usePrivated: _usePrivated || usePrivated,
        useRichText: _useRichText || useRichText,
        useAnonymous: _useAnonymous || useAnonymous,
        allowAnonymousSetting: _allowAnonymousSetting || allowAnonymousSetting,
        defaultAnonymousByCheck: _defaultAnonymousByCheck || defaultAnonymousByCheck,
      }
    }
  }

  getConfigInfo = () => {
    const { page, config } = this.props;
    const { client } = page || {};
    const {
      actionConfig = initActionConfig({ client }),
      optionConfig = initOptionConfig({ client }),
      replySetting = initReplySettingConfig({ client }),
    } = config;

    // actionConfig 格式处理
    let _actionConfig = actionConfig?.filter(item => item.enable)?.map(item => {
      const { customName, name, showName, enable, customMessage, id, ...resItem } = item;
      const { nameAlias: customNameAlias } = customName! || {};
      const { nameAlias: customMessageAlias } = customMessage! || {};
      const btnName = showName ? (customNameAlias || name) : "";
      const btnMessage = customMessageAlias || name;
      // 移动端content为按钮名称
      const content = client === "MOBILE" ? btnName : btnMessage;
      return {
        ...resItem,
        id: id.indexOf('split-line__') > -1 ? '|' : id,
        name: btnName,
        content,
        // hideTitle: !showName,
      }
    }) || [];

    // 从数组队首开始遍历，找到第一个非 '|' 索引
    let firstIndex = 0, lastIndex = _actionConfig.length - 1;
    while (firstIndex < _actionConfig.length && _actionConfig[firstIndex].id === '|') {
      firstIndex++;
    }
    // 从数组末尾开始遍历，找到第一个非 '|' 索引
    while (lastIndex >= firstIndex && _actionConfig[lastIndex].id === '|') {
      lastIndex--;
    }
    // 去除队首和末尾的 '|' 索引 数据
    if (firstIndex > 0 || lastIndex < _actionConfig.length - 1) {
      _actionConfig = _actionConfig.slice(firstIndex, lastIndex + 1);
    }

    // optionConfig 格式处理
    const _optionConfig: CommentOptionObj[] = []
    let childrenCommentOrderWay = ''
    optionConfig?.filter(item => {
      if (this.isDesign) { // 设计视图去掉【取消置顶】
        const { id } = item || {}
        if (designHideOptions.includes(id)) return false
      }
      return item.enable
    }).forEach(item => {
      const { customName, customMessage, name, showName, enable, id, iconName, settingData, ...resItem } = item;
      const _id = this.isDesign ? `${id}_design` : id;
      //const _id = id;
      const result = {
        ...resItem,
        id: _id,
        hideTitle: !showName,
      }
      if (sortOptions.includes(id)) {
        // 处理正序和倒序
        childrenCommentOrderWay = settingData[0]
        Array.isArray(customName) && customName?.forEach((item, index) => {
          const { nameAlias: customNameAlias } = item || {}
          const { nameAlias: customMessageAlias } = Array.isArray(customMessage) ? customMessage[index] || {} : customMessage || {}
          const id = settingData[index]
          const _id = this.isDesign ? `${id}_design` : id;
          //const _id = id;
          const resultObj = {
            ...result,
            id: _id,
            iconName: iconName[index],
            title: customNameAlias || name[index],
            titleInfo: customMessageAlias
          }
          // 设计视图，只展示正序、倒序之一
          if (!(this.isDesign && id === childrenCommentOrderWay)) {
            _optionConfig.push(resultObj)
          }
        })
      } else {
        const { nameAlias: customNameAlias } = customName || {};
        const { nameAlias: customMessageAlias } = customMessage! || {};
        _optionConfig.push({
          ...result,
          iconName: iconName,
          title: customNameAlias || name,
          titleInfo: customMessageAlias
        })
      }
    })
    // 单独处理【默认正序/倒序】(总是生效，不被开关过滤)
    const sortOption = optionConfig.find(item => item.id === 'sort')
    childrenCommentOrderWay = sortOption?.settingData?.[0] || ''

    // floorActionConfig 格式处理
    const floorActionConfig = this.getFloorActionConfig(optionConfig)

    // floorOptionConfig 格式处理
    const floorOptionConfig = replySetting?.options?.filter(item => item.enable)?.map(item => {
      const { customName, customMessage, name, showName, enable, id, ...resItem } = item;
      const { nameAlias: customNameAlias } = customName! || {};
      const { nameAlias: customMessageAlias } = customMessage! || {};
      const title = showName ? customNameAlias || name : "";
      const titleInfo = customMessageAlias;

      const _id = this.isDesign ? `${id}_design` : id;

      return {
        ...resItem,
        id: _id,
        title,
        titleInfo,
        hideTitle: !showName,
      }
    }) || [];

    return {
      actionConfig: _actionConfig,
      optionConfig: this.getOptionConfig(_optionConfig),
      floorActionConfig,
      floorOptionConfig,
      childrenCommentOrderWay
    }

  }

  getOptionConfig = (optionConfig: CommentOptionObj[]) => {
    return (data: ReplyOption) => {
      const { commentCount } = data
      if (!commentCount) { // 过滤排序icon
        return optionConfig.filter(item => {
          return ['asc', 'desc'].every(id => !item.id.includes(id))
        })
      }
      return optionConfig
    }
  }

  getSearch = () => {
    const { config } = this.props;
    const { topOperatBtns } = config!;
    const btns = topOperatBtns?.btns;
    const search = btns?.find((item: any) => item?.event?.to === EventTo.SEARCH);
    const sort = btns?.find((item: any) => item?.event?.to === EventTo.SORT);
    const echart = btns?.find((item: any) => item?.event?.to === EventTo.STATISTICS);

    return {
      hasCommentSearch: search?.isOpen,
      hasCommentSort: sort?.isOpen,
      hasEchart: echart?.isOpen,
      defaultSortBy: sort?.isOpen ? sort.defalutContent : undefined,
    }

  }

  getDisplaySettingConfig = () => {
    const { config, page } = this.props;
    return getDisplayConfig(config, page)
  }

  getCommentProps = () => {

    const { page, config } = this.props;
    const { client } = page || {};

    const {
      title,
      commentTitle,
      pageSize,
      moduleValue,
      hasEdit,
      printHideInput,
      printHorizontal,
      showTotal,
      usePosition,
      replyUnderComment,
      replyFloorExpand,
      showFloorExpand,
      replySetting,
      hoverShow,
      ...resProps
    } = config;

    // 处理分页
    let commentPageSize = parseInt(pageSize!, 10);
    commentPageSize = isNaN(commentPageSize) ? 10 : commentPageSize;
    const paginationProps = {
      pageSize: commentPageSize,
    }

    // module信息处理
    const moduleInfo = this.getModuleInfo();
    // actionConfig和optionConfig处理
    const configInfo = this.getConfigInfo();
    // 事项关联相关配置
    const associativeInfo = this.getAssociativeProps();
    // 显示设置相关配置
    const displayConfig = this.getDisplaySettingConfig();

    const defaultProps = {
      ...resProps,
      paginationProps,
      hasEdit: !printHideInput && hasEdit === "show",
      isHorizontal: printHorizontal,
      ...configInfo,
      ...moduleInfo,
      ...associativeInfo,
      ...this.getSearch(),
      ...this.getTopicProps(),
      layout: replyUnderComment ? 'floor' : 'content',
      key: replyUnderComment ? 'floor' : 'content',
      showFloorExpand,
      replyExpand: replyUnderComment && (!showFloorExpand || replyFloorExpand),
      // 引用，置顶功能需要
      uesAdvancedFeatures: true,
      // 引用内容省略
      quoteEllipsis: true,
      // 引用显示位置
      quotePosition: 'outer',
      // 显示设置
      ...displayConfig
    }

    // pc端评论
    const commentProps = {
      ...commentDefProps,
      ...defaultProps,
      alwaysShow: !hoverShow,
      floorAlwaysShow: !(replySetting?.hoverShowReplyOption ?? true), // 默认开启
    }
    // 移动端评论
    const mCommentProps = {
      ...mCommentDefProps,
      ...defaultProps,
      useAddress: usePosition,
    }

    return client === "PC" ? commentProps : mCommentProps;

  }

}

const getCommentProps = (props: CommentViewProps | CommentDesignProps, isDesign?: boolean): any => {
  const commentConfig = new CommentConfig(props, isDesign);

  return commentConfig.getCommentProps();
}

export default getCommentProps;