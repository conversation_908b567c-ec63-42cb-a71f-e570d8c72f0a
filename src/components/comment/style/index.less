@import "./mobile.less";
@import "./pc.less";

.@{commentClsPrefix} {

  &-design {
    // pointer-events: none;
    position: relative;

    &-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 566;
      pointer-events: all;
    }

    .ui-comment-item-footer-btns {
      & > * {
        position: relative;
        z-index: 999;
      }
    }

    .active_area {
      border: 1px solid var(--primary) !important;
    }

    .ui-comment-option,
    .ui-m-comment-item-footer-btns,
    .@{commentClsPrefix}-custom-form {
      pointer-events: none;
    }
  }

  &-view {
    overflow: visible;
  }

  &-config {
    &-icon {
      text-align: right;
    }
    &-select {
      width: 130px;
    }
    &-setting {
      width: 100%;

      &-footer {
        display: flex;
        align-items: center;

        &-btn {
          flex: none;
        }
      }

      &-help {
        margin-left: 5px;
      }

      &-description {
        flex: auto;
        font-size: var(--font-size-12);
        color: var(--secondary-fc);
        text-align: left;
        padding-right: 16px;

        .ui-icon {
          line-height: 1;
        }
      }

      &-btn {
        width: 100%;
      }

      &-names, &-icons {
        position: relative;
        &-split {
          border-left: 1px solid var(--secondary-fc);;
          height: 16px;
          width: 1px;
          position: relative;
        }
      }

      &-names:last-child &-names-split, &-icons:last-child &-icons-split {
          display: none;
      }

      &-icon, &-icons {
        color: var(--secondary-fc);
      }

      &-locale {
        .ui-locale {
          margin-bottom: 5px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      &-check {
      }

      &-operation {
        display: flex;
        align-items: center;
        height: 100%;

        .ui-select {
          width: 100%;
        }
      }

      &-content {
        margin-top: calc(-0.5 * var(--v-spacing-lg));
        &-split {
          display: flex;
          padding-bottom: calc(0.5 * var(--v-spacing-lg));
          justify-content: flex-end;
          color: var(--primary);
          .ui-icon {
            cursor: pointer;
          }
        }

        &-table-split-row {
          td {
            &:not(:first-child):not(:last-child) > span {
              &::before {
                margin-bottom: 4px;
                content: "";
                position: absolute;
                top: 50%;
                left: 1px;
                transform: translateY(-50%);
                width: calc(100% - 1px);
                height: 1px;
                border-top: 1px dashed var(--border-color);
              }

              & > div {
                display: none;
              }
            }

            &:nth-child(2) > span {
              &::before {
                right: 0 !important;
                width: calc(100% - 12px) !important;
                left: inherit !important;
              }
            }
          }
        }

        .ui-table-grid-tr[data-id='sort'] {
          .ui-table-grid-td {
            vertical-align: middle;
            .@{commentClsPrefix}-config-setting-names {
              width: 2em;
              &-split {
                left: 50%;
                transform: translateX(-50%);
              }
            }
            .@{commentClsPrefix}-config-setting-icons {
              width: 20px;
              &-split {
                left: 50%;
                transform: translateX(-50%);
              }
            }
          }
        }

        &-top-area {
          display: flex;
          justify-content: space-between;
          font-size: var(--font-size-12);
          &-title {
            margin-top: 2px;
            margin-bottom: 8px;
          }
        }
      }
    }

    &-radio-group {
      margin-top: 8px;
      margin-bottom: 5px;
      margin-right: 0;
      width: 100%;

      .ui-radio-label {
        padding-top: 1px;
        width: calc(100% - 14px);

        &-span {
          white-space: normal;
        }
      }

      &:first-child {
        margin-top: 5px;
        padding-top: 3px;
      }
    }

    &-switch {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row-reverse;
      width: 100%;
    }

    &-custom-body-extra {
      font-size: var(--font-size-12);
      background-color: var(--base-white);
      padding: 8px 16px;
      display: flex;
      align-items: center;
      border: 1px solid var(--border-color);
      & > span {
        margin-right: 32px;
      }
    }

    &-option-config, &-reply-setting-config {
      .@{commentClsPrefix}-config-setting-content {
        margin-top: calc(0.5* var(--v-spacing-lg));
      }
    }
  }

  &-header {
    display: flex;
    color: var(--regular-fc);
    align-items: center;
    padding-right: 16px;

    &-active {
      color: var(--primary);
    }

    .ui-icon {
      cursor: pointer;

      & +.ui-icon {
        margin-left: var(--comment-margin);
      }
    }
  }

  &-title {
    display: flex;
    width: 100%;

    &-content {
      flex: auto;
    }
  }

  &-title &-header {
    padding-right: 0;
  }

  &-echarts {

    &-header {
      text-align: center;
      padding: var(--comment-padding) 0;
    }

    &-types {
      padding-bottom: var(--comment-padding);
      display: flex;
      justify-content: center;

      .ui-menu {
        width: 180px;
        .ui-menu-tab-top .ui-menu-tab-top-container {
          display: flex;
          justify-content: center;
        }
      }
    }

    &-date {

      .ui-radio-wrapper-checked {

        .ui-radio-label {          
          color: var(--primary);
        }

        .ui-tag {
          border-color: var(--primary);
        }
      }

      .ui-radio {
        display: inline-block;
        vertical-align: text-bottom;
      }
      
      .ui-date-picker-rangePickerWrapper {
        display: inline-flex;
      }
    }
  }

  &-top-button {
    width: 100% !important;
    display: flex;
    padding-right: 24px;
    margin-top: 0 !important;
    padding-top: 5px;
    overflow-x: auto;

    &-select {
      .ui-input-wrap {
        border: var(--border-solid) !important;
      }

      .ui-input-suffix {
        display: flex !important;
      }
    }

    &-container {
      display: flex;
      width: 100% !important;

      .split-line {
        padding: 0 3px;
        margin-top: -5px;
        font-weight: 700;
      }
    }

    & &-container {

      div.ui-locale {
        display: flex;
        width: 100%;
      }
    }

    &-icon {

      &&-hasVal {
        border: 0;
      }
    }
  }

  &-design-floor-item-name {
    color: var(--primary);
    &:last-child {
      padding-right: 5px;
    }
  }

  &-design {
    .ui-list-footer {
      pointer-events: none;
    }
  }

  &-pc-advanced {
    .ui-comment-item .ui-comment-body {
      padding-left: 0;
    }
  }

  &-list-card {
    font-size: var(--font-size-12);

    &:not(:last-child) {
      border-bottom: var(--border-width) dashed var(--border-color);
    }

    .ui-pagination {
      text-align: right;
    }

    .ebcoms-nlist-card-layout-wrap {
      display: flex;
    }

    .ebcoms-nlist-card-layout {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      overflow: hidden;
    }

    .ebcoms-nlist-card-layout.fixed {
      flex-grow: 0;
      flex-shrink: 0;
    }

    .ebcoms-nlist-card-layout-row {
      display: flex;
      width: 100%;
      position: relative;
      line-height: 1.2;

      &-main {
        flex-wrap: wrap;
        overflow: hidden;
        display: flex;
        width: 100%;
        position: relative;
        line-height: 1.2;
      }

      &-cell {
        display: flex;
      }

      &:not(:last-child) {
        margin-bottom: calc(3 * var(--hd));
      }
    }

    .@{commentClsPrefix}-hrm-span,
    .ebcoms-nlist-card-layout-row-cell.hasEventActions {
      color: var(--primary);
      cursor: pointer;
    }
  }

  &-custom-cell {
    line-height: normal;
    font-size: inherit;
    width: 100%;

    &-fullwidth {
      width: 100%;
    }

    .ui-comment-item-footer-btns,
    .ui-comment-upvote-children {
      display: flex;
      align-items: center;
    }

    .ui-comment-item-header {
      padding-bottom: 0;
    }

    > .ui-comment-item-content,
    > .ui-comment-address {
      margin-bottom: 0;
    }

    .ui-comment-avatar-container {
      display: flex;
    }

    .ui-comment-item-content,
    .ui-comment-item-text,
    .ui-comment-item-header,
    //.ui-comment-link,
    .ui-comment-reply-top,
    .ui-comment-action,
    .ui-comment-action-name,
    .ui-comment-task-top,
    //.ui-comment-task-user,
    .ui-comment-upvote .ui-comment-upvote-icon {
      color: inherit;
    }

    .ui-comment-item-footer-info:empty {
      display: none;
    }

    .ui-comment-item-footer-btns {
      flex-wrap: wrap;
      justify-content: flex-end;
    }

    .ui-comment-item-footer-set {
      font-size: inherit;
    }

    .ui-comment-task-top {
      white-space: normal;
    }

    .ui-comment-item-text-container {
      margin-bottom: 0;

      &-privy {
        padding-top: calc(4 * var(--hd));
      }
    }

    .ui-comment-floor {
      margin-top: 0;

      .ui-comment-item-content {
        margin-bottom: calc(10 * var(--hd));
      }
    }
  }

  &-custom-form {
    margin-top: calc(5 * var(--hd));
    padding: calc(10 * var(--hd));
    border: var(--border-solid);
    border-radius: var(--input-border-radius);

    .weapp-form-widget.@{commentClsPrefix}-custom-formitem {
      overflow: auto;

      > .weapp-form-widget-title:first-child {
          justify-content: flex-start;
      }
    }
  }

  &-hrm-span {
    font-size: var(--font-size-12);
    height: calc(18 * var(--hd));
    line-height: calc(18 * var(--hd));
    height: auto;
    word-break: break-word;
  }
}

.@{commentClsPrefix}-config-replyundercomment-dialog {
  font-size: var(--font-size-14);
  .content {
    width: 100%;
    .top {
      margin-bottom: 16px;
    }
    .bottom {
      display: flex;
      justify-content: space-between;
      background-color: #fff;
      padding: 16px;
      .item {
        width: 50%;
        .desc {
          margin-left: 24px;
        }
        img {
          width: 100%;
        }
      }
    }
  }
}

.@{commentClsPrefix}-optionconfig-topsetting {
  &-form-share-icon {
    color: var(--regular-fc);
    cursor: pointer;
  }
}

.@{commentClsPrefix}-display-setting {
  &-template-input {
    cursor: pointer;
    border: none !important;
  }

  &-display-content {
    .ui-tag {
      cursor: pointer;
      width: calc(100 * var(--hd));
      margin-right: calc(10 * var(--hd));
      margin-bottom: calc(5 * var(--hd));
    }
  }

  &-form {
    .@{commentClsPrefix}-display-setting-subtitle {
      margin-top: 0;
    }
  }

  &-subtitle {
    font-size: var(--font-size-12);
    font-weight: 650;
    display: inline-block;
    margin-bottom: calc(10 * var(--hd));
    margin-top: calc(10 * var(--hd));
  }

  &-optionConfig {
    width: calc(150 * var(--hd));
  }

  &-comment-template {
    border-top: none !important;
    background: var(--base-white);
    pointer-events: none;

    .ui-comment-leftbar-line-tail {
      height: calc(100% + 100 * var(--hd)) !important;
    }
  
    .ui-list-footer {
      display: none;
    }

    &-customfield {
      background: #F2F4F9;
      border-radius: calc(3 * var(--hd));
      padding: calc(8 * var(--hd)) calc(10 * var(--hd));
    }

    &-department {
      margin-right: calc(5 * var(--hd));
      margin-bottom: calc(5 * var(--hd));
    }
  }

  &-m-comment-template {
    display: flex;
    align-items: center;
    background: var(--base-white);

    .ui-m-list-body {
      background-color: #f7f7f7;
    }

    &-department {
      margin-left: calc(5 * var(--hd));
    }

    &-department-below {
      color: var(--secondary-fc);
      margin-top: calc(5 * var(--hd));
    }

    &-below {
      .ui-m-comment-item-header {
        padding-bottom: calc(5 * var(--hd));
      }
    }
  }

  .ui-m-comment-menu&-m-comment-template {
    height: calc(420 * var(--hd)) !important;
    width: calc(400 * var(--hd));
    margin: 0 auto;
    pointer-events: none;

    .ui-m-comment-menu-header {
      display: none;
    }

    .ui-m-comment-item-footer-wrap {
      display: flex;
      flex-direction: column;

      .ui-m-comment-item-footer-info:not(:empty) {
        margin-bottom: calc(8 * var(--hd));
      }

      .ui-m-comment-item-footer-btns {
        display: flex;
        justify-content: space-between;

        .ui-icon-svg {
          height: calc(22 * var(--hd));
          width: calc(22 * var(--hd));
        }
      }
    }
  }

  &-preview {
    .@{commentClsPrefix}-display-setting-m-comment-template {
      background: none;
      height: auto !important;
    }

    .@{commentClsPrefix}-list-card {
      background: var(--base-white);
      pointer-events: none;
      
      .ui-m-comment-item-footer-btns,
      .ui-m-comment-item-footer-wrap {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .ui-icon-svg {
          height: calc(22 * var(--hd));
          width: calc(22 * var(--hd));
        }
      }

      .ui-m-comment-item-footer-info:empty {
        display: none;
      }
    }

    &-hide {
      display: none;
    }

    .ui-list-footer {
      display: none;
    }

    &-advanced {
      background: var(--base-white);

      .ui-comment-body {
        padding-left: 0;
      }
    }
  }
}

.@{commentClsPrefix}-display-setting-template-dialog {
  .ui-dialog-body {
    padding: 0;
    padding-bottom: var(--comment-padding);

    .ui-radio {
      width: 100%;

      .@{commentClsPrefix}-display-setting-template-card {
        width: 47%;
        margin-left: 2%;
        margin-top: 2%;
        background: var(--base-white);
        cursor: pointer;
        overflow: hidden;
        border: var(--border-width) solid #e5e5e5;
        position: relative;

        &-mask {
          position: absolute;
          width: 100%;
          height: 100%;
          z-index: 99;
        }

        &-active {
          border: var(--border-width) solid var(--primary);
        }

        .ui-comment-item {
          height: calc(140 * var(--hd));
        }
      }
    }

    .ui-radio-wrapper {
      margin-top: var(--comment-padding);
      margin-left: var(--comment-padding);
    }
  }
}

.@{commentClsPrefix}-config-customfields-dialog {
  .ui-dialog-body {
    height: 100%;
  }
}

.@{commentClsPrefix}-event {
  &-action {
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
  }

  &-refresh-over {
    .ui-help {
      margin-left: calc(20 * var(--hd));
    }
  }
}

.@{commentClsPrefix}-config-dataset {
  width: 100%;
  display: flex;
  align-items: center;

  .ebcomponents-dataset-view-wrap {
    flex-shrink: 0;
    flex-grow: 1;
    margin-right: calc(10 * var(--hd));
  }

  .ui-icon {
    color: var(--secondary-fc);
    cursor: pointer;
  }
}