.@{commentClsPrefix}-pc {
  border: none;

  .ui-comment-item-header {
    display: flex;
    align-items: flex-start;
    
    .ui-comment-username {
      flex: none;
    }
  }

  &-blog {
    display: flex;
    align-items: center;

    &-info {
      display: flex;
      align-items: center;

      & > .ui-icon {
        line-height: 1;
        margin-right: 5px;
      }
    }

    .ui-checkbox-label {
      line-height: 1;
    }
  }

  &-empty {
    padding: calc(20 * var(--hd)) 0 calc(50 * var(--hd));
  }

  &-header {
    
    &-info {
      display: inline-flex;
      align-items: center;
      flex: auto;
      justify-content: space-around;
    }

    &-left {
      display: flex;
      flex: auto;
    }

    &-right {
      display: flex;
      flex: auto;
      justify-content: flex-end;
    }
  }

  &-container {
    &-part {
      .ui-list-body[data-id="eb-comment-list"] {
        border-bottom: var(--border-width) dashed var(--border-color);
      }
      .ui-comment-list-body {
        & > .ui-pagination {
          margin: 0;
          height: 40px;
          padding: 9px var(--comment-padding);
        }
      }
    }

    &-input-only {
      .ui-comment-list {
        display: none;
      }
    }

    &-list-only {
      .ui-comment-edit {
        display: none;
      }
      .ui-comment-list:not(:first-child):not(.ui-comment-list-nodata) {
        border-top: transparent;
      }
    }
  }

  &-design {
    .ui-comment-item-footer-btns {
      & > * {
        opacity: 1;
      }
    }
  }
}