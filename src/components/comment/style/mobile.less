.@{commentClsPrefix}-m {
  //border: 1px solid var(--border-color);

  .ui-m-comment-item-header {
    display: flex;
    align-items: flex-start;
    
    .ui-m-comment-username {
      flex: none;
    }
  }
  
  &-edit {
    height: 76px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    border-bottom: var(--border-width) dashed var(--border-color);

    &-avatar {
      padding-right: 9px;
      flex: none;
    }

    &-body {
      font-size: var(--font-size-12);
      color: var(--secondary-fc);
      flex: auto;
    }

    &-text {
      height: 30px;
      display: flex;
      align-items: center;
      border: var(--border-solid);
      border-radius: 3px;
      padding: 0 8px;
    }

    &-active {
      border: 1px solid var(--primary);
    }
  }

  &-add {
    background: var(--label-bc);
    height: 100%;

    &-input-only {
      background-color: var(--base-white);
    }
  }

  &-blog {
    background: var(--base-white);
    padding: 10px var(--h-spacing-lg) 6px var(--h-spacing-lg);
    margin-top: 10px;

    &-info {
      display: flex;
      align-items: center;

      & > .ui-icon {
        line-height: 1;
        margin-right: 5px;
      }
    }

    .ui-checkbox-label {
      color: var(--secondary-fc);
      line-height: 1;
    }
  }

  &-header {
    
    &-info {
      display: inline-flex;
      align-items: center;
      flex: auto;
      justify-content: space-around;
    }

    &-left {
      display: flex;
      flex: auto;
    }

    &-right {
      display: flex;
      flex: auto;
      justify-content: flex-end;
    }
  }

  &-container {
    &-input-only {
      .weapp-ui-props-design-comment-m-edit {
        border-bottom: transparent;
      }
      .ui-m-comment-content {
        display: none;
      }
    }

    &-list-only {
      .weapp-ui-props-design-comment-m-edit {
        display: none;
      }
    }
  }
  .ui-m-comment-item-footer-wrap {
    display: flex;
    flex-direction: column;
  }

  .ui-m-comment-item-footer-info {
    margin-bottom: 9px;
  }

  .ui-m-comment-item-footer-info:empty {
    display: none;
  }

  .ui-m-comment-item-footer-btns {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .ui-icon-svg {
      height: 22px;
      width: 22px;
    }

    .ui-icon {
      line-height: 1;
    }

    .ui-m-comment-iconbtn {
      display: flex;
      align-items: center;
      margin-left: 0;
    }

    .ui-m-comment-iconbtn-title {
      margin-left: 2px;
    }

  }

  &-advanced-template {
    height: auto !important;
    width: calc(400 * var(--hd));
    margin: 0 auto;

    & > .ui-m-comment-menu-header {
      display: none;
    }
  }

  &-target-title {
    padding: var(--h-spacing-md);
    font-size: var(--font-size-14);
    color: #666;
    height: calc(var(--hd) * 40);
    display: flex;
    align-items: center;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      height: var(--hd);
      width: 100%;
      background-color: var(--border-color);
      -webkit-transform: scaleY(.3);
      transform: scaleY(.3);
      pointer-events: none;
    }
  }

  &-target-browser {
    height: calc(100% - var(--hd) * 40);

    & > div {
      height: 100%;
    }
  }

  &-custom-form {
    border-radius: calc(var(--hd) * 3);
    overflow: hidden;

    .weapp-form-widget-m.@{commentClsPrefix}-custom-formitem {
      background: #F2F4F9;
    }

    .ui-m-list-item {
      justify-content: flex-end;

      >:not(.ui-m-list-content) {
        justify-content: flex-end;
      }
    }

    .@{commentClsPrefix}-custom-formitem.weapp-form-widget-m {
      > .weapp-form-widget-title:first-child {
        background-color: transparent;
      }
    }
  }

  &-custom-form-edit {
    .weapp-form-widget-m.@{commentClsPrefix}-custom-formitem {
      background: var(--base-white);
    }
  }

  .ebcoms-nlist-card-layout-row, .ebcoms-nlist-card-layout-row-main {
    display: flex;
    width: 100%;
    position: relative;
    line-height: 1.2;
  }

  .ebcoms-nlist-card-layout-row-cell {
    display: flex;
  }

  &-hrm-span {
    margin: 0 calc(5 * var(--hd));
    line-height: calc(18 * var(--hd));
    overflow: auto;
    word-break: break-word;

    &-below {
      margin-top: calc(5 * var(--hd));
      color: var(--secondary-fc);
      overflow: auto;
      word-break: break-word;
    }
  }
}

.weapp-ebde-mgl-card-item-handle {
  .@{ebcomClsPrefix} {
    &.@{ebcomCommentClsPrefix} {
      & > .content {
        padding: 0;
      }
    }
  }
}

.@{ebcomClsPrefix}-m {
  &.@{ebcomCommentClsPrefix} {
    & > .content {
      padding: 0;
    }
  }
}

.@{commentClsPrefix}-list-card.@{commentClsPrefix}-m-list-card {
  font-size: var(--font-size-14);
}

.@{commentClsPrefix}-m,
.@{commentClsPrefix}-m-advanced-template {
  .@{commentClsPrefix}-list-card {
    .ui-m-comment-item-footer-info:empty {
      display: none;
    }

    .@{commentClsPrefix}-custom-cell {
      > .ui-m-comment-item-content {
        margin-bottom: 0;
      }

      .ui-m-comment-item-header {
        padding: 0;
        align-items: center;

        .ui-m-comment-item-privy {
          top: 0;
        }
      }
    }

    .ui-m-comment-item-text,
    .ui-m-comment-item-content,
    .ui-m-comment-item-header,
    .ui-m-comment-item-privy,
    .ui-m-comment-quote,
    .ui-m-comment-floor,
    .@{commentClsPrefix}-hrm-span {
      font-size: inherit;
      color: inherit;
    }

    .ui-m-comment-username {
      font-size: inherit;
    }
  }
}