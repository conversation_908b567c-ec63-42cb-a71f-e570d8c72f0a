import { FC, useCallback, useState } from "react";
import { CommentConfigProps } from "../../types";
import { Button, Dialog, Help, Switch } from "@weapp/ui";
import { commentClsPrefix } from "../../../../constants";
import { getLabel } from "@weapp/utils";
import replyMode1 from '../../statics/images/replyMode1.png'
import replyMode2 from '../../statics/images/replyMode2.png'

interface ReplyUnderCommentProps extends CommentConfigProps<any> {

}

const ReplyUnderComment: FC<ReplyUnderCommentProps> = (props) => {

    const { value, className, onChange } = props;
    const [helpVisible, setHelpVisible] = useState(false)

    const handleChange = useCallback((value: boolean) => {
        onChange(value)
    }, [onChange])

    const handleHelpClick = useCallback(() => {
        setHelpVisible(!helpVisible)
    }, [])

    const closeDialog = useCallback(() => {
        setHelpVisible(false)
    }, [])

    return (
        <div className={`${commentClsPrefix}-config-switch`}>
            <Switch
                weId={`${props.weId || ''}_fe9971`}
                value={value}
                className={className}
                onChange={handleChange}
            />
            <div onClick={handleHelpClick}>
                <Help
                    weId={`${props.weId || ''}_pyvf0e`}
                    popoverProps={{ visible: false }}
                />
            </div>
            <Dialog weId={`${props.weId || ''}_oez1z6`}
                visible={helpVisible}
                width={700}
                closable
                title={getLabel('260937', '说明')}
                icon={"Icon-e-builder"}
                onClose={closeDialog}
                className={`${commentClsPrefix}-config-replyundercomment-dialog`}
                footer={[
                    <Button weId={`${props.weId || ''}_f793w1@${0}`} onClick={closeDialog}>{getLabel('265379', '关闭')}</Button>
                ]}
            >
                <div className='content'>
                    <div className="top">{getLabel('260942', '评论的显示支持两种模式：')}</div>
                    <div className="bottom">
                        <div className="item">
                            <div className="desc">{getLabel('260943', '1、默认：回复均以新评论的方式展示')}</div>
                            <div className="image">
                                <img src={replyMode1} />
                            </div>
                        </div>
                        <div className="item">
                            <div className="desc">{getLabel('260944', '2、回复显示在评论下方')}</div>
                            <div className="image">
                                <img src={replyMode2} />
                            </div>
                        </div>
                    </div>
                </div>
            </Dialog>
        </div>
    )
}

export default ReplyUnderComment;