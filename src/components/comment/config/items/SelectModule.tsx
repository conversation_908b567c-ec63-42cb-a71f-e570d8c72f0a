import { FC, useCallback } from "react";
import { CommentConfigProps, ModuleKey } from "../../types";
import { Select, SelectValueType } from "@weapp/ui";
import modules from "../../constant/modules";

interface SelectModuleProps extends CommentConfigProps<ModuleKey> {

}

const SelectModule: FC<SelectModuleProps> = (props) => {

  const { value, className, onChange } = props;

  const handleChange = useCallback((value: SelectValueType, option: any) => {
    onChange(value as ModuleKey)
  }, [onChange])

  return (
    <Select
      weId={`${props.weId || ''}_3r49zp`}
      value={value}
      className={className}
      // @ts-ignore
      data={modules}
      onChange={handleChange}
    />
  )
}

export default SelectModule;