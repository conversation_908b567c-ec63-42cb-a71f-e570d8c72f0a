import { commentClsPrefix } from '../../../../constants';
import { FC, memo, useCallback, useMemo, useState } from 'react';
import { Select, SelectOptionData, Input, Layout } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { CommentConfigProps } from '../../types';

interface RefreshTypeProps extends CommentConfigProps<any> {
  weId: string
  value: string
}

const { Row, Col } = Layout;
const { InputNumber } = Input;
const prefixCls = commentClsPrefix;

const data: SelectOptionData[] = [{
  id: "noRefresh",
  content: getLabel("244066", "不刷新"),
}, {
  id: "5_second",
  content: `5${getLabel('59672', '秒')}`,
}, {
  id: "10_second",
  content: `10${getLabel('59672', '秒')}`,
}, {
  id: "30_second",
  content: `30${getLabel('59672', '秒')}`,
}, {
  id: "1_minute",
  content: `1${getLabel("236802", "分钟")}`,
}, {
  id: "3_minute",
  content: `3${getLabel("236802", "分钟")}`,
}, {
  id: "custom_default",
  content: `5${getLabel("236802", "分钟")}`,
}] as SelectOptionData[]

const RefreshType: FC<RefreshTypeProps> = memo((props) => {
  const { weId, onChange, className, value } = props
  const [customMinValue, setCustomMinValue] = useState(5)
  const [selectData, setSelectData] = useState(data)

  const handleChange = useCallback((value: any) => {
    onChange?.(value);
  }, [onChange])

  const onCustomMinValueChange = useCallback((value: any) => {
    const newData = data.map(i => {
      if (i.id.indexOf('custom_') > -1) {
        return { id: `custom_${value}`, content: `${value}${getLabel("236802", "分钟")}`}
      }

      return i
    })
    setSelectData(newData)
    setCustomMinValue(value)
    onChange?.(`custom_${value}`)
  }, [data, setSelectData, setCustomMinValue, onChange])

  const customOptionRender = useCallback((option: SelectOptionData) => {
    const { content, id } = option;
    if (id.indexOf('custom_') > -1) {
      return (
        <Row weId={`${weId || ''}_4wl7en`} style={{ width: '100%', alignItems: 'center' }}>
          <Col weId={`${weId || ''}_nlcv9e`} span={10}>
            <span>{getLabel('11662','自定义')}</span>
          </Col>
          <Col weId={`${weId || ''}_2uh8pj`} span={14}>
            <InputNumber
              weId={`${weId || ''}_ero4ei`}
              suffix={<span>{getLabel("236802", "分钟")}</span>}
              value={customMinValue}
              min={1}
              onChange={onCustomMinValueChange}
            />
          </Col>
        </Row>
      )
    }

    return <span>{content}</span>
  }, [customMinValue, onCustomMinValueChange])

  const cls = classnames(`${prefixCls}-config-pagination-type`, className);

  return (
    <Select
      weId={`${weId || ''}_r5xl3r`}
      value={value || 'noRefresh'}
      onChange={handleChange}
      className={cls}
      data={selectData}
      customOptionRender={customOptionRender}
      dropdownStyle={{ minWidth: '180px' }}
    />
  )
})

export default RefreshType;