import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CommentConfigProps } from "../../types";
import { AnyObj, CorsComponent } from '@weapp/ui';
import { getCommentFormFieldsMemo, getFields } from '../../utils/getApiDatas';
import { toJS } from 'mobx';
import { handleFormatSourceFields } from '../../utils';
import { fieldSelectOption } from '../../api/data';
import { CommentEvents, ebuilderformGroupId } from '../../constant';

interface FilterConditionProps extends CommentConfigProps<any> {

}

const FilterCondition: FC<FilterConditionProps> = (props) => {
    const { value, config, com, events, onChange, onConfigChange } = props;

    const { useOriginalTableFields, relatedDataSource, itemFormId } = config

    const [sourceFields, setSourceFields] = useState<Array<AnyObj>>([])

    const [commentSourceFields, setCommentSourceFields] = useState<Array<AnyObj>>([])

    const [commentFormData, setCommentFormData] = useState<any>(null);

    useEffect(() => {
        events?.on(CommentEvents.COMMENT_UPDATE_COMMENTFORM, com?.id as any, updateCommentForm);
        return () => {
            //events?.off(CommentEvents.COMMENT_UPDATE_COMMENTFORM, com?.id as any);
        }
    }, [])

    const updateCommentForm = (commentForm: any) => {
        setCommentFormData(commentForm)
    }

    const handleCommentForm = useCallback(async () => {
        // 如果已经缓存，直接返回格式化后的值
        if (commentFormData) {
            const { commentFormObjId } = commentFormData || {}
            const dataset = {
                ...relatedDataSource,
                id: commentFormObjId
            }
            const sourceFields = await handleCommentFields(dataset)
            const formFieldsObj = handleFormatSourceFields(sourceFields);
            return formFieldsObj;
        }

        // 否则发请求
        if (!itemFormId) return {};
        const res = await getCommentFormFieldsMemo(itemFormId, relatedDataSource?.dataExtMemoTime);
        onConfigChange?.({ commentForm: res.data }, { skipLayoutCompare: true })
        const { commentFormObjId } = res.data || {};
        const dataset = {
            ...relatedDataSource,
            id: commentFormObjId
        }
        const sourceFields = await handleCommentFields(dataset)
        const formFieldsObj = handleFormatSourceFields(sourceFields);

        // 缓存结果
        setCommentFormData(res.data);
        return formFieldsObj;
    }, [itemFormId, relatedDataSource, commentFormData]);

    const handleChange = useCallback((value: any) => {
        const result = toJS(value)
        const _filter = result.filter
        const { commentFormObjId } = commentFormData || {} // 评论表数据
        if (commentFormObjId) {
            const filterDatas = _filter?.datas?.map((item: any) => {
                const { objId, fieldName, config } = item
                if (objId === commentFormObjId) { // 评论表
                    const dataKey = commentSourceFields?.[0]?.fields?.find((field: AnyObj) => field.id === config?.fieldId)?.config?.dataKey || fieldName
                    return {
                        dataKey,
                        ...item,
                        customFieldName: fieldName,
                        fieldName: `c.${dataKey}`,
                        id: undefined, // 如果传id，条件会显示“已删除”（eb逻辑）；后端根据dataKey过滤，这里不传id无影响
                    }
                } else { // 主表
                    const dataKey = sourceFields?.[0]?.fields?.find((field: AnyObj) => field.id === config?.fieldId)?.config?.dataKey || fieldName
                    return {
                        dataKey,
                        ...item,
                        customFieldName: fieldName,
                        fieldName: `i.${dataKey}`,
                        id: undefined, // 如果传id，条件会显示“已删除”（eb逻辑）；后端根据dataKey过滤，这里不传id无影响
                    }
                }
            })
            onChange({
                ...result,
                filter: {
                    ..._filter,
                    datas: filterDatas
                }
            })
        }
    }, [sourceFields, commentSourceFields, commentFormData, onChange])

    const handleFields = useCallback(async () => {
        const sourceFields = await getFields(relatedDataSource, 'field')
        setSourceFields(sourceFields)
        return sourceFields
    }, [relatedDataSource])

    const handleCommentFields = useCallback(async (dataset) => {
        const sourceFields = await getFields(dataset, 'field')
        setCommentSourceFields(sourceFields)
        return sourceFields
    }, [])

    const getSourceField = useCallback(async () => {
        if (useOriginalTableFields) {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm(), handleFields()]).then(([formFieldsObj, fields]: [any, any]) => {
                    reslove([...fields, formFieldsObj])
                })
            })
        } else {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm()]).then(([formFieldsObj]: [any]) => {
                    reslove([formFieldsObj])
                })
            })
        }
    }, [useOriginalTableFields, handleCommentForm, handleFields])

    const getFieldSelectOptionUrl = useCallback(async (fieldComVal: any) => {
        const { fieldName, objId } = fieldComVal || {}
        const params = {
            objId,
            fieldName,
            sourceType: 'LOGIC',
            groupId: ebuilderformGroupId
        }
        const res = await fieldSelectOption(params);
        return res.data;
    }, [])

    const valueFilter = useMemo(() => {
        const { datas } = value?.filter || {}
        return {
            ...value?.filter,
            datas: toJS(datas)?.map((data: any) => {
                const { customFieldName, fieldName } = data
                return {
                    ...data,
                    fieldName: customFieldName || fieldName
                }
            })
        }
    }, [value])

    return (
        <div style={{ width: '100%' }}>
            <CorsComponent weId={`${props.weId || ''}_u3a8pb`}
                app="@weapp/ebdcontainercoms"
                compName="FilterCom"
                value={valueFilter || {}} // 显示字段回显filter对象
                onConfigChange={handleChange} // 回调
                config={{
                    ...value, // 显示字段回显filter对象
                    filter: valueFilter || {},
                    dataset: relatedDataSource, // 数据源
                }}
                comProps={{
                    fieldSelectOptionUrl: getFieldSelectOptionUrl
                }}
                // sourceField={sourceField}
                // placeholder={xxx} // 输入框placeholder
                // comId={xxx} // 组件id
                getSourceField={relatedDataSource ? getSourceField : undefined}
            />
        </div>
    )
}

export default FilterCondition;