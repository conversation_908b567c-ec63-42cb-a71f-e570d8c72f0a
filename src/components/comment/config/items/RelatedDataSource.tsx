import { FC, useCallback, useEffect } from 'react';
import { CommentConfigProps } from "../../types";
import { AnyObj, <PERSON><PERSON><PERSON>, CorsComponent, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { getCommentForm, getFormIdByObjId } from '../../api/data';
import { commentCommonFields, CommentEvents, ebuilderformGroupId } from '../../constant';
import { toJS } from 'mobx';
import { formatTopOperatBtns } from '../../utils/formatFormDatas';
import { commentClsPrefix } from '../../../../constants';

interface RelatedDataSourceProps extends CommentConfigProps<any> {

}

const RelatedDataSource: FC<RelatedDataSourceProps> = (props) => {
    const { onConfigChange, value: realValue, events, com, config } = props;
    const { displaySetting } = config

    const handleChange = useCallback(async (data: any) => {
        const result = data?.[0]
        if (!result) {
            onDelete()
            return
        }
        const { objId, name, appId } = result
        const value = {
            id: objId,
            appId,
            detailTable: false,
            groupId: ebuilderformGroupId,
            isPhysical: true,
            module: 'ebuilder',
            objType: 'physical',
            text: name,
            type: 'LOGIC',
            usage: 'all'
        }
        const { id } = value
        const res = await getFormIdByObjId({ objId: id })
        if (res.code !== 200) return;
        const commentFormRes = await getCommentForm({ itemFormId: res.data })
        if (commentFormRes.code !== 200) return;
        let changedData: AnyObj = {
            relatedDataSource: value,
            filterCondition: {},
            sortFields: [],
            itemFormId: res.data,
            commentFormId: commentFormRes.data.commentFormId,
            commentForm_Id: commentFormRes.data.id,
            displaySetting: {
                ...displaySetting,
                displayMode: 'normal',
                displayContentAdvanced: undefined
            }
        }
        if (!realValue && value) {
            // 切换成数据源模式，处理右上角按钮
            const topOptions = formatTopOperatBtns({ ...config, ...changedData })
            changedData.topOperatBtns = topOptions
        }
        onConfigChange(changedData)
        events?.emit(CommentEvents.COMMENT_UPDATE_COMMENTFORM, com?.id as any, commentFormRes.data)

        // 更新状态
        const { commentFormFieldList } = commentFormRes.data
        const customFieldsExist = commentFormFieldList.find((f: any) => {
            return !commentCommonFields.includes(f.dataKey)
        })
        events?.emit(CommentEvents.COMMENT_UPDATE_CUSTOMFIELDS, com?.id as any, customFieldsExist)
    }, [onConfigChange])

    const ebuilderFormGroup = { groupId: ebuilderformGroupId, sourceType: 'LOGIC' }

    const groupsFilter = useCallback((groups: Array<AnyObj>, sourceType: string) => {
        if (sourceType === ebuilderFormGroup.sourceType) {
            return groups.filter(group => group.id === ebuilderFormGroup.groupId)
        }
        return groups
    }, [])

    const onDelete = useCallback(() => {
        const changedData: AnyObj = {
            relatedDataSource: undefined,
            filterCondition: {},
            sortFields: [],
            itemFormId: '',
            commentForm: undefined,
            commentFormId: '',
            commentForm_Id: '',
            displaySetting: {
                ...displaySetting,
                displayMode: 'normal',
                displayContentAdvanced: undefined
            }
        }
        if (realValue) {
            // 切换成普通模式，处理右上角按钮
            const topOptions = formatTopOperatBtns({ ...config, ...changedData })
            changedData.topOperatBtns = topOptions
        }
        onConfigChange(changedData)
        events?.emit(CommentEvents.COMMENT_UPDATE_CUSTOMFIELDS, com?.id as any, false)
    }, [realValue, onConfigChange])

    const { id, text } = realValue || {}
    const browserValue = id ? [{ id, name: text }] : []

    return (
        <div className={`${commentClsPrefix}-config-dataset`}>
            {/** 标准数据源选择 */}
            {/*<CorsComponent weId={`${props.weId || ''}_odsuu2`}
                app='@weapp/components'
                compName='DataSetView'
                value={realValue}
                placeholder={getLabel('312132', '请选择数据源')}
                onChange={handleChange}
                //showEteamsData={false} // 显示数据仓库数据
                //showExternalData={false} // 显示外部数据源数据
                //showLogicData={false} // 显示逻辑表数据
                //groupsFilter={groupsFilter}
                //datasetDft={ebuilderFormGroup}
                useLogicFormData
                //showDataRights
            />
            <CorsComponent weId={`${props.weId || ''}_fedapj`}
                app="@weapp/ebdcoms"
                compName="IconFont"
                title={getLabel('207017', '删除')}
                name="Icon-delete"
                size="s"
                onClick={onDelete}
            />*/}
            {/** ebuilder选择表单浏览按钮 */}
            <Browser weId={`${props.weId || ''}_mvdegg`}
                type="ebuilderForm"
                module="ebuilder/form"
                commonParams={{ tableType: 'physical', isTree: true }}
                onChange={handleChange}
                value={browserValue}
                browserAssociativeProps={{
                    displayInput: true,
                    useBoxSelection: true,
                    inputWidth: '100%',
                    //disabledAssociativeList: true
                }}
            />
        </div>
    )
}

export default RelatedDataSource;