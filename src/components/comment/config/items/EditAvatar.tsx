import { FC, useCallback } from 'react';
import { Switch } from "@weapp/ui";
import { CommentConfigProps } from "../../types";

interface EditAvatarProps extends CommentConfigProps<any> {
  form?: any
}

const EditAvatar: FC<EditAvatarProps> = (props) => {
  const { className, onChange, value, form } = props;

  const handleChange = useCallback((value: boolean) => {
    onChange(value)
  }, [onChange])

  const showText = form.datas.showText

  return (
    <Switch weId={`${props.weId || ''}_nm1cvl`}
      value={value}
      className={className}
      onChange={handleChange}
      disabled={showText && props.client === 'MOBILE'}
    />
  )
}

export default EditAvatar;