import { FC, memo, useCallback, useMemo, useRef, useState } from "react";
import { CommentConfigProps, CommonConfigData } from "../../../types";
import { AnyObj, Button, Dialog, Icon, Select, Switch } from "@weapp/ui";
import { commentClsPrefix, uiPropsDesignClsPrefix } from "../../../../../constants";
import { getLabel } from "@weapp/utils";
import { columnsWidths, defColumnConfig } from "../common-config/constant";
import ButtonPlus from "../../../../../ui-plus/buttons-plus";
import { ReplySettingConfigData, initReplySettingConfig } from "../../../utils/initData";
import { CommonConfigContentRef } from "../../types";
import CommonConfigContent from "../common-config/Content";

const prefixCls = commentClsPrefix;

interface ReplySettingConfigProps extends CommentConfigProps<any> {

}

const settingNamse: Array<AnyObj> = [];

const ReplySettingConfig: FC<ReplySettingConfigProps> = memo((props) => {

    const { value, weId, page, ...resProps } = props;
    const { onChange } = props

    const contentRef = useRef<CommonConfigContentRef>(null);

    const [visible, setVisible] = useState(false);

    const handleOpenDialog = useCallback(() => {
        setVisible(true);
    }, [weId])

    const handleCloseDialog = useCallback(() => {
        setVisible(false);
    }, [weId])

    const { client } = page || {};

    const defaultData: ReplySettingConfigData = useMemo(() => {
        return initReplySettingConfig({ client }) || {};
    }, [client])

    const columnConfig = useMemo(() => defColumnConfig.filter(key => {
        let filter = true;
        switch (key) {
            case "operation": {
                filter = false;
                break;
            }
            case "customMessage": {
                filter = client === "PC";
                break;
            }
            default: {
                filter = true;
            }
        }
        return filter;
    }), [client])

    const customRenderOperation = useCallback((data: AnyObj, pos: number, name: string, totalData: AnyObj, handleDataChange: any) => {
        const { id } = data;

        if (!settingNamse.includes(id)) {
            return null;
        }

        return (
            <ButtonPlus
                weId={`${props.weId || ''}_bmv0f0`}
                type="link"
            >
                {getLabel("223930", "设置")}
            </ButtonPlus>
        )
    }, [props.weId])

    const [hoverShowReplyOption, setHoverShowReplyOption] = useState(value?.hoverShowReplyOption || defaultData?.hoverShowReplyOption);

    const customBodyExtraRender = useCallback(() => {
        return client === 'PC' ? <div className={`${commentClsPrefix}-config-custom-body-extra`}>
            <span>{getLabel('265602', '鼠标悬停显示回复操作')}</span>
            <Switch
                weId={`${props.weId || ''}_9m2h0d`}
                value={hoverShowReplyOption}
                size="sm"
                onChange={setHoverShowReplyOption}
            />
        </div> : <></>
    }, [props.weId, hoverShowReplyOption, client])

    const handleSave = useCallback(() => {
        const tableData = contentRef.current?.getCurrentData?.();
        onChange?.({
            hoverShowReplyOption,
            options: tableData
        })
        handleCloseDialog();
    }, [hoverShowReplyOption, handleCloseDialog])

    const description = client === "MOBILE" && (
        <>
            <span>
                {getLabel("266949", "说明：启用名称时，最多支持显示{0}个按钮，超出时最后一个位置显示", ["3"])}
            </span>
            <Icon
                weId={`${props.weId || ''}_6qt6tc`}
                name="Icon-more03"
                size="md"
            />
            <span>
                {getLabel("266950", "，点击可查看更多操作。")}
            </span>
        </>
    )

    const footer = (
        <div className={`${prefixCls}-config-setting-footer`}>
            <div className={`${prefixCls}-config-setting-description ${uiPropsDesignClsPrefix}-ellipsis`}>
                {description}
            </div>
            <div className={`${prefixCls}-config-setting-footer-btn`}>
                <Button
                    weId={`${props.weId || ''}_1n0rhu`}
                    type="primary"
                    onClick={handleSave}
                >
                    {getLabel("207006", "保存")}
                </Button>
            </div>
        </div>
    )

    const dialogWidth = useMemo(() => {
        let width = 80;
        columnConfig?.forEach(key => {
            width += columnsWidths[key];
        })

        return width;
    }, [columnConfig]);

    return (
        <>
            <div className={`${commentClsPrefix}-config-icon`}>
                <Icon
                    weId={`${props.weId || ''}_37yo6c`}
                    name="Icon-set-up-o"
                    style={{ cursor: 'pointer', color: 'var(--secondary-fc)' }}
                    onClick={handleOpenDialog}
                />
            </div>
            <Dialog
                weId={`${props.weId || ''}_cn3uhv`}
                visible={visible}
                title={getLabel('255857', '回复操作设置')}
                icon="Icon-e-builder"
                scale
                onClose={handleCloseDialog}
                footer={footer}
                closable
                destroyOnClose
                width={dialogWidth}
                className={`${commentClsPrefix}-config-reply-setting-config`}
            >
                <CommonConfigContent
                    weId={`${props.weId || ''}_0r5e56`}
                    {...resProps}
                    ref={contentRef}
                    page={page}
                    columnConfig={columnConfig}
                    value={value?.options || defaultData?.options}
                    customRenderOperation={customRenderOperation}
                    customBodyExtraRender={customBodyExtraRender}
                />
            </Dialog>
        </>
    )
})

export default ReplySettingConfig;