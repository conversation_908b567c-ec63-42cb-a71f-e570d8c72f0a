import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { CommentConfigProps } from "../../types";
import { CorsComponent, Dialog, utils } from '@weapp/ui';
import { getLabel, RequestOptionConfig } from '@weapp/utils';
import { commentClsPrefix } from '../../../../constants';
import { commentCommonFields, CommentEvents, moduleData } from '../../constant';
import { getCommentFormFieldsMemo, getCommentFormFieldsWithoutMemo } from '../../utils/getApiDatas';

const { getRandom } = utils
interface CustomFieldsProps extends CommentConfigProps<any> {

}

const CustomFields: FC<CustomFieldsProps> = (props) => {
    const { onChange, value, weId, config, onConfigChange, events, com, ...resProps } = props;

    const { commentFormId, commentForm, itemFormId, relatedDataSource } = config || {}

    const [visible, setVisible] = useState(false)

    const [customFieldsExist, setCustomFieldsExist] = useState(false)

    const handleChange = useCallback((value: any) => {
        onChange(value)
    }, [onChange])

    useEffect(() => {
        getCommentFormFieldsMemo(itemFormId).then(res => {
            if (res.code === 200) {
                const { commentFormFieldList } = res.data
                const customFieldsExist = commentFormFieldList.find((f: any) => {
                    return !commentCommonFields.includes(f.dataKey)
                })
                setCustomFieldsExist(customFieldsExist)
            }
        })

        events?.on(CommentEvents.COMMENT_UPDATE_CUSTOMFIELDS, com?.id as any, setCustomFieldsExist)

        return () => {
            events?.off(CommentEvents.COMMENT_UPDATE_CUSTOMFIELDS, com?.id as any, setCustomFieldsExist)
        }
    }, [])

    const handleOpenDialog = useCallback(() => {
        setVisible(true)
    }, [setVisible])

    const handleCloseDialog = useCallback(async () => {
        setVisible(false)

        // 重新获取表单数据
        if (!itemFormId) return;
        const res = await getCommentFormFieldsWithoutMemo(itemFormId);
        if (res.code === 200) {
            const { commentFormFieldList } = res.data
            const customFieldsExist = commentFormFieldList.find((f: any) => {
                return !commentCommonFields.includes(f.dataKey)
            })
            setCustomFieldsExist(customFieldsExist)

            onConfigChange?.({ commentForm: res.data, relatedDataSource: { ...relatedDataSource, dataExtMemoTime: getRandom() } }, { skipLayoutCompare: true })
            events?.emit(CommentEvents.COMMENT_UPDATE_COMMENTFORM, com?.id as any, res.data)
        }
    }, [relatedDataSource, setVisible])

    const interceptors = useMemo(() => {
        return {
            response: (responseData: any, config: RequestOptionConfig) => {
                if (config.url?.includes('/api/ebuilder/form/builder/getFormLayout')) {
                    return {
                        ...responseData,
                        data: {
                            ...responseData?.data,
                            // 关闭引导
                            firstEntry: false
                        }
                    }
                }
                return responseData
            }
        }
    }, [])

    return (
        <>
            <CorsComponent weId={`${props.weId || ''}_fedapj`}
                app="@weapp/ebdcoms"
                compName="IconFont"
                title={getLabel('223930', '设置')}
                name={customFieldsExist ? "Icon-set-up02-mcolor" : "Icon-set-up-o"}
                style={{ cursor: 'pointer', color: 'var(--secondary-fc)' }}
                size="s"
                onClick={handleOpenDialog}
            />
            <Dialog weId={`${props.weId || ''}_ii4ryz`}
                visible={visible}
                onClose={handleCloseDialog}
                closable
                scale
                mask
                maskClosable
                destroyOnClose
                defaultInnerScale
                title={getLabel('312131', '评论表自定义字段')}
                className={`${commentClsPrefix}-config-customfields-dialog`}
            >
                <CorsComponent weId={`${props.weId || ''}_z9gop7`}
                    app="@weapp/formbuilder"
                    compName="FormBuilder"
                    formId={commentFormId || commentForm.commentFormId}
                    module={moduleData.ebuilderformModule}
                    apiModule="ebuilder"
                    interceptors={interceptors}
                />
            </Dialog>
        </>
    )
}

export default CustomFields;