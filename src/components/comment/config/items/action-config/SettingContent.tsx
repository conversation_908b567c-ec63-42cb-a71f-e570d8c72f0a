import { Form, FormDatas, FormGroupProps, FormItemProps, FormLayoutType, FormStore } from "@weapp/ui";
import { FC, useCallback, useEffect } from "react";
import { EnableModuleType } from "../../../types";
import { getLabel } from "@weapp/utils";
import { Associate, AssociateType } from "./types";

export interface AssociatedSettingContentProps {
  weId: string;
  enableModule?: EnableModuleType[];
  formStore: FormStore;
}

const SettingContent: FC<AssociatedSettingContentProps> = (props) => {

  const { formStore, enableModule } = props;

  useEffect(() => {
    const { initForm, isFormInit, updateDatas, setLayoutProps } = formStore;
    const datas: any = {};
    let associateItems = [Associate.ASSOCIATETYPE];
    if (enableModule) {
      datas[Associate.ASSOCIATETYPE] = AssociateType.SELECT;
      datas[Associate.ASSOCIATE] = enableModule;
      associateItems = [Associate.ASSOCIATETYPE, Associate.ASSOCIATE]
    } else {
      datas[Associate.ASSOCIATETYPE] = AssociateType.ALL;
    }
    if (!isFormInit) {
      const items: FormItemProps = {
        [Associate.ASSOCIATETYPE]: {
          itemType: "SELECT",
          options: [{
            id: AssociateType.ALL,
            content: getLabel("223932", "全部"),
          }, {
            id: AssociateType.SELECT,
            content: getLabel("223933", "选择"),
          }]
        },
        [Associate.ASSOCIATE]: {
          itemType: "BROWSER",
          otherParams: {
            module: "relevance",
            type: "scopeItem",
            multiple: true,
          },
          browserBean: {
            browserAssociativeProps: { displayInput: true },
            disabledSelectedSort: false,
          }
        }
      }
      const layout: FormLayoutType[] = [[{
        id: 'associate',
        label: getLabel("223934", '显示范围'),
        items: associateItems,
        groupId: '',
        hide: false,
      }]]

      const groups: FormGroupProps[] = [];
      
      initForm({
        layout,
        items,
        groups,
        data: datas,
      })
    } else {
      updateDatas(datas);
      setLayoutProps("associate", {
        items: associateItems,
      });
    }
  }, [enableModule])

  const handleChange = useCallback((value: FormDatas = {}) => {
    const { setLayoutProps, updateDatas } = formStore;
    if (Associate.ASSOCIATETYPE in value) {
      const items = value[Associate.ASSOCIATETYPE] === AssociateType.ALL ? [Associate.ASSOCIATETYPE] : [Associate.ASSOCIATETYPE, Associate.ASSOCIATE];
      const data: any = value[Associate.ASSOCIATETYPE] === AssociateType.ALL ? { associate: undefined } : { associate: enableModule };
      setLayoutProps("associate", {
        items
      })
      updateDatas(data);
    }
  }, [enableModule, formStore])

  return (
    <Form
      weId={`${props.weId || ''}_kld7be`}
      store={formStore}
      onChange={handleChange}
    />
    
  )
}

export default SettingContent;