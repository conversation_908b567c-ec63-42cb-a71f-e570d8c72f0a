import { Button, DialogProps, FormStore } from "@weapp/ui";
import { FC, ReactElement, cloneElement, useCallback, useMemo, useState } from "react";
import { EnableModuleType } from "../../../types";
import DialogPlus from "../../../../../ui-plus/dialog-plus";
import SettingContent from "./SettingContent";
import { getLabel } from "@weapp/utils";
import { Associate, AssociateType } from "./types";

export interface AssociatedSettingProps {
  weId: string;
  visible?: boolean;
  dislogProps?: DialogProps;
  enableModule?: EnableModuleType[];
  onClose?: () => void;
  onSure?: (data?: EnableModuleType[]) => void;
}

const Setting: FC<AssociatedSettingProps> = (props) => {

  const { visible: pVisible, enableModule, children, onSure, onClose } = props;
  const [visible, setVisible] = useState(false);

  const formStore = useMemo(() => new FormStore(), []);
  
  if (pVisible !== undefined && visible !== pVisible) {
    setVisible(pVisible);
  }

  const handleClick = useCallback((e: any) => {
    setVisible(true);
  }, [])

  const handleClose = useCallback(() => {
    setVisible(false);
    onClose?.()
  }, [onClose])

  const cloneChild = cloneElement(children as ReactElement, {
    onClick: handleClick
  })

  const handleSave = useCallback(() => {

    const { getFormDatas } = formStore;
    const { [Associate.ASSOCIATETYPE]: type, [Associate.ASSOCIATE]: data } = getFormDatas();
    
    onSure?.(type === AssociateType.SELECT ? data : undefined);
    setVisible(false);
  }, [formStore, onSure])

  const footer = (
    <Button
      weId={`${props.weId || ''}_j7pzp2`}
      type="primary"
      onClick={handleSave}
    >
      {getLabel("223931", "保存")}
    </Button>
  )

  return (
    <>
      {cloneChild}
      <DialogPlus
        weId={`${props.weId || ''}_ajmo58`}
        onClose={handleClose}
        visible={visible}
        title={getLabel("223906", "评论关联事项设置")}
        width={580}
        destroyOnClose
        footer={footer}
        closable
      >
        <SettingContent
          weId={`${props.weId || ''}_6a1j1f`}
          enableModule={enableModule}
          formStore={formStore}
        />
      </DialogPlus>
    </>
  )
}

export default Setting;