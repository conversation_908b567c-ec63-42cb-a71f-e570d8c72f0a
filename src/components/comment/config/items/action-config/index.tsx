import { FC, memo, useCallback, useEffect, useMemo, useState } from "react";
import CommonConfig from "../common-config";
import { CommentConfigProps, EnableModuleType } from "../../../types";
import { CommonConfigData } from "../../../types";
import { initActionConfig } from "../../../utils/initData";
import { AnyObj, Icon, utils } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import ButtonPlus from "../../../../../ui-plus/buttons-plus";
import Setting from "./Setting";
import { defColumnConfig } from "../common-config/constant";

const { isEqual } = utils;

interface ActionConfigProps extends CommentConfigProps<CommonConfigData[]> {
  customHandleAssociativeChange?: (data?: EnableModuleType[]) => void;
}

const settingNamse = ["associated"];

const ActionConfig: FC<ActionConfigProps> = memo((props) => {

  const { value, weId, page, config, onChange, onConfigChange, onTableWidthChange, onCustomChange, customHandleAssociativeChange, ...resProps } = props;
  const { client } = page || {};
  const { enableModule } = config;

  const [visible, setVisible] = useState(false);
  const [enableModuleList, setEnableModuleList] = useState<EnableModuleType[]>();

  const handleChange = useCallback((value: CommonConfigData[]) => {
    onConfigChange({ actionConfig: value, enableModule: enableModuleList! });
  }, [enableModuleList, onConfigChange])

  useEffect(() => {
    setEnableModuleList(enableModule);
  }, [visible])

  const handleVisibleChange = useCallback((_visible: boolean) => {
    setVisible(_visible);
  }, [])

  const defaultData: CommonConfigData[] = useMemo(() => {
    return initActionConfig({ client: client! });
  }, [client])

  const handleAssociativeChange = useCallback((data?: EnableModuleType[]) => {
    setEnableModuleList(data);
  }, [])

  const customRenderOperation = useCallback((data: AnyObj, pos: number, name: string) => {

    const { id } = data;

    if (!settingNamse.includes(id)) {
      return null;
    }

    const onSure = typeof customHandleAssociativeChange === 'function' ? customHandleAssociativeChange : handleAssociativeChange

    return (
      <Setting
        {...props}
        weId={`${props.weId || ''}_z731sw`}
        enableModule={enableModuleList}
        onSure={onSure}
      >
        <ButtonPlus
          weId={`${props.weId || ''}_pc54n5`}
          type="link"
          data={data}
        >
          {getLabel("223930", "设置")}
        </ButtonPlus>
      </Setting>
    )
  }, [enableModuleList, handleAssociativeChange, props])

  const columnConfig = useMemo(() => defColumnConfig.filter(key => {
    let filter = true;
    switch (key) {
      case "customMessage": {
        filter = client === "PC";
        break;
      }
      case "showName": {
        filter = client === "PC";
        break;
      }
      default: {
        filter = true;
      }
    }
    return filter;
  }), [client])

  const description = client === "MOBILE" && (
    <>
      <span>
        {getLabel("223903", "说明：最多显示{0}个按钮，超出时最后一个位置显示", ["6"])}
      </span>
      <Icon
        weId={`${props.weId || ''}_u0opry`}
        name="Icon-add-to02"
        size="md"
      />
      <span>
        {getLabel("223904", "，点击可展开所有操作。")}
      </span>
    </>
  )

  return (
    <>
      <CommonConfig
        weId={`${props.weId || ''}_v6gp0m`}
        {...resProps}
        page={page}
        visible={visible}
        onVisibleChange={handleVisibleChange}
        onChange={handleChange}
        onCustomChange={onCustomChange}
        onConfigChange={onConfigChange}
        value={value || defaultData}
        columnConfig={columnConfig}
        title={getLabel("223905", "配置评论输入框操作按钮")}
        config={config}
        customRenderOperation={customRenderOperation}
        description={description}
        onTableWidthChange={onTableWidthChange}
        showSplitLine
      />
    </>
  )
})

export default ActionConfig;