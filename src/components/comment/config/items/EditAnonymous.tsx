import { FC, useCallback, useState } from 'react';
import { Switch } from '@weapp/ui';
import { getLabel } from "@weapp/utils";
import { IconFont } from "@weapp/ebdcoms";
import { CommentConfigProps } from "../../types";
import { AnonymousSettingDialog } from './anonymous-config';

interface EditAnonymousProps extends CommentConfigProps<any> {
  form?: any
}

const EditAnonymous: FC<EditAnonymousProps> = (props) => {
  const [visible, setVisible] = useState(false);

  const { className, onChange, value, form, weId, ...resProps } = props;

  const handleChange = useCallback((value: boolean) => {
    // @ts-ignore
    if (form.datas.useAnonymous === undefined) {
      resProps?.onConfigChange({ allowAnonymousSetting: true })
    }
    onChange(value)
  }, [onChange, resProps?.onConfigChange])

  const onSettingChange = useCallback((value: any) => {
    resProps?.onConfigChange?.(value);
    setVisible(false);
  }, [resProps?.onConfigChange])

  const onIconFontClick = useCallback(() => setVisible(true), [])
  const onVisibleChange = useCallback((value: boolean) => setVisible(value), [])

  const useAnonymous = form.datas.useAnonymous
  return (
    <div className={'ebcoms-config-titleConfig'}>
      <Switch weId={`${weId || ''}_f2jocy`}
        value={value}
        className={className}
        onChange={handleChange}
      />
      {useAnonymous && (
        <IconFont weId={`${weId || ''}_peh28y`}
          name="Icon-set-up-o"
          title={getLabel('288130', '配置相关功能')}
          placement="bottomRight"
          onClick={onIconFontClick}
          size={'md'}
        />)}
       <AnonymousSettingDialog
         weId={`${weId || ''}_zwtj3h`}
         key={'commentConfig'}
         {...resProps}
         form={form}
         visible={visible}
         onVisibleChange={onVisibleChange}
         onChange={onSettingChange}
       />
    </div>
  )
}

export default EditAnonymous;