import { FC, useCallback, useEffect, useRef, useState } from 'react';
import { CommentConfigProps } from "../../types";
import { AnyObj, CorsComponent } from '@weapp/ui';
import { getCommentFormFieldsMemo, getFields } from '../../utils/getApiDatas';
import { handleFormatSourceFields } from '../../utils';
import { CommentEvents } from '../../constant';

interface SortFieldsProps extends CommentConfigProps<any> {

}

const SortFields: FC<SortFieldsProps> = (props) => {
    const { onChange, events, com, value, config } = props;

    const { useOriginalTableFields, relatedDataSource, itemFormId } = config

    const [commentSourceFields, setCommentSourceFields] = useState<Array<AnyObj>>([])

    const [commentFormData, setCommentFormData] = useState<any>(null);

    useEffect(() => {
        events?.on(CommentEvents.COMMENT_UPDATE_COMMENTFORM, com?.id as any, updateCommentForm);
        return () => {
            //events?.off(CommentEvents.COMMENT_UPDATE_COMMENTFORM, com?.id as any);
        }
    }, [])

    const updateCommentForm = (commentForm: any) => {
        setCommentFormData(commentForm)
    }

    const handleCommentForm = useCallback(async () => {
        // 如果已经缓存，直接返回格式化后的值
        if (commentFormData) {
            const { commentFormObjId } = commentFormData || {}
            const dataset = {
                ...relatedDataSource,
                id: commentFormObjId
            }
            const sourceFields = await handleCommentFields(dataset)
            const formFieldsObj = handleFormatSourceFields(sourceFields)
            return formFieldsObj?.fields ? [formFieldsObj] : []
        }

        // 否则发请求
        if (!itemFormId) return [];
        const res = await getCommentFormFieldsMemo(itemFormId, relatedDataSource?.dataExtMemoTime);
        const { commentFormObjId } = res.data || {};
        const dataset = {
            ...relatedDataSource,
            id: commentFormObjId
        }
        const sourceFields = await handleCommentFields(dataset)
        const formFieldsObj = handleFormatSourceFields(sourceFields);

        // 缓存结果
        setCommentFormData(res.data);

        return formFieldsObj?.fields ? [formFieldsObj] : [];
    }, [itemFormId, commentFormData, relatedDataSource]);

    const handleChange = useCallback((data: any) => {
        const { order } = data || {}
        const { commentFormObjId } = commentFormData || {}
        const newOrder = order?.map((item: any) => {
            const { objId, id, name } = item
            if (objId === commentFormObjId) {
                const dataKey = commentSourceFields?.[0]?.fields?.find((field: AnyObj) => field.id === id)?.config?.dataKey || name
                return {
                    ...item,
                    dataKey,
                    //fieldName: `c.${field?.dataKey}`,
                    tableAlias: 'c',
                    field: dataKey,
                    ascending: item.orderType === 'ASC'
                }
            } else {
                return {
                    ...item,
                    //fieldName: item.fieldName ? `i.${item.fieldName}` : item.fieldName,
                    tableAlias: 'i',
                    field: item.name,
                    ascending: item.orderType === 'ASC'
                }
            }
        }) || []
        onChange(newOrder)
    }, [commentFormData, onChange])

    const handleFields = useCallback(async () => {
        return getFields(relatedDataSource, 'field')
    }, [relatedDataSource])

    const handleCommentFields = useCallback(async (dataset) => {
        const sourceFields = await getFields(dataset, 'field')
        setCommentSourceFields(sourceFields)
        return sourceFields
    }, [])

    const getCusFields = useCallback(async () => {
        if (useOriginalTableFields) {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm(), handleFields()]).then(([formFieldsArr, fields]: [any, any]) => {
                    reslove([...fields, ...formFieldsArr])
                })
            })
        } else {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm()]).then(([formFieldsArr]: [any]) => {
                    reslove(formFieldsArr)
                })
            })
        }
    }, [useOriginalTableFields, handleCommentForm, handleFields])

    return (
        <div style={{ width: '100%' }}>
            <CorsComponent weId={`${props.weId || ''}_dp9efk`}
                app="@weapp/ebdcontainercoms"
                compName="OrderFieldsCom"
                value={value} // 显示字段回显数组order
                onConfigChange={handleChange} // 回调
                config={{
                    order: value, // 显示字段回显数组order
                    dataset: relatedDataSource, // 数据源
                }}
                showEbuilderSort={false}
                ajaxInDialog
                getCusFields={relatedDataSource?.id ? getCusFields : undefined}
                // 强制触发组件重新加载
                key={useOriginalTableFields ? 'withOriginal' : 'withoutOriginal'}
            // needDetailField={false} // 是否需要明细字段
            // externalElement={xxx} // 自定义显示回显组件，不传默认是输入框组件
            />
        </div>
    )
}

export default SortFields;