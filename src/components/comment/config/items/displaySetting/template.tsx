import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { CommentConfigProps } from "../../../types";
import { Dialog, Icon, Input, Radio, RadioOptionProps, Button } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { getInitTempateData, getInitTempateDataM } from './constants';
import CommentTemplate from './CommentTemplate';
import { ClientType } from '@weapp/ebdcoms';

interface TemplateProps extends CommentConfigProps<any> {
    prefixCls: string;
}

const Template: FC<TemplateProps> = (props) => {
    const { weId, prefixCls, value, onChange, client, ...resProp } = props;

    const [visible, setVisible] = useState(false)
    const [template, setTemplate] = useState(value)

    useEffect(() => {
        setTemplate(value)
    }, [visible])

    const handleOpenDialog = useCallback(() => {
        setVisible(!visible)
    }, [visible, setVisible])

    const onSure = useCallback(() => {
        onChange(template)
        handleOpenDialog()
    }, [template, onChange, handleOpenDialog])

    return (
        <>
            <Input
                weId={`${props.weId || ''}_e3ikuj`}
                suffix={<Icon weId={`${props.weId || ''}_uyec0s`} onClick={handleOpenDialog} size={'s'} name="Icon-search" />}
                type='text'
                inputReadOnly
                value={getLabel('312110', '模板') + value}
                inputClassName={`${prefixCls}-template-input`}
                style={{ cursor: 'pointer' }}
                onClick={handleOpenDialog}
            />
            <Dialog weId={`${props.weId || ''}_lvzcit`}
                title={getLabel('312121', '选择模板')}
                visible={visible}
                onClose={handleOpenDialog}
                closable
                scale
                mask
                maskClosable
                destroyOnClose
                width={850}
                icon='Icon-e-builder'
                className={`${prefixCls}-template-dialog`}
                footer={[
                    <Button weId={`${props.weId || ''}_x7ls7x@${0}`} onClick={onSure} type='primary'>{getLabel('261599', '确定')}</Button>,
                    <Button weId={`${props.weId || ''}_3qnau1@${1}`} onClick={handleOpenDialog}>{getLabel('223917', '取消')}</Button>
                ]}
            >
                <Content weId={`${props.weId || ''}_71rna3`}
                    prefixCls={prefixCls}
                    value={template}
                    onChange={setTemplate}
                    client={client}
                />
            </Dialog>
        </>
    )
}

interface ContentProps {
    weId: string;
    prefixCls: string;
    value: string;
    onChange: (id: string) => void;
    client?: ClientType;
}

const Content: FC<ContentProps> = (props) => {

    const { prefixCls, value, onChange, client, weId } = props

    const templateData = useMemo(() => {
        return client === 'PC' ? getInitTempateData() : getInitTempateDataM()
    }, [client])

    const customOptionRender = useCallback((_props: RadioOptionProps, ele: React.ReactNode) => {
        const { id, showDepartmentBelow } = _props.option as any || {}
        return <CustomOption
            weId={`${props.weId || ''}_j03jxe`}
            prefixCls={prefixCls}
            value={value}
            ele={ele}
            id={id}
            onChange={onChange}
            client={client}
            showDepartmentBelow={showDepartmentBelow}
        />
    }, [value, prefixCls, onChange])

    const onRadioChange = useCallback((value: any) => {

    }, [])

    return (
        <>
            <Radio weId={`${props.weId || ''}_587j7y`}
                data={templateData}
                customOptionRender={customOptionRender}
                canReversedChoose={false}
                value={value}
                onChange={onRadioChange}
            />
        </>
    )
}

interface CustomOptionProps {
    id: string;
    weId: string;
    prefixCls: string;
    value: string;
    ele: React.ReactNode;
    onChange: (id: string) => void;
    client?: ClientType;
    showDepartmentBelow?: boolean;
}

const CustomOption: FC<CustomOptionProps> = (props) => {

    const { prefixCls, ele, id, value, client, showDepartmentBelow, onChange } = props

    const onSelect = useCallback(() => {
        onChange(id)
    }, [onChange, id])

    const cardCls = classnames(`${prefixCls}-template-card`, {
        [`${prefixCls}-template-card-active`]: value === id
    })

    return (
        <>
            <div className={cardCls}>
                <div className={`${prefixCls}-template-card-mask`} onClick={onSelect}></div>
                {ele}
                <CommentTemplate
                    weId={`${props.weId || ''}_j2q0fa`}
                    commentProps={{
                        templateId: id,
                        showSource: true,
                        showDepartment: true,
                        showDepartmentBelow
                    }}
                    prefixCls={prefixCls}
                    client={client}
                />
            </div>
        </>
    )
}

export default Template;