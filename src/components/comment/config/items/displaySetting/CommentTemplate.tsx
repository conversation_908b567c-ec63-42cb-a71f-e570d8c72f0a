import { FC, ReactNode, useCallback, useMemo } from 'react';
import { AnyObj, Comment, MComment, ReplyOption } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { ClientType } from '@weapp/ebdcoms';

const { CommentList } = Comment
const { MCommentMenu } = MComment

interface CommentTemplateProps {
    weId: string;
    prefixCls: string;
    showCustomArea?: boolean;
    client?: ClientType;
    commentProps?: CommentProps;
}

interface CommentProps extends AnyObj {
    templateId?: string;
    showListAvatar?: boolean;
    showTime?: boolean;
    showSource?: boolean;
    splitline?: string;
    splitlineColor?: boolean;
    showCustomField?: boolean;
    showDepartment?: boolean;
    showDepartmentBelow?: boolean;
    optionConfig?: any;
}

const CommentTemplate: FC<CommentTemplateProps> = (props) => {

    const { prefixCls, client, showCustomArea, commentProps } = props
    const { showCustomField, showDepartment, showDepartmentBelow, optionConfig } = commentProps || {}
    const previewData = useMemo(() => {
        return [
            { id: '1', client: 'pc', content: getLabel('312098', '好的，收到！'), commentor: { id: '1', username: getLabel('312099', '江辰') }, addTime: 1659337200000 },
            { id: '2', client: 'iphone', content: getLabel('312098', '好的，收到！'), commentor: { id: '2', username: getLabel('312100', '彭玉') }, addTime: 1659333600000 }
        ] as ReplyOption[]
    }, [])

    const customAvatar = useCallback((data: any) => {
        const { id } = data
        if (id === '1') {
            return '/images/media/boy.png'
        }
        return '/images/media/girl.png'
    }, [])

    const renderCustomMoreContent = useCallback((data: any) => {
        if (showCustomField) {
            return <div className={`${prefixCls}-comment-template-customfield`}>{getLabel('312101', '自定义字段')}</div>
        }
        return null
    }, [showCustomField])

    const getDepartment = useCallback((data: any) => {
        const { id } = data
        const department = id === '1' ? getLabel('312102', '销售经理部') : getLabel('312103', '产品管理部')
        return department
    }, [])

    const renderItemHeaderMore = useCallback((data: any) => {
        if (showDepartment && !showDepartmentBelow) {
            const department = getDepartment(data)
            const cls = classnames(`${prefixCls}-comment-template-department`, {
                [`${prefixCls}-m-comment-template-department`]: client === 'MOBILE'
            })
            return <span className={cls}>{department}</span>
        }
        return null
    }, [showDepartment, showDepartmentBelow, client, getDepartment])

    const renderItemHeaderInfo = useCallback(() => {
        return null
    }, [])

    const renderItemHeaderBelow = useCallback((data: any) => {
        if (showDepartment && showDepartmentBelow) {
            const department = getDepartment(data)
            return <span className={`${prefixCls}-m-comment-template-department-below`}>{department}</span>
        }
        return null
    }, [showDepartment, showDepartmentBelow, getDepartment])

    return (
        client === 'PC' ? <CommentList
            weId={`${props.weId || ''}_evg8zg`}
            {...commentProps}
            data={previewData}
            alwaysShow
            customAvatarUrl={customAvatar}
            className={`${prefixCls}-comment-template`}
            //@ts-ignore
            showCustomArea={showCustomArea}
            renderCustomMoreContent={renderCustomMoreContent}
            renderItemHeaderMore={renderItemHeaderMore}
            renderItemHeaderInfo={renderItemHeaderInfo}
        /> : <MCommentMenu // 移动端用MCommentMenu渲染列表（如果使用MComment，需要用handleAfterGetComment处理接口数据，且无法避免调接口）
            weId={`${props.weId || ''}_m931vj`}
            optionConfig={['delete', 'reply', 'quote', 'task', 'more']}
            {...commentProps}
            className={classnames(`${prefixCls}-m-comment-template`, { [`${prefixCls}-m-comment-template-below`]: showDepartmentBelow })}
            data={previewData}
            customAvatarUrl={customAvatar}
            //@ts-ignore
            showCustomArea={showCustomArea}
            renderCustomMoreContent={renderCustomMoreContent}
            renderItemHeaderMore={renderItemHeaderMore}
            renderItemHeaderInfo={renderItemHeaderInfo}
            renderItemHeaderBelow={renderItemHeaderBelow}
            hasCommentSearch={false}
            optionMax={4}
            listProps={{ renderFooter: <></> }}
        />
    )
}

export default CommentTemplate;