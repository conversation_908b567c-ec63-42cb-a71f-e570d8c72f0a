import { FC, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { Comment, CorsComponent, MComment, ReplyOption } from '@weapp/ui';
import { classnames, eventEmitter, getLabel } from '@weapp/utils';
import pluginStore from '../../../components/pluginStore';
import { CommentConfigProps } from '../../../types';
import { appName, commentClsPrefix } from '../../../../../constants';
import { CommentEvents } from '../../../constant';
import ReactDOM from 'react-dom';
import { CustomFields } from './constants';
import { handleGridToText } from '../../../utils';

const { CommentList } = Comment
const { MCommentMenu } = MComment

const prefixCls = commentClsPrefix;

interface AdvancedTemplateProps extends CommentConfigProps {
    weId: string;
    prefixCls: string;
    advancedData: any;
    commentProps: any;
}

const AdvancedTemplate: FC<AdvancedTemplateProps> = (props) => {

    const { config, advancedData, comId, commentProps, client } = props

    const pluginName = 'CommentPlugin'

    const suffix = '_config'

    const id = comId + suffix

    const [forceUpdate, setForceUpdate] = useState(0)

    const [slots, setSlots] = useState<any>({})

    const updateSlots = useCallback((newSlots: any) => {
        setSlots({ ...newSlots })
    }, [])

    useEffect(() => {
        const plugin = pluginStore.pluginCenter.originalEnabledPlugins.find((plugin: any) => plugin.name === pluginName)
        const eventName = `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`
        if (!plugin.eventNames.find((name: any) => name === eventName)) {
            plugin.eventNames.push(eventName)
        }
        eventEmitter.on(appName, `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`, updateSlots)

        return () => {
            const plugin = pluginStore.pluginCenter.originalEnabledPlugins.find((plugin: any) => plugin.name === pluginName)
            const eventName = `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`
            if (plugin.eventNames.find((name: any) => name === eventName)) {
                plugin.eventNames = plugin.eventNames.filter((name: any) => name !== eventName)
            }
            eventEmitter.off(appName, `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`, updateSlots)
        }
    }, [])


    const renderPortals = useCallback((ele: any, data: any, type: string) => {
        const { id } = data
        const itemSlots = slots[id]?.filter((slot: any) => slot.type === type)
        let portals = itemSlots?.map((slot: any) => {
            const { id, ref } = slot
            if (id.includes(suffix)) { // 限制渲染config中的元素
                const container = ref?.current || document.getElementById(id)
                if (container) {
                    return ReactDOM.createPortal(ele, container)
                }
            }
            return null
        })
        if (type === 'operate') {
            if (portals?.every((portal: any) => !portal)) {
                return ele
            }
        }
        return portals
    }, [slots])

    const customAvatar = useCallback((ele: any, data: any) => {
        return renderPortals(ele, data, 'avatar')
    }, [renderPortals, forceUpdate])

    const renderUserContent = useCallback((data: any, ele: any) => {
        return renderPortals(ele, data, 'user')
    }, [renderPortals, forceUpdate])

    const renderItemContent = useCallback((data: any, ele: any) => {
        return renderPortals(ele, data, 'content')
    }, [renderPortals, forceUpdate])

    const renderSourceContent = useCallback((data: any, ele: any) => {
        return renderPortals(ele, data, 'source')
    }, [renderPortals, forceUpdate])

    const renderTimeContent = useCallback((data: any, ele: any) => {
        return renderPortals(ele, data, 'time')
    }, [renderPortals, forceUpdate])

    const renderFooterContent = useCallback((data: any, ele: any) => {
        return renderPortals(ele, data, 'operate')
    }, [renderPortals, forceUpdate])

    const renderFooterBottomContent = useCallback((data: any, ele: any) => {
        return renderPortals(ele, data, 'operateBottomArea')
    }, [renderPortals, forceUpdate])

    const advancedListData = useMemo(() => {
        const { listField } = advancedData || {}
        let data1: any = {
            id: '35752241697824588151',
        }
        let data2: any = {
            id: '35752241252430588101',
        }
        listField?.forEach((field: any) => {
            const { id, text, hidden } = field
            if (!CustomFields[id] && !hidden) {
                data1[id] = text
                data2[id] = text
            }
        })
        Object.assign(data1, { comId, isConfig: true })
        Object.assign(data2, { comId, isConfig: true })
        return [data1, data2]
    }, [comId, advancedData.listField])

    useEffect(() => {
        // 在渲染完成后触发一次更新，用于触发 customAvatar 的重新渲染
        // 因为avatar等中的renderPortals会先于customRenderCommentItemContent的ListCard执行，那么ListCard渲染了一个新的dom后，avatar并不会重新渲染，就会元素导致空白
        // 所以强制再触发一次渲染
        setForceUpdate(t => t + 1);
    }, [advancedData]); // 依赖需要注意：customRenderCommentItemContent触发渲染之后，触发这个effect，因此这里添加依赖advancedData

    const customRenderCommentItemContent = useCallback((rowData: ReplyOption, content: ReactNode) => {
        const data = advancedListData.find(item => item.id === rowData.id)
        const grid = handleGridToText(advancedData.cardLayout?.grid || [])
        const cardLayout = {
            ...advancedData.cardLayout,
            grid
        }
        return <div className={`${prefixCls}-list-card`}>
            <CorsComponent weId={`${props.weId || ''}_dzxaiq`}
                app="@weapp/ebdcontainercoms"
                compName="ListCard"
                key={rowData.id}
                data={data} // 行数据
                rowIndex={rowData.id} // 行下标
                cardLayout={cardLayout || { grid: [] }} // config中保存的cardLayout
                plugin={pluginStore.pluginCenter} // 实例化后的插件中心
                // pageNo={pageNo} // 当前页码，配置序号字段时需要
                // pageSize={pageSize} // 页码大小，配置序号字段时需要
                isDesign={true} // 是否是设计器
                extraConfig={{
                    ...advancedData,
                    //listField: [],
                    dataset: config.relatedDataSource || {}
                }} // config配置，要有dataset、ListField
            />
            <div style={{ display: 'none' }}>{content}</div>
        </div>
    }, [advancedListData, advancedData, config.relatedDataSource])

    const previewData = useMemo(() => {
        return [
            { id: '35752241697824588151', client: 'pc', content: getLabel('312098', '好的，收到！'), commentor: { id: '1', username: getLabel('312099', '江辰') }, addTime: 1659337200000 },
            { id: '35752241252430588101', client: 'iphone', content: getLabel('312098', '好的，收到！'), commentor: { id: '2', username: getLabel('312100', '彭玉') }, addTime: 1659333600000 }
        ] as ReplyOption[]
    }, [])

    return <>
        {client === 'PC' ? <CommentList weId={`${props.weId || ''}_b500fq`}
            disableOptionCheck
            {...commentProps}
            data={previewData}
            //optionConfig={['copy','reply','delete']}
            renderUserContent={renderUserContent}
            renderItemContent={renderItemContent}
            renderSourceContent={renderSourceContent}
            renderTimeContent={renderTimeContent}
            renderFooterContent={renderFooterContent}
            renderFooterBottomContent={renderFooterBottomContent}
            customAvatar={customAvatar}
            customRenderCommentItemContent={customRenderCommentItemContent}
        /> : <MCommentMenu weId={`${props.weId || ''}_glmdv7`}
            disableOptionCheck
            {...commentProps}
            className={classnames(`${prefixCls}-m-advanced-template`)}
            data={previewData}
            hasCommentSearch={false}
            optionMax={4}
            listProps={{ renderFooter: <></> }}
            //optionConfig={['copy','reply','delete']}
            renderUserContent={renderUserContent}
            renderItemContent={renderItemContent}
            renderSourceContent={renderSourceContent}
            renderTimeContent={renderTimeContent}
            renderFooterContent={renderFooterContent}
            renderFooterBottomContent={renderFooterBottomContent}
            customAvatar={customAvatar}
            customRenderCommentItemContent={customRenderCommentItemContent}
        />
        }
    </>
}

export default AdvancedTemplate