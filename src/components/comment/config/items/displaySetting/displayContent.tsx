import { FC, useCallback, useEffect, useState } from 'react';
import { CommentConfigProps } from "../../../types";
import { Tag, TagProps } from '@weapp/ui';
import { getInitTagData } from './constants';

interface DisplayContentProps extends CommentConfigProps<any> {
    prefixCls: string;
}

interface TagDataProps {
    id: string;
    content: string;
    editable: boolean;
    checked: boolean;
    disabled: boolean;
}

const DisplayContent: FC<DisplayContentProps> = (props) => {
    const { weId, prefixCls, value, onChange, ...resProp } = props;

    const [displayData, setDisplayData] = useState<TagDataProps[]>()

    useEffect(() => {
        // 需要处理历史数据
        setDisplayData(value || getInitTagData())
    }, [])

    const onClick = useCallback((checked: boolean, _index: number) => {
        const newData = displayData?.map((item, index) => {
            if (index === _index) {
                return {
                    ...item,
                    checked
                }
            }
            return item
        })
        setDisplayData(newData)
        onChange(newData)
    }, [displayData, setDisplayData, onChange])

    return (
        <div className={`${prefixCls}-display-content`}>
            {displayData?.map((item, index) => {
                const { id, content, checked, disabled } = item
                const _props = { id, checked, onClick, disabled, content, index }
                return <TagContent weId={`${props.weId || ''}_fy52oj@${id}`} key={id} {..._props} />
            })}
        </div>
    )
}

export interface TagContentProps extends TagProps {
    weId: string;
    id: string;
    content: string;
    index: number;
    onClick: (checked: boolean, index: number) => void;
}

const TagContent: FC<TagContentProps> = (props) => {
    const { checked, onClick, disabled, content, index } = props

    const onCheck = useCallback((checked: boolean, e: any) => {
        onClick(checked, index)
    }, [index, onClick])

    return (
        <Tag weId={`${props.weId || ''}_twyxvy`} checked={checked} onCheck={onCheck} disabled={disabled} type={checked ? 'primary' : 'default'}>{content}</Tag>
    )
}

export default DisplayContent;