import { FC, useCallback, useEffect, useMemo } from 'react';
import { CommentConfigProps, CommonConfigData } from "../../../types";
import { AnyObj, CorsComponent, Form, FormDatas, FormItemProps, FormLayoutType, FormStore, FormSwitchProps, utils } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import Template from './template';
import DisplayContent, { TagContentProps } from './displayContent';
import DisplayContentAdvanced from './DisplayContentAdvanced';
import CommentTemplate from './CommentTemplate';
import { observer } from 'mobx-react';
import { CustomFields, getInitTagData, getInitTempateData, getInitTempateDataM } from './constants';
import OptionConfig from '../option-config';
import getCommentProps from '../../../props/commentProps';
import { getDisplayConfig } from '../../../utils';
import pluginStore from '../../../components/pluginStore';
import { toJS } from 'mobx';
import AdvancedTemplate from './advancedTemplate';

const { needSync } = utils

interface ContentProps extends CommentConfigProps {
    formStore: FormStore;
    prefixCls: string;
}

const labelSpan = 6

const Content: FC<ContentProps> = (props) => {
    const { onChange, formStore, value, weId, prefixCls, config, client, ...resProps } = props;

    const handleChange = useCallback((value: any) => {
        onChange(value)
    }, [onChange])

    useEffect(() => {
        const items: FormItemProps = {
            'template': {
                itemType: 'CUSTOM',
                value: '1'
            },
            'mTemplate': {
                itemType: 'CUSTOM',
                value: '1'
            },
            'displayMode': {
                itemType: 'SELECT',
                data: [
                    { id: 'normal', content: getLabel('312117', '一般模式') },
                    { id: 'advanced', content: getLabel('312118', '高级模式') }
                ],
                value: 'normal'
            },
            'displayContent': {
                itemType: 'CUSTOM',
            },
            'displayContentAdvanced': {
                itemType: 'CUSTOM',
            },
            'sourceConfig': {
                itemType: 'CHECKBOX',
                data: [
                    { id: 'PC', content: getLabel('312119', 'PC端提交，显示评论来源') },
                    { id: 'nonPC', content: getLabel('312120', '非PC端提交，显示评论来源') }
                ],
                value: ['PC', 'nonPC']
            },
            'optionConfig': {
                itemType: 'CUSTOM',
            },
            'split': {
                itemType: 'SELECT',
                data: [
                    { id: 'solid', content: '—————————' },
                    { id: 'longDashed', content: '— — — — — — — ' },
                    { id: 'dashed', content: '---------------------------' },
                    { id: 'dotted', content: '···························' },
                    { id: 'differDashed', content: '— - — - — - — - — - ' },
                ],
                value: 'dashed',
                style: { width: '150px' }
            },
            'splitColor': {
                itemType: 'COLORPICKER',
                value: '#e5e5e5'
            },
        }
        const layout: FormLayoutType[] = [
            [{
                id: 'template',
                label: getLabel('312121', '选择模板'),
                labelSpan,
                items: ['template'],
                groupId: '',
                hide: false
            }],
            [{
                id: 'displayMode',
                label: getLabel('312122', '显示模式'),
                labelSpan,
                items: ['displayMode'],
                groupId: '',
                hide: false
            }],
            [{
                id: 'mTemplate',
                label: getLabel('312121', '选择模板'),
                labelSpan,
                items: ['mTemplate'],
                groupId: '',
                hide: false
            }],
            [{
                id: 'displayContent',
                label: getLabel('312123', '显示内容'),
                labelSpan,
                items: ['displayContent'],
                groupId: '',
                hide: false
            }],
            [{
                id: 'sourceConfig',
                label: getLabel('312124', '来源设置'),
                labelSpan,
                items: ['sourceConfig'],
                groupId: '',
                hide: false
            }],
            [{
                id: 'optionConfig',
                label: getLabel('312126', '按钮设置'),
                labelSpan,
                items: ['optionConfig'],
                groupId: '',
                hide: false
            }],
            [{
                id: 'split',
                label: getLabel('312128', '分隔线样式'),
                labelSpan,
                items: ['split'],
                groupId: '',
                hide: false
            }],
            [{
                id: 'splitColor',
                label: getLabel('312129', '线段颜色'),
                labelSpan,
                items: ['splitColor'],
                groupId: '',
                hide: false
            }],
        ]
        // 处理历史数据: showListAvatar, showTime, showSource
        let displayContent = [...(value.displayContent || [])]
        if (!value.displayContent) {
            const initTagData = getInitTagData()
            const { showListAvatar, showTime, showSource } = config
            if (showListAvatar !== undefined) {
                const tag = initTagData.find(tag => tag.id === 'avatar')
                if (tag) {
                    tag.checked = showListAvatar
                }
            }
            if (showTime !== undefined) {
                const tag = initTagData.find(tag => tag.id === 'time')
                if (tag) {
                    tag.checked = showTime
                }
            }
            if (showSource !== undefined) {
                const tag = initTagData.find(tag => tag.id === 'source')
                if (tag) {
                    tag.checked = showSource
                }
            }
            displayContent = initTagData
        }
        // 处理 评论操作-配置按钮optionConfig
        const data = { ...value, displayContent, optionConfig: config.optionConfig }
        formStore.initForm({
            data,
            items,
            layout,
            groups: []
        })
        // 初次渲染的联动
        const { template: pcTemplate, mTemplate, displayMode } = formStore.getFormDatas()
        const template = client === 'PC' ? pcTemplate : mTemplate
        if (displayMode === 'normal') {
            formStore.setLayoutProps('displayContent', { items: ['displayContent'] })
            const source = displayContent.find(item => item.id === 'source')
            const { checked } = source || {}
            formStore.setHide('sourceConfig', !checked)
            formStore.setHide('template', client !== 'PC')
            formStore.setHide('mTemplate', client === 'PC')
            const showSplitline = templateData.find(t => t.id === template)?.showSplitline
            formStore.setHide('split', !showSplitline)
            formStore.setHide('splitColor', !showSplitline)
        } else {
            const { listField } = value.displayContentAdvanced as AnyObj || {}
            const useSource = listField?.find((field: any) => field.id === CustomFields.SOURCE)
            formStore.setLayoutProps('displayContent', { items: ['displayContentAdvanced'] })
            formStore.setHide('sourceConfig', !useSource)
            formStore.setHide('template', true)
            formStore.setHide('mTemplate', true)
            formStore.setHide('split', client !== 'PC')
            formStore.setHide('splitColor', client !== 'PC')
        }
        if (!config.relatedDataSource) { // 没有数据源，只显示一般模式
            formStore.setItemProps('displayMode', {
                data: [{ id: 'normal', content: getLabel('312117', '一般模式') }]
            })
        }
    }, [config.relatedDataSource])

    useEffect(() => {
        const { pluginCenter } = pluginStore

    })

    /** 用于初始切换至高级模式时，自动带入默认字段布局 */
    const onAdvandedChange = useCallback((value: any) => {
        formStore.updateDatas({ displayContentAdvanced: value })
        onFormChange({ displayContentAdvanced: value })
    }, [onChange])

    const customRenderFormSwitch = useCallback((key: string, formSwitchProps: FormSwitchProps) => {
        const comProps = (formSwitchProps as any).props;
        const { onChange, value } = comProps;

        const commonProps = {
            onChange,
            value,
            config,
            client,
            ...resProps,
            prefixCls
        }

        const optionConfigProps = {
            ...commonProps,
        }

        let resultDom = <></>
        switch (key) {
            case 'template':
            case 'mTemplate':
                resultDom = <Template
                    weId={`${props.weId || ''}_wlbaby`}
                    {...commonProps}
                />
                break
            case 'optionConfig':
                resultDom = <div className={`${prefixCls}-optionConfig`}>
                    <OptionConfig
                        weId={`${props.weId || ''}_dvwvyn`}
                        {...optionConfigProps}
                    />
                </div>
                break
            case 'displayContent':
                resultDom = <DisplayContent
                    weId={`${props.weId || ''}_fy9wux`}
                    {...commonProps}
                />
                break
            case 'displayContentAdvanced':
                resultDom = <DisplayContentAdvanced
                    weId={`${props.weId || ''}_mdyfr4`}
                    onAdvandedChange={onAdvandedChange}
                    {...commonProps}
                />
                break
        }
        return resultDom;
    }, [config, client, resProps, prefixCls])

    const templateData = useMemo(() => {
        return client === 'PC' ? getInitTempateData() : getInitTempateDataM()
    }, [client, getInitTempateData, getInitTempateDataM])

    const { template: pcTemplate, mTemplate, displayContent, split, splitColor, displayMode, optionConfig, displayContentAdvanced } = formStore.datas

    const onFormChange = useCallback((value?: FormDatas, otherParams?: any) => {
        if (value && (needSync('template', value) || needSync('mTemplate', value))) {
            const { template: pcTemplate, mTemplate } = value
            const template = client === 'PC' ? pcTemplate : mTemplate
            const showSplitline = templateData.find(t => t.id === template)?.showSplitline
            formStore.setHide('split', !showSplitline)
            formStore.setHide('splitColor', !showSplitline)
        }
        if (value && needSync('displayMode', value)) {
            const { displayMode } = value
            if (displayMode === 'normal') {
                formStore.setLayoutProps('displayContent', { items: ['displayContent'] })
                formStore.setHide('template', client !== 'PC')
                formStore.setHide('mTemplate', client === 'PC')

                const source = (displayContent as TagContentProps[]).find(item => item.id === 'source')
                const { checked } = source || {}
                formStore.setHide('sourceConfig', !checked)

                const template = client === 'PC' ? pcTemplate : mTemplate
                const showSplitline = templateData.find(t => t.id === template)?.showSplitline
                formStore.setHide('split', !showSplitline)
                formStore.setHide('splitColor', !showSplitline)
            } else {
                const { listField } = displayContentAdvanced as AnyObj || {}
                const useSource = listField?.find((field: any) => field.id === CustomFields.SOURCE)
                formStore.setLayoutProps('displayContent', { items: ['displayContentAdvanced'] })
                formStore.setHide('template', true)
                formStore.setHide('mTemplate', true)
                formStore.setHide('sourceConfig', !useSource)
                formStore.setHide('split', client !== 'PC')
                formStore.setHide('splitColor', client !== 'PC')
            }
        }
        if (value && needSync('displayContent', value)) {
            const displayContent = value.displayContent as TagContentProps[]
            const source = displayContent.find(item => item.id === 'source')
            const { checked } = source || {}
            formStore.setHide('sourceConfig', !checked)
        }
        if (value && needSync('displayContentAdvanced', value)) {
            const { listField } = value.displayContentAdvanced as AnyObj
            const useSource = listField?.find((field: any) => field.id === CustomFields.SOURCE)
            formStore.setHide('sourceConfig', !useSource)
        }
    }, [client, displayContent, pcTemplate, mTemplate, displayContentAdvanced])

    /*const commentTemplateConfig = useMemo(() => {
        if (displayMode === 'normal') {
            const currentConfig = getDisplayConfig({ displaySetting: formStore.datas }, props.page)
            return currentConfig as AnyObj
        }
        return {}
    }, [template, mTemplate, displayContent, split, splitColor, displayMode, templateData, formStore.datas, props.page])
    */
    const commentProps = useMemo(() => {
        const showOptionConfig = (displayContent as any)?.find((tag: any) => tag.id === 'optionConfig')?.checked
        const commentProps = getCommentProps({ config: { ...config, displaySetting: formStore.datas, optionConfig: showOptionConfig ? optionConfig as any : [] }, page: props.page, type: 'Comment' }, true)
        return commentProps
    }, [config, props.page, pcTemplate, mTemplate, optionConfig, splitColor, displayMode, displayContent, formStore.datas, getCommentProps])

    const advancedData: any = toJS(displayContentAdvanced) || {}
    //console.log(advancedData, props)
    //console.log(pluginStore.pluginCenter)
    //console.log(advancedData, config, config.relatedDataSource)
    const templateCls = classnames({
        [`${prefixCls}-preview-hide`]: displayMode !== 'normal'
    })
    const aTemplateCls = classnames({
        [`${prefixCls}-preview-advanced`]: client === 'PC',
        [`${prefixCls}-preview-hide`]: displayMode === 'normal',
    })
    return (
        <div className={prefixCls}>
            <div className={`${prefixCls}-form`}>
                <span className={`${prefixCls}-subtitle`}>{getLabel('312123', '显示内容')}</span>
                <Form weId={`${props.weId || ''}_eveizr`}
                    store={formStore}
                    customRenderFormSwitch={customRenderFormSwitch}
                    onChange={onFormChange}
                />
            </div>
            <div className={`${prefixCls}-preview`}>
                <span className={`${prefixCls}-subtitle`}>{getLabel('312130', '效果预览')}</span>
                <div className={templateCls}>
                    <CommentTemplate
                        weId={`${props.weId || ''}_jixudw`}
                        prefixCls={prefixCls}
                        //{...commentTemplateConfig}
                        client={client}
                        commentProps={commentProps}
                    />
                </div>
                <div className={aTemplateCls}>
                    <AdvancedTemplate
                        weId={`${props.weId || ''}_7rbf1r`}
                        prefixCls={prefixCls}
                        advancedData={advancedData}
                        onChange={onChange}
                        config={config}
                        commentProps={commentProps}
                        client={client}
                        {...resProps}
                    />
                </div>
            </div>
        </div>
    )
}

export default observer(Content);