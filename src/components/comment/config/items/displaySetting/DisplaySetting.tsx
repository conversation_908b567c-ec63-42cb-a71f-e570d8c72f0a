import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { CommentConfigProps } from "../../../types";
import { Button, Dialog, FormStore, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { commentClsPrefix } from '../../../../../constants';
import Content from './content';

interface DisplaySettingProps extends CommentConfigProps<any> {

}

const DisplaySetting: FC<DisplaySettingProps> = (props) => {
    const { weId, onChange, onConfigChange, ...resProp } = props;

    const [visible, setVisible] = useState(false)

    useEffect(() => {
        //console.log(props)
    }, [])

    const formStore = useMemo(() => new FormStore(), [visible])

    const handleOpenDialog = useCallback(() => {
        setVisible(!visible)
    }, [visible, setVisible])

    const onSure = useCallback(() => {
        const formdatas = formStore.getFormDatas()
        const { optionConfig } = formdatas
        onChange(formdatas)
        onConfigChange({ optionConfig })
        handleOpenDialog()
    }, [formStore, onChange, onConfigChange])

    const prefixCls = `${commentClsPrefix}-display-setting`

    return (
        <>
            <Icon weId={`${props.weId || ''}_xon0on`}
                name="Icon-set-up-o"
                style={{ cursor: 'pointer', color: 'var(--secondary-fc)' }}
                onClick={handleOpenDialog}
            />
            <Dialog weId={`${props.weId || ''}_szeluk`}
                title={getLabel('233629', '显示设置')}
                visible={visible}
                onClose={handleOpenDialog}
                closable
                scale
                mask
                maskClosable
                destroyOnClose
                width={850}
                icon='Icon-e-builder'
                className={prefixCls}
                height={1000}
                footer={[
                    <Button weId={`${props.weId || ''}_frzgk4@${0}`} onClick={onSure} type='primary'>{getLabel('223931', '保存')}</Button>,
                ]}
            >
                <Content
                    weId={`${props.weId || ''}_60i1g0`}
                    formStore={formStore}
                    {...resProp}
                    onChange={onChange}
                    onConfigChange={onConfigChange}
                    prefixCls={prefixCls}
                />
            </Dialog>
        </>
    )
}

export default DisplaySetting;