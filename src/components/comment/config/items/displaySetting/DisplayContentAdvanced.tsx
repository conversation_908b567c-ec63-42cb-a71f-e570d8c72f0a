import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CommentConfigProps } from "../../../types";
import { AnyObj, CorsComponent, utils } from '@weapp/ui';
import { cloneDeep, isEqual } from '@weapp/utils';
import { CustomFields, getCustomFields, getDefaultAdvancedContentData, getInitTagData } from './constants';
import { toJS } from 'mobx';
import { getCommentFormFieldsMemo, getFields } from '../../../utils/getApiDatas';
import { handleFormatSourceFields } from '../../../utils';

const { isValueEmpty } = utils
interface DisplayContentProps extends CommentConfigProps<any> {
    prefixCls: string;
    /** 用于初始切换至高级模式时，自动带入默认字段布局 */
    onAdvandedChange: (value: any) => void;
}

interface TagDataProps {
    id: string;
    content: string;
    editable: boolean;
    checked: boolean;
    disabled: boolean;
}

const DisplayContent: FC<DisplayContentProps> = (props) => {
    const { weId, prefixCls, value = {}, config, page, onChange, onConfigChange, onAdvandedChange, store, ...resProp } = props;

    const { useOriginalTableFields, itemFormId, relatedDataSource } = config

    const [commentFormData, setCommentFormData] = useState<any>(null);

    const [displayData, setDisplayData] = useState<TagDataProps[]>()

    const [commentSourceFields, setCommentSourceFields] = useState<Array<AnyObj>>([])

    useEffect(() => {
        setDisplayData(value || getInitTagData())
    }, [])

    const onListFieldChange = useCallback((data: any) => {
        onChange(data)
    }, [onChange])

    const handleCommentForm = useCallback(async () => {
        // 如果已经缓存，直接返回格式化后的值
        if (commentFormData) {
            const { commentFormObjId } = commentFormData;
            const dataset = {
                ...relatedDataSource,
                id: commentFormObjId
            }
            const sourceFields = await handleCommentFields(dataset)
            const formFieldsObj = handleFormatSourceFields(sourceFields);
            return formFieldsObj;
        }

        // 否则发请求
        if (!itemFormId) return {};
        const res = await getCommentFormFieldsMemo(itemFormId, relatedDataSource?.dataExtMemoTime);
        onConfigChange?.({ commentForm: res.data }, { skipLayoutCompare: true })
        const { commentFormObjId } = res.data || {};
        const dataset = {
            ...relatedDataSource,
            id: commentFormObjId
        }
        const sourceFields = await handleCommentFields(dataset)
        const formFieldsObj = handleFormatSourceFields(sourceFields);

        // 缓存结果
        setCommentFormData(res.data)
        return formFieldsObj;
    }, [itemFormId, commentFormData, relatedDataSource]);

    const customFields = useMemo(() => {
        const customFields = getCustomFields(page?.client)
        return customFields
    }, [page?.client, getCustomFields])

    const handleCommentFields = useCallback(async (dataset) => {
        const sourceFields = await getFields(dataset, 'field')
        setCommentSourceFields(sourceFields)
        return sourceFields
    }, [])

    const commentFieldsFilter = (field: any) => {
        const { config } = field
        // 过滤表单中的【评论内容】字段，改为自定义渲染
        const filterKeys = [CustomFields.CONTENT]
        return !filterKeys.includes(config?.dataKey)
    }

    const getCusFields = useCallback(async (comConfig: any) => {
        const handleFields = async () => {
            return getFields(comConfig.dataset, 'field')
        }
        if (useOriginalTableFields) {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm(), handleFields()]).then(([formFieldsObj, fields]: [any, any]) => {
                    const _fields = cloneDeep(fields)
                    if (_fields[0]?.fields) {
                        const fields = _fields[0]?.fields || []
                        const formFields = formFieldsObj.fields?.filter(commentFieldsFilter) || []
                        _fields[0].fields = [...fields, ...formFields, ...customFields]
                    }
                    reslove(_fields)
                })
            })
        } else {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm()]).then(([formFieldsObj]: [any]) => {
                    const _formFieldsObj = cloneDeep(formFieldsObj)
                    if (_formFieldsObj.fields) {
                        const formFields = _formFieldsObj.fields?.filter(commentFieldsFilter) || []
                        _formFieldsObj.fields = [...formFields, ...customFields]
                    }
                    reslove([_formFieldsObj])
                })
            })
        }
    }, [useOriginalTableFields, handleCommentForm])

    const getDefaultValue = useCallback(() => {
        const { commentFormFieldList } = commentFormData || {}
        if (commentFormFieldList) {
            const formFieldsObj = handleFormatSourceFields(commentSourceFields)
            const formFields = formFieldsObj.fields?.filter(commentFieldsFilter) || []
            const fields = [...formFields, ...customFields]
            const defaultValue = getDefaultAdvancedContentData(fields, page)
            return defaultValue
        }
        return {}
    }, [commentFormData, page])

    const currentValue = useMemo(() => {
        if (!isValueEmpty(value)) return value
        return getDefaultValue()
    }, [value, commentFormData])

    useEffect(() => {
        /** 初始为空，且切换至高级模式时，自动带入默认字段布局 */
        if (isValueEmpty(value) && commentFormData) {
            const defaultValue = getDefaultValue()
            onAdvandedChange(defaultValue)
        }
    }, [value, commentFormData])

    /** 更新视图 */
    const onDidUpdate = useCallback((preConfig: any, config: any) => {
        if (!isEqual(preConfig?.listField, config?.listField)) {
            return true
        }
        return false
    }, [])

    return (
        <div className={`${prefixCls}-display-content`}>
            <CorsComponent weId={`${props.weId || ''}_n1eywn`}
                app="@weapp/ebdcontainercoms"
                compName="ListField"
                value={currentValue?.listField || []} // 显示字段回显数组listField
                onConfigChange={onListFieldChange} // 回调
                config={{
                    mode: '2',
                    dataset: relatedDataSource, // 数据源
                    ...toJS(currentValue)
                }}
                store={store}
                page={page} // 页面对象，事件动作需要
                client={page?.client} // 默认PC
                getCusFields={getCusFields}
                onDidUpdate={onDidUpdate} // 是否需要初始化，返回true就重新初始化
            // canSetEventAction={false} // 关闭事件动作
            // comId={xxx} // 组件id，扩展区域情况时需要使用
            // fieldFilter={xxx} // 过滤显示字段
            // externalElement={xxx} // 自定义显示回显组件，不传默认是输入框组件
            />
        </div>
    )
}

export default DisplayContent;