import { AnyObj } from "@weapp/ui";
import { cloneDeep, getLabel } from "@weapp/utils";
import ebdcoms from "../../../../../utils/ebdcoms";
import { Page } from "@weapp/ebdcoms";

const { UUID } = ebdcoms.get();

export const getInitTagData = () => [
    { id: 'avatar', content: getLabel('312107', '评论人头像'), disabled: false, checked: true },
    { id: 'username', content: getLabel('312108', '评论人姓名'), disabled: true, checked: true },
    { id: 'department', content: getLabel('312109', '评论人部门'), disabled: false, checked: false },
    { id: 'content', content: getLabel('240119', '评论内容'), disabled: true, checked: true },
    { id: 'source', content: getLabel('208508', '评论来源'), disabled: false, checked: true },
    { id: 'time', content: getLabel('208509', '评论时间'), disabled: false, checked: true },
    { id: 'optionConfig', content: getLabel('309629', '操作按钮'), disabled: false, checked: true },
    { id: 'customField', content: getLabel('312101', '自定义字段'), disabled: false, checked: true }
]

export const getInitTempateData = () => [
    { id: '1', content: getLabel('312110', '模板') + '1', showSplitline: true, showDepartmentBelow: false },
    { id: '2', content: getLabel('312110', '模板') + '2', showSplitline: true },
    { id: '3', content: getLabel('312110', '模板') + '3', showSplitline: false },
    { id: '4', content: getLabel('312110', '模板') + '4', showSplitline: false },
    { id: '5', content: getLabel('312110', '模板') + '5', showSplitline: true },
    { id: '6', content: getLabel('312110', '模板') + '6', showSplitline: true },
    { id: '7', content: getLabel('312110', '模板') + '7', showSplitline: false },
]

export const getInitTempateDataM = () => [
    { id: '1', content: getLabel('312110', '模板') + '1', showSplitline: false },
    { id: '2', content: getLabel('312110', '模板') + '2', showSplitline: false },
    { id: '3', content: getLabel('312110', '模板') + '3', showSplitline: false },
    { id: '4', content: getLabel('312110', '模板') + '4', showSplitline: false, showDepartmentBelow: true },
    { id: '5', content: getLabel('312110', '模板') + '5', showSplitline: false, showDepartmentBelow: true },
    { id: '6', content: getLabel('312110', '模板') + '6', showSplitline: false },
    { id: '7', content: getLabel('312110', '模板') + '7', showSplitline: false },
    { id: '8', content: getLabel('312110', '模板') + '8', showSplitline: false },
    { id: '9', content: getLabel('312110', '模板') + '9', showSplitline: false, showDepartmentBelow: true },
]

export const CustomFields: AnyObj = {
    COMMENTER: 'comment_commenter',
    COMMENTER_NAME: 'comment_commenter_name', // 评论人姓名
    COMMENTER_DEPARTMENT: 'comment_commenter_department', // 评论人部门
    COMMENTER_SUB: 'comment_commenter_sub', // 评论人分部
    COMMENTER_POST: 'comment_commenter_post', // 评论人岗位
    CONTENT: 'comment_origin_content',
    PLAIN_CONTENT: 'comment_plain_content',
    SOURCE: 'comment_source',
    TIME: 'create_time',
    OPTION: 'comment_option',
    OPTIONAREA: 'comment_option_area',
    USER: 'comment_user',
    M_USER: 'comment_m_user',
}

/** 高级视图自定义字段配置 */
export const getCustomFields = (client?: string) => {
    const user = {
        text: getLabel('240120', '评论人'),
        id: CustomFields.USER,
        name: CustomFields.USER,
        objId: CustomFields.USER,
        type: 'String',
        mainField: true
    }
    const mUser = {
        text: getLabel('240120', '评论人'),
        id: CustomFields.M_USER,
        name: CustomFields.M_USER,
        objId: CustomFields.M_USER,
        type: 'String',
        mainField: true
    }
    const fields = [
        {
            text: getLabel('312107', '评论人头像'),
            id: CustomFields.COMMENTER,
            name: CustomFields.COMMENTER,
            objId: CustomFields.COMMENTER,
            type: 'Employee',
            mainField: true
        },
        {
            text: getLabel('312108', '评论人姓名'),
            id: CustomFields.COMMENTER_NAME,
            name: CustomFields.COMMENTER_NAME,
            objId: CustomFields.COMMENTER_NAME,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('312109', '评论人部门'),
            id: CustomFields.COMMENTER_DEPARTMENT,
            name: CustomFields.COMMENTER_DEPARTMENT,
            objId: CustomFields.COMMENTER_DEPARTMENT,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('312113', '评论人分部'),
            id: CustomFields.COMMENTER_SUB,
            name: CustomFields.COMMENTER_SUB,
            objId: CustomFields.COMMENTER_SUB,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('312114', '评论人岗位'),
            id: CustomFields.COMMENTER_POST,
            name: CustomFields.COMMENTER_POST,
            objId: CustomFields.COMMENTER_POST,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('240119', '评论内容'),
            id: CustomFields.CONTENT,
            name: CustomFields.CONTENT,
            objId: CustomFields.CONTENT,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('208508', '评论来源'),
            id: CustomFields.SOURCE,
            name: CustomFields.SOURCE,
            objId: CustomFields.SOURCE,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('208509', '评论时间'),
            id: CustomFields.TIME,
            name: CustomFields.TIME,
            objId: CustomFields.TIME,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('309629', '操作按钮'),
            id: CustomFields.OPTION,
            name: CustomFields.OPTION,
            objId: CustomFields.OPTION,
            type: 'String',
            mainField: true
        },
        {
            text: getLabel('309630', '评论操作区域'),
            id: CustomFields.OPTIONAREA,
            name: CustomFields.OPTIONAREA,
            objId: CustomFields.OPTIONAREA,
            type: 'String',
            mainField: true
        }
    ]
    if (client === 'PC') {
        fields.unshift(user)
    } else {
        fields.unshift(mUser)
    }
    return fields
}

/** 获取默认的高级模式字段布局 */
export const getDefaultAdvancedContentData = (fields: any, page?: Page) => {
    const handleField = (fields: any, id: string) => {
        const uid = UUID()
        const field = fields.find((f: any) => f.id === id) || {}
        delete field['config']
        const listField = cloneDeep(field)
        delete listField['browserParams']
        return {
            field: {
                ...field,
                uid
            },
            listField: {
                ...listField,
                uid
            }
        }
    }
    const comment_commenter = handleField(fields, CustomFields.COMMENTER)
    const comment_commenter_name = handleField(fields, CustomFields.COMMENTER_NAME)
    const comment_content = handleField(fields, CustomFields.CONTENT)
    const comment_source = handleField(fields, CustomFields.SOURCE)
    const comment_time = handleField(fields, CustomFields.TIME)
    const comment_option = handleField(fields, CustomFields.OPTION)
    const comment_option_area = handleField(fields, CustomFields.OPTIONAREA)

    const comment_m_user = handleField(fields, CustomFields.M_USER)

    if (page?.client === 'PC') {
        return {
            "cardLayout": {
                "split": [
                    {
                        "index": 0,
                        "id": UUID(),
                        "setting": {
                            "width": {
                                "type": "left",
                                "value": "80"
                            }
                        },
                        "type": "1"
                    },
                    {
                        "index": 1,
                        "setting": {
                            "width": {
                                "type": "",
                                "value": ""
                            }
                        },
                        "id": UUID(),
                        "type": "0"
                    }
                ],
                "grid": [
                    [
                        [
                            {
                                "x": 0,
                                "y": 0,
                                "field": {
                                    ...comment_commenter.field,
                                    "mainPrimaryKey": false,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        0,
                                        0,
                                        0,
                                        0
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {},
                                    "autoFocus": false
                                },
                                "i": UUID()
                            }
                        ]
                    ],
                    [
                        [
                            {
                                "x": 0,
                                "y": 0,
                                "field": {
                                    ...comment_commenter_name.field,
                                    "mainPrimaryKey": false,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        0,
                                        12,
                                        0,
                                        0
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {},
                                    "autoFocus": false
                                },
                                "i": UUID()
                            }
                        ],
                        [
                            {
                                "x": 0,
                                "y": 1,
                                "field": {
                                    ...comment_content.field,
                                    "mainPrimaryKey": false,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "2",
                                        "value": 100,
                                        "unit": "%"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        0,
                                        0,
                                        0,
                                        0
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {},
                                    "autoFocus": false
                                },
                                "i": UUID()
                            }
                        ],
                        [
                            {
                                "x": 0,
                                "y": 2,
                                "field": {
                                    ...comment_source.field,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        "",
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {
                                        "color": "#999999"
                                    },
                                    "autoFocus": false,
                                    "mainPrimaryKey": false,
                                },
                                "i": UUID()
                            },
                            {
                                "x": 1,
                                "y": 2,
                                "field": {
                                    ...comment_time.field,
                                    "browserParams": {},
                                    "mainField": true,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        "",
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {
                                        "color": "#999999"
                                    },
                                    "autoFocus": false,
                                    "mainPrimaryKey": false,
                                },
                                "i": UUID()
                            },
                            {
                                "x": 2,
                                "y": 2,
                                "field": {
                                    ...comment_option.field,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        "",
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {
                                        "color": "#999999"
                                    },
                                    "autoFocus": false,
                                    "mainPrimaryKey": false,
                                },
                                "i": UUID()
                            }
                        ],
                        [
                            {
                                "x": 0,
                                "y": 3,
                                "field": {
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "2",
                                        "value": 100,
                                        "unit": "%"
                                    },
                                    "wordWrap": true,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        "",
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {
                                        "color": "#666666"
                                    },
                                    "autoFocus": false,
                                    ...comment_option_area.field,
                                    "mainPrimaryKey": false,
                                },
                                "i": UUID()
                            }
                        ]
                    ]
                ],
                "size": [
                    [
                        1,
                        1
                    ],
                    [
                        4,
                        3
                    ]
                ],
                "row": [
                    [
                        {
                            "index": 0,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        }
                    ],
                    [
                        {
                            "index": 0,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        },
                        {
                            "index": 1,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        },
                        {
                            "index": 2,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        },
                        {
                            "index": 3,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        }
                    ]
                ]
            },
            "listField": [
                {
                    ...comment_commenter.listField,
                    "compType": "Employee",
                    "type": "Employee",
                    "mainPrimaryKey": false,
                    "fieldVersion": 1
                },
                {
                    ...comment_commenter_name.listField,
                    "mainPrimaryKey": false,
                    "type": "String",
                    "compType": "Text"
                },
                {
                    ...comment_content.listField,
                    "mainPrimaryKey": false,
                    "type": "TextArea",
                    "compType": "TextArea"
                },
                {
                    ...comment_source.listField,
                    "mainPrimaryKey": false,
                    "type": "String",
                    "compType": "Text"
                },
                {
                    ...comment_time.listField,
                    "mainPrimaryKey": false,
                    "type": "Date",
                    "compType": "DateComponent"
                },
                {
                    ...comment_option.listField,
                    "mainPrimaryKey": false,
                    "type": "String",
                    "compType": "Text"
                },
                {
                    ...comment_option_area.listField,
                    "mainPrimaryKey": false,
                    "type": "String",
                    "compType": "Text"
                }
            ],
            "uaLanguage": "zh_CN"
        }
    } else if (page?.client === 'MOBILE') {
        return {
            "cardLayout": {
                "split": [
                    {
                        "index": 0,
                        "setting": {
                            "width": {
                                "type": "",
                                "value": ""
                            }
                        },
                        "id": UUID(),
                        "type": "0"
                    }
                ],
                "grid": [
                    [
                        [
                            {
                                "x": 0,
                                "y": 0,
                                "field": {
                                    ...comment_commenter.field,
                                    "mainPrimaryKey": false,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        0,
                                        12,
                                        0,
                                        0
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {},
                                    "autoFocus": false
                                },
                                "i": UUID()
                            },
                            {
                                "x": 1,
                                "y": 0,
                                "field": {
                                    ...comment_m_user.field,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        0,
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {},
                                    "autoFocus": false,
                                    "mainPrimaryKey": false,
                                },
                                "i": UUID()
                            }
                        ],
                        [
                            {
                                "x": 0,
                                "y": 1,
                                "field": {
                                    ...comment_content.field,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "2",
                                        "value": 100,
                                        "unit": "%"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        0,
                                        0,
                                        0,
                                        0
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {},
                                    "autoFocus": false,
                                    "mainPrimaryKey": false,
                                    "eventGroup": []
                                },
                                "i": UUID()
                            }
                        ],
                        [
                            {
                                "x": 0,
                                "y": 2,
                                "field": {
                                    ...comment_source.field,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        "",
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {
                                        "color": "#999999"
                                    },
                                    "autoFocus": false,
                                    "mainPrimaryKey": false
                                },
                                "i": UUID()
                            },
                            {
                                "x": 1,
                                "y": 2,
                                "field": {
                                    ...comment_time.field,
                                    "mainField": true,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        "",
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {
                                        "color": "#999999"
                                    },
                                    "autoFocus": false,
                                    "mainPrimaryKey": false
                                },
                                "i": UUID()
                            }
                        ],
                        [
                            {
                                "x": 0,
                                "y": 3,
                                "field": {
                                    ...comment_option.field,
                                    "showTrans": [],
                                    "isTransAvatar": false,
                                    "width": {
                                        "type": "1",
                                        "value": "",
                                        "unit": "px"
                                    },
                                    "wordWrap": false,
                                    "horAlign": "0",
                                    "align": "left",
                                    "minWidth": "80",
                                    "padding": [
                                        "0",
                                        "",
                                        "0",
                                        "0"
                                    ],
                                    "image": {
                                        "showType": "inline",
                                        "previewable": true,
                                        "displayType": "equalScaleCut",
                                        "h": "auto",
                                        "w": "auto"
                                    },
                                    "overFlow": {
                                        "number": "",
                                        "type": "line"
                                    },
                                    "style": {
                                        "color": "#999999"
                                    },
                                    "autoFocus": false,
                                    "mainPrimaryKey": false
                                },
                                "i": UUID()
                            }
                        ]
                    ]
                ],
                "size": [
                    [
                        4,
                        3
                    ]
                ],
                "row": [
                    [
                        {
                            "index": 0,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        },
                        {
                            "index": 1,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        },
                        {
                            "index": 2,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        },
                        {
                            "index": 3,
                            "isHide": false,
                            "setting": {
                                "height": "",
                                "alignItems": "center"
                            }
                        }
                    ]
                ]
            },
            "listField": [
                {
                    ...comment_commenter.listField,
                    "mainPrimaryKey": false,
                    "type": "Employee",
                    "fieldVersion": 1
                },
                {
                    ...comment_m_user.listField,
                    "mainPrimaryKey": false,
                    "type": "String"
                },
                {
                    ...comment_content.listField,
                    "mainPrimaryKey": false,
                    "type": "TextArea",
                    "compType": "TextArea"
                },
                {
                    ...comment_source.listField,
                    "mainPrimaryKey": false,
                    "type": "String",
                    "compType": "Text"
                },
                {
                    ...comment_time.listField,
                    "mainPrimaryKey": false,
                    "type": "Date",
                    "compType": "DateComponent"
                },
                {
                    ...comment_option.listField,
                    "mainPrimaryKey": false,
                    "type": "String",
                    "compType": "Text"
                }
            ],
            "uaLanguage": "zh_CN"
        }
    }
}
