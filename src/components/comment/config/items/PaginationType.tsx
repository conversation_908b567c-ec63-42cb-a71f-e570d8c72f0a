import { Select, SelectOptionData } from "@weapp/ui";
import { FC, memo, useCallback, useMemo } from "react";
import { classnames, getLabel } from "@weapp/utils";
import { commentClsPrefix } from "../../../../constants";
import { CommentConfigProps } from "../../types";
import { getPaginationType } from "../../utils/initData";

interface PaginationTypeProps extends CommentConfigProps<string> {
}

const prefixCls = commentClsPrefix;

const PaginationType: FC<PaginationTypeProps> = memo((props) => {
  const { page, className, value, onChange } = props;
  const { client } = page || {};

  const data: SelectOptionData[] = useMemo(() => {
    const result = [{
      id: "scroll",
      content: getLabel("206999", "滚动分页"),
      hide: client === "PC",
    }, {
      id: "more",
      content: getLabel("207000", "点击加载更多"),
    }, {
      id: "part",
      content: getLabel("223902", "页码分页"),
      hide: client === "MOBILE",
    }]

    return result.filter(item => !item.hide) as SelectOptionData[];
  }, [client])

  const handleChange = useCallback((value: any) => {
    onChange?.(value);
  }, [onChange])

  const cls = classnames(`${prefixCls}-config-pagination-type`, className);

  const defaultValue = getPaginationType({ client });

  return (
    <Select
      weId={`${props.weId || ''}_eyd6lf`}
      className={cls}
      value={value || defaultValue}
      onChange={handleChange}
      data={data}
    />
  )
})

export default PaginationType;