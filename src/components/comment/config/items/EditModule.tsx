import { FC, useCallback, useMemo } from 'react';
import { Select, SelectValueType } from "@weapp/ui";
import { getLabel } from '@weapp/utils';
import { CommentConfigProps } from "../../types";

interface EditModuleProps extends CommentConfigProps<any> {
  form?: any
}

const data = [{
  id: "show",
  content: getLabel("206983", "显示"),
}, {
  id: "hidden",
  content: getLabel("206984", "隐藏"),
}]

const EditModule: FC<EditModuleProps> = (props) => {
  const { className, onChange, form, value } = props;

  const handleChange = useCallback((value: SelectValueType) => {
    onChange(value)
  }, [onChange])

  const componentShowType = form.datas.componentShowType

  return (
    <Select weId={`${props.weId || ''}_kvx6ko`}
      value={value}
      className={className}
      data={data}
      onChange={handleChange}
      disabled={componentShowType === 'input'}
    />
  )
}

export default EditModule;