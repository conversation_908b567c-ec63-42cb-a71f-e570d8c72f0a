import { FC, useCallback } from 'react';
import { Switch } from "@weapp/ui";
import { CommentConfigProps } from "../../types";

interface ShowTextProps extends CommentConfigProps<any> {
  form?: any
}

const ShowText: FC<ShowTextProps> = (props) => {
  const { className, onConfigChange, form, value, client } = props;

  const handleChange = useCallback((value: boolean) => {
    const { datas } = form

    onConfigChange?.({
      showEditAvatar: (value && client === 'MOBILE') ? false : datas.showEditAvatar,
      showText: value
    });
  }, [onConfigChange])

  return (
    <Switch weId={`${props.weId || ''}_t4z2w2`}
            value={value}
            className={className}
            onChange={handleChange}
    />
  )
}

export default ShowText;