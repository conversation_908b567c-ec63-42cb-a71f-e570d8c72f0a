import { getLabel } from "@weapp/utils";
import { FC, ReactNode, useCallback, useEffect, useMemo, useState } from "react"
import DialogPlus from "../../../../../ui-plus/dialog-plus";
import { AnyObj, Button, FormStore, utils } from "@weapp/ui";
import { SettingContent } from "./SettingContent";
import { CommentConfigProps, CommonConfigData, EnableModuleType } from "../../../types";
import { commentClsPrefix } from "../../../../../constants";
import { ReplyActionConfigOption } from "./contants";
import { initActionConfig } from "../../../utils/initData";

const { deepClone } = utils

const prefixCls = commentClsPrefix;

export interface ReplySettingProps extends CommentConfigProps {
  weId: string;
  /** 自定义渲染设置 */
  customRender?: (props: AnyObj) => ReactNode;
  /** 设置数据改变事件 */
  onSettingDataChange?: (value: AnyObj) => void;
}

export const Setting: FC<ReplySettingProps> = (props) => {

  const { customRender, weId, onSettingDataChange, value, client, ...resProps } = props

  const [visible, setVisible] = useState(false);

  /** 回复配置是否同评论输入框 */
  const isSame = useMemo(() => {
    const optionVal = value?.replyActionConfig?.optionVal
    return optionVal !== ReplyActionConfigOption.DIFF
  }, [value?.replyActionConfig?.optionVal])

  const defaultTableData: CommonConfigData[] = useMemo(() => {
    if (isSame) {
      // 同评论输入框，将输入框配置带入
      const { actionConfig } = props.config || {}
      return deepClone(actionConfig) || initActionConfig({ client: client! })
    }
    return initActionConfig({ client: client! });
  }, [client, isSame, props.config])

  const defaultEnableModule = useMemo(() => {
    if (isSame) {
      return props.config?.enableModule
    }
    return undefined
  }, [isSame, props.config])

  const defaultConfig = useMemo(() => {
    const {
      useRichText,
      usePrivated,
      usePosition,
      usePhrase,
      useSignApprove,
      useSignature,
      useAnonymous,
      allowAnonymousSetting,
      defaultAnonymousByCheck
    } = props.config || {}
    if (isSame) {
      // 同评论输入框，将输入框配置带入
      return {
        useRichText,
        usePrivated,
        usePosition,
        usePhrase,
        useSignApprove,
        useSignature,
        useAnonymous,
        allowAnonymousSetting,
        defaultAnonymousByCheck
      }
    }
    return {}
  }, [isSame, props.config])

  const [tableValue, setTableValue] = useState<CommonConfigData[]>()

  const [associativeValue, setAssociativeValue] = useState<EnableModuleType[] | undefined>()

  useEffect(() => {
    setTableValue(value?.replyActionConfig?.configData || defaultTableData)
    setAssociativeValue(value?.replyActionConfig?.enableModule || defaultEnableModule)
  }, [visible])

  const resultValue = useMemo(() => {
    const result = {
      ...defaultConfig,
      ...(value || {}),
      replyActionConfig: {
        optionVal: value?.replyActionConfig?.optionVal,
        configData: value?.replyActionConfig?.configData ?? defaultTableData,
        enableModule: value?.replyActionConfig?.enableModule ?? defaultEnableModule
      }
    }
    return result
  }, [value, defaultConfig, defaultTableData, defaultEnableModule])

  const formStore = useMemo(() => new FormStore(), [visible]);

  const handleClose = useCallback(() => {
    setVisible(false);
  }, [])

  const handleOpen = useCallback(() => {
    setVisible(true);
  }, [])

  const handleSave = useCallback(() => {
    const { getFormDatas } = formStore;
    const formdata = getFormDatas();
    const { replyActionConfig = {} } = formdata

    const value = {
      ...(replyActionConfig.optionVal === ReplyActionConfigOption.DIFF ? formdata : {}),
      replyActionConfig: {
        optionVal: replyActionConfig.optionVal || ReplyActionConfigOption.SAME,
        configData: replyActionConfig.optionVal === ReplyActionConfigOption.DIFF ? tableValue : undefined,
        enableModule: replyActionConfig.optionVal === ReplyActionConfigOption.DIFF ? associativeValue : undefined,
      }
    }
    onSettingDataChange?.(value)
    setVisible(false);
  }, [formStore, tableValue, associativeValue, onSettingDataChange])

  const footer = (
    <Button
      weId={`${props.weId || ''}_eeb2uv`}
      type="primary"
      onClick={handleSave}
    >
      {getLabel("223931", "保存")}
    </Button>
  )

  const onCustomChange = useCallback((value: CommonConfigData[]) => {
    setTableValue(value)
  }, [])

  const customHandleAssociativeChange = useCallback((value?: EnableModuleType[]) => {
    setAssociativeValue(value)
  }, [])

  const [dialogWidth, setDialogWidth] = useState(950)

  /** 获取表格宽度 */
  const onTableWidthChange = useCallback((width: number) => {
    setDialogWidth(width)
  }, [weId]);

  return (
    <>
      {customRender?.({
        onClick: handleOpen
      })}
      <DialogPlus
        weId={`${props.weId || ''}_bgumm5`}
        icon="Icon-e-builder"
        onClose={handleClose}
        visible={visible}
        title={getLabel("266049", "评论回复设置")}
        destroyOnClose
        footer={footer}
        closable
        scale
        width={dialogWidth}
        className={`${prefixCls}-config-option-config`}
      >
        <SettingContent
          weId={`${props.weId || ''}_aowdwy`}
          {...resProps}
          client={client}
          value={resultValue}
          config={{ enableModule: associativeValue }}
          onCustomChange={onCustomChange}
          customHandleAssociativeChange={customHandleAssociativeChange}
          formStore={formStore}
          onTableWidthChange={onTableWidthChange}
        />
      </DialogPlus>
    </>
  )
}