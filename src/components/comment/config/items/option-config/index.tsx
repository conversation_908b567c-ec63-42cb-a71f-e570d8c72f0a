import { FC, memo, useCallback, useMemo } from "react";
import { initOptionConfig } from "../../../utils/initData";
import { CommentConfigProps } from "../../../types";
import CommonConfig from "../common-config";
import { CommonConfigData } from "../../../types";
import { AnyObj, Icon, Select, utils } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import ButtonPlus from "../../../../../ui-plus/buttons-plus";
import { defColumnConfig } from "../common-config/constant";
import { Setting } from "./Setting";
import { TopSetting } from './TopSetting'

const { deepClone } = utils

const reverseKeys = ['customName', 'customMessage', 'name', 'iconName']

interface OptionConfigProps extends CommentConfigProps<CommonConfigData[]> {
}

const settingNamse = ["reply", "pinToTop", "sort"];

export const sortOptions = ["sort"]

export const designHideOptions = ["unpin"]

const OptionConfig: FC<OptionConfigProps> = memo((props) => {
  const { value, weId, page, ...resProps } = props;

  const { client } = page || {};

  const getOptionConfig = useCallback(() => {
    const initData = initOptionConfig({ client })
    const data = [...(value || [])]
    // 新增的配置项添加到最后
    initData.forEach(item => {
      if (!value?.find(_item => item.id === _item.id)) {
        data.push(item)
      }
    })
    return data
  }, [client, value, initOptionConfig])

  const initValue: CommonConfigData[] = useMemo(() => {
    const { replyUnderComment } = props.config || {}
    // 需要处理历史数据
    const data = getOptionConfig()
    const result = data.filter((item) => {
      let filter = true;
      switch (item.id) {
        case "sort":
          {
            filter = replyUnderComment;
            break;
          }
        default: {
          filter = true;
        }
      }
      return filter;
    })
    return result
  }, [client, value, props.config?.replyUnderComment])

  const customRenderBtn = useCallback((_props: AnyObj) => {
    return (
      <ButtonPlus
        weId={`${props.weId || ''}_ippgmw`}
        type="link"
        {..._props}
      >
        {getLabel("223930", "设置")}
      </ButtonPlus>
    )
  }, [])

  /** 设置值改变事件 */
  const onSettingDataChange = useCallback((id: string, totalData: AnyObj, handleDataChange: any) => (val: any) => {
    const _totalData = deepClone(totalData) || []
    if (Array.isArray(_totalData)) {
      const option = _totalData.find((option: any) => option.id === id)
      if (option) {
        switch (id) {
          case "sort":
            // 如果是排序，还需要联动显示顺序
            option.settingData = Array.isArray(option.settingData) ? option.settingData.reverse?.() : option.settingData
            //option.id = val
            reverseKeys.forEach((key: string) => {
              if (Array.isArray(option[key])) option[key].reverse?.()
            })
            break
          default:
            option.settingData = val
            break
        }
        handleDataChange(_totalData)
      }
    }
  }, [props.weId])

  /** 自定义渲染设置 */
  const customRenderOperation = useCallback((data: AnyObj, pos: number, name: string, totalData: AnyObj, handleDataChange: any) => {

    const { id } = data;

    if (!settingNamse.includes(id)) {
      return null;
    }

    if (['sort'].includes(id)) {
      const { settingData } = data
      return <Select weId={`${props.weId || ''}_vnl5yz`}
        value={settingData[0]}
        data={[
          { id: 'asc', content: getLabel('265581', '默认正序') },
          { id: 'desc', content: getLabel('265582', '默认倒序') }
        ]}
        onChange={onSettingDataChange(id, totalData, handleDataChange)}
      />
    }

    if (id === 'reply') {
      const { weId, value, ...resProps } = props
      const { settingData } = data
      return <Setting weId={`${props.weId || ''}_sjdxce`}
        {...resProps}
        onSettingDataChange={onSettingDataChange(id, totalData, handleDataChange)}
        value={settingData}
        customRender={customRenderBtn}
      />
    }

    if (id === 'pinToTop') {
      const { weId, value, ...resProps } = props
      const { settingData } = data
      return <TopSetting weId={`${props.weId || ''}_gc99y1`}
        {...resProps}
        customRender={customRenderBtn}
      />
    }

    return (
      <ButtonPlus
        weId={`${props.weId || ''}_pc54n5`}
        type="link"
      >
        {getLabel("223930", "设置")}
      </ButtonPlus>
    )
  }, [props.weId, props.config])

  const columnConfig = useMemo(() => defColumnConfig.filter(key => {
    let filter = true;
    switch (key) {
      case "operation": {
        //filter = false;
        break;
      }
      case "customMessage": {
        filter = client === "PC";
        break;
      }
      default: {
        filter = true;
      }
    }
    return filter;
  }), [client])

  return (
    <CommonConfig
      weId={`${props.weId || ''}_v6gp0m`}
      {...resProps}
      page={page}
      columnConfig={columnConfig}
      value={initValue}
      title={getLabel("223907", "配置评论操作按钮")}
      customRenderOperation={customRenderOperation}
      multipleOptions={sortOptions}
      description={(
        <>
          <span>
            {getLabel("223903", "说明：最多显示{0}个按钮，超出时最后一个位置显示", [client === "PC" ? "5" : "4"])}
          </span>
          <Icon
            weId={`${props.weId || ''}_u0opry`}
            name="Icon-more03"
            size="md"
          />
          <span>
            {getLabel('288377', '，点击可查看更多操作。')}
          </span>
        </>
      )}
    />
  )
})

export default OptionConfig;