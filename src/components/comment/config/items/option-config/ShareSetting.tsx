import { Icon, Share, ShareListStore } from "@weapp/ui"
import { FC, useCallback, useEffect, useMemo, useState } from "react"
import { commentClsPrefix } from "../../../../../constants";
import { getLabel, request, RequestOptionConfig, RequestPromise } from "@weapp/utils";
import { CommentConfigType } from "../../../types";
import { addConfig, deleteConfig, detailConfig, editConfig, listConfig } from "./contants";
import { moduleData } from "../../../constant";

const prefixCls = `${commentClsPrefix}-optionconfig-topsetting`

export interface ShareSettingProps {
    weId: string;
    value?: any;
    onChange?: any;
    config: CommentConfigType;
    total?: number;
    onTotalChange?: (total: number) => void;
}

export const ShareSetting: FC<ShareSettingProps> = (props) => {

    const { weId, config, total, onTotalChange, ...resProps } = props

    const { relatedDataSource } = config
    const module = relatedDataSource ? moduleData.ebuilderformObjectModule : moduleData.bcwModule

    const [visible, setVisible] = useState(false);

    const shareListStore = useMemo(() => {
        const store = new ShareListStore()
        return store
    }, [visible])

    const fetchGetData = useCallback((options: RequestOptionConfig) => {
        return new Promise((resolve) => {
            request(options).then(res => {
                if (res.code === 200) {
                    const { total } = res.data || {}
                    if (total > -1) onTotalChange?.(total)
                }
                resolve(res)
            })
        }) as RequestPromise
    }, [onTotalChange])

    useEffect(() => {
        const { init } = shareListStore
        // 列表数据接口
        const listConfigResult: RequestOptionConfig = {
            ...listConfig({ module }),
            params: {
                current: 1,
                pageSize: 10,
                sourceId: config.targetId,
                id: config.targetId
            }
        }

        init({
            listConfig: listConfigResult,
            deleteConfig: deleteConfig({ module }),
            addConfig: addConfig({ module }),
            detailConfig: detailConfig({ module }),
            editConfig: editConfig({ module }),
            sortable: true,
            topVisible: true,
            fetchGetData
        })
    }, [shareListStore])

    const onClose = useCallback((visible: boolean) => {
        setVisible(visible)
    }, [setVisible])

    const onOepn = useCallback(() => {
        setVisible(true)
    }, [setVisible])

    return (
        <Share
            weId={`${props.weId || ''}_m9qti7`}
            visible={visible}
            onClose={onClose}
            module={module}
            permissionId={'comment_top'}
            permissionType={1}
            id={config.targetId}
            sourceType={1}
            store={shareListStore}
            dialogProps={{
                title: getLabel('267979', '可见范围')
            }}
        >
            <Icon weId={`${props.weId || ''}_mcr43d`} className={`${prefixCls}-form-share-icon`} name={total && total > 0 ? 'Icon-set-up02-mcolor' : 'Icon-set-up02'} onClick={onOepn} />
        </Share>
    )
}