import {
  Form, FormDatas, FormGroupProps, FormItemProps, FormLayoutType, FormStore, FormSwitchProps, Select, Switch,
} from '@weapp/ui';
import { getLabel } from "@weapp/utils";
import { FC, useCallback, useEffect, useState } from "react";
import ActionConfig from "../action-config";
import { CommentConfigProps, CommonConfigData, EnableModuleType } from "../../../types";
import { ReplyActionConfigOption } from "./contants";
import { IconFont } from '@weapp/ebdcoms';
import { AnonymousSettingDialog } from '../anonymous-config';

interface ReplyActionConfigData {
    /** 回复输入框操作按钮 */
    optionVal?: string;
    /** 回复输入框操作按钮，单独配置-具体值 */
    configData?: CommonConfigData[];
    /** 启用模块 */
    enableModule?: EnableModuleType[];
}

interface ReplySettingContentData {
    /** 回复设置数据 */
    replyActionConfig?: ReplyActionConfigData;
}

export interface ReplySettingContentProps extends CommentConfigProps<ReplySettingContentData> {
    weId: string;
    formStore: FormStore;
    onChange: (value: any, act?: any) => void;
    /** 启用模块值改变事件 */
    customHandleAssociativeChange?: (data?: EnableModuleType[]) => void;
}

const mobileShowKeys = ['useRichText', 'usePrivated', 'useAnonymous', 'usePosition', 'usePhrase', 'useSignApprove', 'useSignature']
const pcShowKeys = ['useRichText', 'usePrivated', 'useAnonymous']

export const SettingContent: FC<ReplySettingContentProps> = (props) => {
    const [visible, setVisible] = useState(false);
    const { formStore, weId, onTableWidthChange, value, onCustomChange, customHandleAssociativeChange, ...resProps } = props

    useEffect(() => {
        const { initForm, isFormInit } = formStore;
        const datas = {
            ...(value || {})
        };
        if (!isFormInit) {
            const items: FormItemProps = {
                'useRichText': {
                    itemType: 'SWITCH',
                    value: false
                },
                'usePrivated': {
                    itemType: 'SWITCH',
                    value: false
                },
                'useAnonymous': {
                  itemType: 'CUSTOM',
                },
                'allowAnonymousSetting': {
                  itemType: "SWITCH",
                  value: false
                },
                'defaultAnonymousByCheck': {
                  itemType: "SWITCH",
                  value: false
                },
                'usePosition': {
                    itemType: "SWITCH",
                    value: false
                },
                'usePhrase': {
                    itemType: "SWITCH",
                    value: false
                },
                'useSignApprove': {
                    itemType: "SWITCH",
                    value: false
                },
                'useSignature': {
                    itemType: "SWITCH",
                    value: false
                },
                'replyActionConfig': {
                    itemType: 'CUSTOM',
                }
            }
            const layout: FormLayoutType[] = [
                [{
                    id: 'replyActionConfig',
                    label: getLabel('266072', '回复输入框操作按钮'),
                    items: ['replyActionConfig'],
                    groupId: '',
                    hide: false
                }],
                [{
                    id: 'useRichText',
                    label: getLabel('206987', '启用富文本'),
                    items: ['useRichText'],
                    groupId: '',
                    hide: true,
                }],
                [{
                    id: 'usePrivated',
                    label: getLabel('206989', '启用私评'),
                    items: ['usePrivated'],
                    groupId: '',
                    hide: true,
                }],
                [{
                  id: 'useAnonymous',
                  label: getLabel('288127', '启用匿名'),
                  items: ['useAnonymous'],
                  groupId: '',
                  hide: true,
                }],
                [{
                  id: 'allowAnonymousSetting',
                  label: getLabel('288129','匿名设置'),
                  items: ['allowAnonymousSetting'],
                  groupId: '',
                  hide: true,
                }],
                [{
                  id: 'defaultAnonymousByCheck',
                  label: getLabel('261575','默认启用'),
                  items: ['defaultAnonymousByCheck'],
                  groupId: '',
                  hide: true,
                }],
                [{
                    id: 'usePosition',
                    label: getLabel("223927", "启用位置"),
                    items: ['usePosition'],
                    groupId: '',
                    hide: true
                }],
                [{
                    id: 'usePhrase',
                    label: getLabel("223928", "启用常用短语"),
                    items: ['usePhrase'],
                    groupId: '',
                    hide: true
                }],
                [{
                    id: 'useSignApprove',
                    label: getLabel("244069", "启用手写签批"),
                    items: ['useSignApprove'],
                    groupId: '',
                    hide: true
                }],
                [{
                    id: 'useSignature',
                    label: getLabel("244068", "启用电子签名"),
                    items: ['useSignature'],
                    groupId: '',
                    hide: true
                }]
            ]

            const groups: FormGroupProps[] = [];

            initForm({
                layout,
                items,
                groups,
                data: datas,
            })

            cascadeTrigger()
        }
    })

    /** 自定义联动规则 */
    const cascadeTrigger = useCallback(() => {
        const { replyActionConfig } = formStore.getFormDatas()
        const { optionVal = ReplyActionConfigOption.SAME } = replyActionConfig || {}
        switch (optionVal) {
            case ReplyActionConfigOption.SAME:
                mobileShowKeys.forEach(key => {
                    formStore.setLayoutProps(key, { hide: true })
                })
                break
            case ReplyActionConfigOption.DIFF:
                if (props.client === 'PC') {
                    pcShowKeys.forEach(key => {
                        formStore.setLayoutProps(key, { hide: false })
                    })
                } else {
                    mobileShowKeys.forEach(key => {
                        formStore.setLayoutProps(key, { hide: false })
                    })
                }
                break
        }
    }, [formStore, props.client])

    const handleChange = useCallback((value: FormDatas = {}) => {
        const { updateDatas } = formStore;
        updateDatas({ ...value });
        cascadeTrigger()
    }, [formStore])

    const handleAnonymousChange = useCallback((value: boolean) => {
      const { updateDatas } = formStore;
      updateDatas({ useAnonymous: value });
    }, [formStore])

    const [actionConfigVisible, setActionConfigVisible] = useState(value?.replyActionConfig?.optionVal === ReplyActionConfigOption.DIFF)

    const onReplyActionConfigChange = useCallback((onChange, value) => (val: any) => {
        onChange({
            ...(value || {}),
            optionVal: val
        })
        setActionConfigVisible(val === ReplyActionConfigOption.DIFF)
    }, [])

    const onIconFontClick = useCallback(() => setVisible(true), [])
    const onVisibleChange = useCallback((value: boolean) => setVisible(value), [])

    const onSettingChange = useCallback((value: any) => {
      const { updateDatas } = formStore;
      updateDatas(value);
      setVisible(false);
    }, [formStore])

    const customRenderFormSwitch = useCallback((key: string, props: FormSwitchProps) => {
        const comProps = (props as any).props;
        const { onChange, value } = comProps;

        let resultDom = <></>
        switch (key) {
            case 'replyActionConfig':
                resultDom = <Select weId={`${props.weId || ''}_2h6njs`}
                    data={[
                        { id: ReplyActionConfigOption.SAME, content: getLabel('266070', '同评论输入框') },
                        { id: ReplyActionConfigOption.DIFF, content: getLabel('266071', '单独配置') }
                    ]}
                    value={value?.optionVal || ReplyActionConfigOption.SAME}
                    onChange={onReplyActionConfigChange(onChange, value)}
                />
                break
            case 'useAnonymous':
              resultDom = (
                <div className={'ebcoms-config-titleConfig'} style={{ justifyContent: 'start' }}>
                  <Switch weId={`${props.weId || ''}_ajgmka`}
                          value={value}
                          onChange={handleAnonymousChange}
                  />
                  {value && (
                    <IconFont weId={`${props.weId || ''}_ins7uu`} 
                              name="Icon-set-up-o"
                              title={getLabel('288130', '配置相关功能')}
                              placement="bottomRight"
                              onClick={onIconFontClick}
                              size={'md'}
                    />)}
                  <AnonymousSettingDialog
                    weId={`${weId || ''}_zwtj3h`}
                    key={'replyActionConfig'}
                    {...resProps}
                    form={formStore}
                    visible={visible}
                    onVisibleChange={onVisibleChange}
                    onChange={onSettingChange}
                  />
                </div>
              )
              break;
        }
      return resultDom;
    }, [visible])

  return (
    <>
      <Form weId={`${props.weId || ''}_syw7ke`}
            store={formStore}
            customRenderFormSwitch={customRenderFormSwitch}
            onChange={handleChange}
      />
      {actionConfigVisible && <ActionConfig weId={`${props.weId || ''}_fpor0o`}
                                            {...resProps}
        /** inDialog为false时， */
                                            tableTitle={getLabel('266072', '回复输入框操作按钮')}
                                            onCustomChange={onCustomChange}
                                            customHandleAssociativeChange={customHandleAssociativeChange}
                                            inDialog={false}
                                            onTableWidthChange={onTableWidthChange}
                                            value={value?.replyActionConfig?.configData}
                showSplitLine
            />}
        </>
    )
}