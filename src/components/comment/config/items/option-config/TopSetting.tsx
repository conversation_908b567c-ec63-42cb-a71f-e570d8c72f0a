import { AnyObj, Button, Form, FormGroupProps, FormItemProps, FormLayoutType, FormStore, FormSwitchProps, ShareListStore } from "@weapp/ui";
import { FC, ReactNode, useCallback, useEffect, useMemo, useState } from "react"
import DialogPlus from "../../../../../ui-plus/dialog-plus";
import { getLabel, request } from "@weapp/utils";
import { commentClsPrefix } from "../../../../../constants";
import { ShareSetting } from "./ShareSetting";
import { CommentConfigProps, CommentConfigType } from "../../../types";
import { listConfig } from "./contants";
import { getCommentTopShareList } from "../../../api/data";

const prefixCls = commentClsPrefix;

export interface TopSettingProps extends CommentConfigProps {
    weId: string;
    /** 自定义渲染设置 */
    customRender?: (props: AnyObj) => ReactNode;
    onSettingDataChange?: any;
    value?: any;
    config: CommentConfigType;
}

export const TopSetting: FC<TopSettingProps> = (props) => {

    const { customRender, weId, onSettingDataChange, value, config, ...resProps } = props

    const [visible, setVisible] = useState(false);

    const [total, setTotal] = useState(0);

    useEffect(() => {
        if (visible) {
            getCommentTopShareList({
                current: 1,
                pageSize: 10,
                sourceId: config.targetId,
                id: config.targetId,
                permissionId: 'comment_top',
                permissionType: 1,
                sourceType: 1
            }).then(res => {
                if (res.code === 200) {
                    const { total } = res.data || {}
                    if (total > -1) setTotal(total)
                }
            })
        }
    }, [visible])

    const formStore = useMemo(() => {
        const store = new FormStore()
        const items: FormItemProps = {
            'visibleRange': {
                itemType: 'CUSTOM'
            }
        }
        const layout: FormLayoutType[] = [
            [{
                id: 'visibleRange',
                label: getLabel('267979', '可见范围'),
                items: ['visibleRange'],
                groupId: '',
                hide: false,
            }],
        ]
        const groups: FormGroupProps[] = [];
        store.initForm({
            layout,
            items,
            groups,
            data: {},
        })
        return store
    }, []);

    const handleClose = useCallback(() => {
        setVisible(false);
    }, [])

    const handleOpen = useCallback(() => {
        setVisible(true);
    }, [])

    const onTotalChange = useCallback((total) => {
        setTotal(total)
    }, [setTotal])

    const customRenderFormSwitch = useCallback((key: string, props: FormSwitchProps) => {
        const comProps = (props as any).props;
        const { onChange, value } = comProps;

        let resultDom = <></>
        switch (key) {
            case 'visibleRange':
                resultDom = <ShareSetting
                    weId={`${props.weId || ''}_5hhe9z`}
                    onChange={onChange}
                    value={value}
                    config={config}
                    total={total}
                    onTotalChange={onTotalChange}
                />
                break
        }
        return resultDom
    }, [total, onTotalChange])

    return (
        <>
            {customRender?.({
                onClick: handleOpen
            })}
            <DialogPlus
                weId={`${props.weId || ''}_0tqxcv`}
                icon="Icon-e-builder"
                onClose={handleClose}
                visible={visible}
                title={getLabel("267978", `"置顶"按钮设置`)}
                destroyOnClose
                footer={[]}
                closable
                scale
                width={500}
                className={`${prefixCls}-optionconfig-topsetting`}
            >
                <Form
                    weId={`${props.weId || ''}_83rcal`}
                    store={formStore}
                    customRenderFormSwitch={customRenderFormSwitch}
                />
            </DialogPlus>
        </>
    )
}