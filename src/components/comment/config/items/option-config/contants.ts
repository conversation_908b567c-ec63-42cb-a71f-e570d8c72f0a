import { RequestOptionConfig } from "@weapp/utils"

export enum ReplyActionConfigOption {
    SAME = 'same',
    DIFF = 'different'
}

interface OptionConfig {
    module: string;
}

/**  */
export const listConfig = (config?: OptionConfig) => {
    const { module = 'bcw' } = config || {}
    return { 
        url: `/api/${module}/common/comment/table/list/new`,
        method: 'POST',
    } as RequestOptionConfig
}

/** 删除接口 */
export const deleteConfig = (config?: OptionConfig) => {
    const { module = 'bcw' } = config || {}
    return { 
        url: `/api/${module}/common/comment/table/delete`,
        method: 'POST',
    } as RequestOptionConfig
}

/** 添加接口 */
export const addConfig = (config?: OptionConfig) => {
    const { module = 'bcw' } = config || {}
    return { 
        url: `/api/${module}/common/comment/table/add`,
        method: 'POST',
    } as RequestOptionConfig
}

/** 编辑接口 */
export const editConfig = (config?: OptionConfig) => {
    const { module = 'bcw' } = config || {}
    return { 
        url: `/api/${module}/common/comment/table/update`,
        method: 'POST',
    } as RequestOptionConfig
}

/** 详情接口 */
export const detailConfig = (config?: OptionConfig) => {
    const { module = 'bcw' } = config || {}
    return { 
        url: `/api/${module}/common/comment/table/info`,
        method: 'POST',
    } as RequestOptionConfig
}