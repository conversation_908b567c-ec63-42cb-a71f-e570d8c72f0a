import { FC, memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button, Dialog, utils } from "@weapp/ui";
import { classnames, getLabel } from "@weapp/utils";
import { commentClsPrefix, uiPropsDesignClsPrefix } from "../../../../../constants";
import CommonConfigContent from "./Content";
import { CommonConfigContentRef, CommonConfigProps } from "../../types";
import { columnsWidths, defColumnConfig } from "./constant";

const prefixCls = commentClsPrefix;
const { needSync } = utils;

const CommonConfig: FC<CommonConfigProps> = memo((props) => {

  const {
    className,
    title,
    weId,
    columnConfig,
    description,
    inDialog,
    onVisibleChange,
    customButtonRender,
    onTableWidthChange,
    ...resProps
  } = props;

  const contentRef = useRef<CommonConfigContentRef>(null);

  const [visible, setVisible] = useState(false);

  if (needSync("visible", props) && visible !== props.visible) {
    setVisible(visible);
  }

  const handleOpenCommonConfig = useCallback(() => {
    setVisible(true);
    onVisibleChange?.(true);
  }, [onVisibleChange])

  const handleCloseCommonConfig = useCallback(() => {
    setVisible(false);
    onVisibleChange?.(false);
  }, [onVisibleChange])

  const handleSave = useCallback(() => {
    contentRef.current?.handleChange();
    handleCloseCommonConfig();
  }, [handleCloseCommonConfig])

  const cls = classnames(`${prefixCls}-config-setting`, className);

  const footer = (
    <div className={`${prefixCls}-config-setting-footer`}>
      <div className={`${prefixCls}-config-setting-description ${uiPropsDesignClsPrefix}-ellipsis`}>
        {description}
      </div>
      <div className={`${prefixCls}-config-setting-footer-btn`}>
        <Button
          weId={`${props.weId || ''}_rtenfs`}
          type="primary"
          onClick={handleSave}
        >
          {getLabel("207006", "保存")}
        </Button>
      </div>
    </div>
  )

  const dialogWidth = useMemo(() => {
    let width = 80;
    columnConfig?.forEach(key => {
      width += columnsWidths[key];
    })

    return width;
  }, [columnConfig]);

  const buttonRender = useMemo(() => {
    return customButtonRender?.(handleOpenCommonConfig)
  }, [customButtonRender])

  const displayInDialog = useMemo(() => {
    // 兼容undefined（未传表示默认dialog）
    return inDialog ?? !inDialog
  }, [inDialog])

  useEffect(() => {
    onTableWidthChange?.(dialogWidth)
  }, [dialogWidth])

  return (
    <div className={cls}>
      {displayInDialog ? <>
        {buttonRender || <Button
          weId={`${props.weId || ''}_lwwnkn`}
          className={`${prefixCls}-config-setting-btn`}
          onClick={handleOpenCommonConfig}
        >
          {getLabel("207007", "配置按钮")}
        </Button>}
        <Dialog
          weId={`${props.weId || ''}_44c10c`}
          visible={visible}
          title={title || getLabel("207008", "评论配置")}
          icon="Icon-e-builder"
          scale
          onClose={handleCloseCommonConfig}
          footer={footer}
          closable
          destroyOnClose
          width={dialogWidth}
        >
          <CommonConfigContent
            weId={`${props.weId || ''}_fk7f90`}
            {...resProps}
            columnConfig={columnConfig}
            ref={contentRef}
          />
        </Dialog>
      </> :
        <CommonConfigContent
          weId={`${props.weId || ''}_ruolzt`}
          {...resProps}
          columnConfig={columnConfig}
          ref={contentRef}
        />}
    </div>
  )
})

CommonConfig.defaultProps = {
  columnConfig: defColumnConfig,
}

export default CommonConfig;