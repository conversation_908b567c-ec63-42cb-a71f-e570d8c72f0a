import { FC, ReactNode, forwardRef, memo, useCallback, useImperativeHandle, useMemo, useState } from "react";
import { AnyObj, EditableTable, EditableTableColumn, FormItemProps, FormValue, Help, Icon, utils } from '@weapp/ui';
import { commentClsPrefix } from "../../../../../constants";
import { getLabel } from "@weapp/utils";
import { CommonConfigData } from "../../../types";
import { ComConfigColType, CommonConfigContentProps, CommonConfigContentRef } from "../../types";
import CommonConfigLocale from "./Locale";
import ButtonPlus from "../../../../../ui-plus/buttons-plus";
import { toJS } from "mobx";
import { columnsWidths } from "./constant";

const prefixCls = commentClsPrefix;
const { getRandom, deepClone } = utils;

interface OperationButtonProps {
  prefixCls: string,
  onDeleteSplitLine: (rowData: AnyObj) => void,
  weId: string,
  rowData: AnyObj
}

const OperationButton: FC<OperationButtonProps> = memo((props) => {
  const { prefixCls, onDeleteSplitLine, weId, rowData } = props

  const onClick = useCallback(() => {
    onDeleteSplitLine(rowData)
  }, [onDeleteSplitLine, rowData])

  return (
    <div
      className={`${prefixCls}-config-setting-operation`}
    >
      <ButtonPlus weId={`${weId || ''}_kkmj7o`}
                  type="link"
                  onClick={onClick}
      >
        {getLabel("207017", "删除")}
      </ButtonPlus>
    </div>
  )
})

// @ts-ignore
const CommonConfigContent: FC<CommonConfigContentProps> = memo(forwardRef<CommonConfigContentRef, CommonConfigContentProps>((props, ref) => {

  const {
    value,
    page,
    columnConfig,
    onChange,
    customRenderOperation,
    client,
    multipleOptions,
    customBodyExtraRender,
    onCustomChange,
    tableTitle,
    showSplitLine
  } = props;
  const { id: pageId } = page!;

  const [ data, setData ] = useState(toJS(value) || []);

  const customSetData = useCallback((value) => {
    typeof onCustomChange === 'function' && onCustomChange(value)
    setData(value)
  }, [setData, onCustomChange])

  const handleDataChange = useCallback((newData: CommonConfigData[]) => {
    const realData = newData.filter(i => !(i.id.indexOf('split-line__') > -1))
    const realDataNum = realData.length
    const enableNum = realData.filter(i => i.enable).length
    const showNameNum = realData.filter(i => i.showName).length

    const _newData = newData.map(item => {
      if (item.id.indexOf('split-line__') > -1) {
        const enable = enableNum === realDataNum ? true
          : enableNum === 0 ? false : item.enable
        const showName = showNameNum === realDataNum ? true
          : showNameNum === 0 ? false : item.showName

        return { ...item, enable, showName }
      } else {
        return item
      }
    })
    customSetData?.(_newData);
  }, [])

  const handleItemEditBefore = useCallback((newData: CommonConfigData[], editKey: string, editValue: FormValue,rowData:any) => {
    const _newData: any[] = deepClone(newData)
    // 处理需要同步数据的配置项
    const syncObjs = [{
      syncIds: ['pinToTop','unpin'], // 需要同步的数据id
      syncAttrs: ['enable'] // 需要同步的key
    }]
    syncObjs.forEach(syncObj => {
      const { syncIds, syncAttrs } = syncObj
      if(syncIds.includes(rowData.id) && syncAttrs.includes(editKey)) {
        syncIds.filter(id => id !== rowData.id)?.map(id => {
          return _newData.findIndex?.(item => item.id === id)
        })?.forEach(index => {
          if(index > -1 && _newData[index]) _newData[index][editKey] = editValue
        })
      }
    })
    
    return _newData
  }, [])

  const handleChange = useCallback(() => {
    onChange?.(data);
  }, [data, onChange])

  const getCurrentData = useCallback(() => {
    return data
  }, [data])

  const onAddSplitLine = useCallback(() => {
    customSetData([...data, {
      id: `split-line__${getRandom()}`,
      name: '',
      iconName: "",
      enable: true,
      showName: true,
    }])
  }, [data])

  const customRowClassName = useCallback((data: any) => {
    if (data?.id?.indexOf?.('split-line__') > -1) {
      return `${prefixCls}-config-setting-content-table-split-row`
    }
    return ''
  }, [prefixCls])

  const onDeleteSplitLine = useCallback((rowData: AnyObj) => {
    customSetData(data.filter(i => i.id !== rowData.id))
  }, [data])

  useImperativeHandle(ref, () => ({
    handleChange,
    getCurrentData
  }), [handleChange, getCurrentData])

  const renderName = useCallback((data: AnyObj, pos: number, name: string) => {
    const { name: _name, id } = data;
    if(multipleOptions?.includes(id)) {
      return (
        _name.map?.((name: string, index: number)=>{
          return <div className={`${prefixCls}-config-setting-names`} key={index}>
            <div className={`${prefixCls}-config-setting-names-text`}>{name}</div>
            <div className={`${prefixCls}-config-setting-names-split`}></div>
          </div>
        })
      )
    }
    return (
      <div>{_name}</div>
    )
  }, [props.weId])

  const renderIcon = useCallback((data: AnyObj, pos: number, name: string) => {
    const { iconName, id } = data;
    if(multipleOptions?.includes(id)) {
      return (
        iconName.map?.((iconName: string, index: number)=>{
          return <div className={`${prefixCls}-config-setting-icons`} key={index}>
            <Icon weId={`${props.weId || ''}_8wcwuj@${index}`}
              name={iconName}
              className={`${prefixCls}-config-setting-icons-icon`}
              size="md"
            />
            <div className={`${prefixCls}-config-setting-icons-split`}></div>
          </div>
        })
      )
    }
    return (
      <Icon
        weId={`${props.weId || ''}_dj7if4`}
        name={iconName}
        className={`${prefixCls}-config-setting-icon`}
        size="md"
      />
    )
  }, [props.weId])

  const renderOperation = useCallback((rowData: AnyObj, pos: number, name: string) => {

    // 分割线操作项单独渲染
    if (rowData?.id?.indexOf?.('split-line__') > -1) {
      return <OperationButton weId={`${props.weId || ''}_ansncw`}
        prefixCls={prefixCls}
        onDeleteSplitLine={onDeleteSplitLine}
        rowData={rowData}
      />
    }

    const content = (
      <ButtonPlus
        weId={`${props.weId || ''}_pc54n5`}
        type="link"
      >
        {getLabel("223930", "设置")}
      </ButtonPlus>
    )

    const button = typeof customRenderOperation === "function" ? customRenderOperation(rowData, pos, name, data, handleDataChange) : content;

    return (
      <div
        className={`${prefixCls}-config-setting-operation`}
      >
        {button}
      </div>
    )
  }, [customRenderOperation, props.weId, data])

  const columns: EditableTableColumn[] = useMemo(() => {
    const colObj: Record<ComConfigColType, EditableTableColumn> = {
      name: {
        title: getLabel("207001", "按钮名称"),
        dataIndex: "name",
        comKey: "",
        bodyRender: renderName
      },
      customName: {
        title: getLabel("207002", "自定义名称"),
        dataIndex: "customName",
        comKey: "customName",
      },
      customMessage: {
        title: (
          <span>
            {getLabel("223935", "按钮提示文字")}
            <Help
              weId={`${props.weId || ''}_beil03`}
              title={getLabel("224106", "不设置时，取按钮名称")}
              className={`${prefixCls}-config-setting-help`}
            />
          </span>
        ),
        dataIndex: "customMessage",
        comKey: "customMessage",
      },
      iconName: {
        title: getLabel("207003", "图标"),
        dataIndex: "iconName",
        comKey: "",
        bodyRender: renderIcon
      },
      enable: {
        title: getLabel("207004", "启用"),
        dataIndex: "enable",
        comKey: "enable",
        showCheckAll: true,
      },
      showName: {
        title: getLabel("207005", "显示名称"),
        dataIndex: "showName",
        comKey: "showName",
        showCheckAll: true,
      },
      operation: {
        title: getLabel("223930", "设置"),
        dataIndex: "operation",
        comKey: "",
        bodyRender: renderOperation
      }
    }
    return columnConfig?.map(key => ({
      ...colObj[key],
      width: columnsWidths[key],
    })) || []
  }, [columnConfig, props.weId, renderIcon, renderOperation])

  const onMulLocaleChange = useCallback((value: any, index: number, onChange: any) => (signleValue: any) => {
    const _value = deepClone(value)
    _value[index] = signleValue
    onChange(_value)
  }, [props.weId])

  const renderLocale = useCallback((content: any, formItemInstance: ReactNode, rowData: any, datas: any, comProps: any, pos: any)=>{
    const { id } = rowData
    const { props: localeProps } = content;
    const { value, onChange: onLocaleValueChange } = localeProps;
    if(multipleOptions?.includes(id)){
      return (
        <div className={`${prefixCls}-config-setting-locale`}>
          {
            value?.map?.((v: any, index: number) => {
              return <CommonConfigLocale
                weId={`${props.weId || ''}_7dwhx1@${index}`}
                key={index}
                targetId={pageId}
                value={v}
                onChange={onMulLocaleChange(value, index, onLocaleValueChange)}
              />
            })
          }
        </div>
      )
    }
    return (
      <CommonConfigLocale
        weId={`${props.weId || ''}_5xqaur`}
        targetId={pageId}
        value={value}
        onChange={onLocaleValueChange}
      />
    )
  }, [pageId, props.weId])

  const comProps: FormItemProps = useMemo(() => ({
    "name": {
      itemType: "INPUT",
      readOnly: true,
    },
    "customName": {
      itemType: "LOCALE",
      customRender: renderLocale
    },
    "customMessage": {
      itemType: "LOCALE",
      customRender: renderLocale
    },
    "enable": {
      itemType: "CHECKBOX",
    },
    "showName": {
      itemType: "CHECKBOX",
    },
  }), [renderLocale])

  const bodyExtraRender = useMemo(() => {
    return customBodyExtraRender?.() || null
  }, [customBodyExtraRender])

  return (
    <div className={`${prefixCls}-config-setting-content`}>
      {
        bodyExtraRender
      }
      {
        <div className={`${prefixCls}-config-setting-content-top-area`}>
          <div className={`${prefixCls}-config-setting-content-top-area-title`}>{tableTitle}</div>
          {
            showSplitLine && client === 'PC' ? (
              <div className={`${prefixCls}-config-setting-content-split`}>
                <Icon
                  weId={`${props.weId || ''}_9r1u26`}
                  name={'Icon-Split-line'}
                  size={'md'}
                  title={getLabel("244065", "添加分割线")}
                  onClick={onAddSplitLine}
                />
              </div>
            ) : null
          }
        </div>
      }
      <EditableTable
        weId={`${props.weId || ""}_1lc853`}
        columns={columns}
        data={data}
        comProps={comProps}
        deleteConfirm={true}
        showSelection={false}
        sortable
        sortableType="icon"
        onChange={handleDataChange}
        onItemEditBefore={handleItemEditBefore}
        rowClassName={customRowClassName}
        // onDrag={handleDrag}
     />
    </div>
  )
}))

export default CommonConfigContent;