import { CorsComponent } from "@weapp/ui";
import { LocaleProps } from "@weapp/ui/lib/components/locale";
import { ReactText, FC, useCallback } from "react";
import LocaleEx from "../../../../../ebdcoms/locale-ex";

interface CommonConfigLocaleProps extends LocaleProps {
  weId: string;
  /** 多语言唯一id */
  targetId?: string;
  /** 修改完整数据方法 */
  onChange?: (value: ReactText) => void;
}

const CommonConfigLocale: FC<CommonConfigLocaleProps> = (props) => {

  const { targetId, value, onChange } = props;

  // const getUrlData = useMemo(() => {
  //   const params: AnyObj = {
  //     targetId,
  //   }
  //   if (targetId) {
  //     params.tablefield = tablefield;
  //   }
  //   return params;
  // }, [tablefield, targetId]);

  // const saveUrlData = useMemo(() => {
  //   const params: AnyObj = {
  //     targetId,
  //     tablefield
  //   }
  //   return params;
  // }, [tablefield, targetId]);

  const handleChange = useCallback((value: any) => {
    onChange?.(value);
  }, [onChange]);

  return (
    // <Locale
    //   weId={`${props.weId || ''}_d3kdjr`}
    //   useDefaultLangConfig={true}
    //   getUrl="/api/bs/bcw/area/multilangsetMultiline/getdata"
    //   saveUrl="/api/bs/bcw/area/multilangsetMultiline/savedata"
    //   saveUrlData={saveUrlData}
    //   getUrlData={getUrlData}
    //   onChange={onChange}
    //   matchWindowIsEnableMultiLang={false}
    //   onSuccessSave={handleSuccessSave}
    // />
    <LocaleEx
      weId={`${props.weId || ''}_mim4d0`}
      value={value}
      // Locale组件请求需要的targetId参数，可取页面id作为targetId
      targetId={targetId}
      // 如果是二级模块，则用"/"隔开，如"xxx/xxx"
      module="ebuilder"
      // 兼容老数据，当值为对象时，需要传入改属性，值为改造多语言前文本内容存储对应的key
      nameAliasKey="name"
      onChange={handleChange}
    />
  )
}

export default CommonConfigLocale;