import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { getLabel } from '@weapp/utils';
import DialogPlus from '../../../../../ui-plus/dialog-plus';
import { CommentConfigProps } from '../../../types';
import { Button, Form, FormGroupProps, FormItemProps, FormLayoutType, FormStore, FormDatas } from '@weapp/ui';
import { LABEL_SPAN_LG } from '../../../../../constants';

interface AnonymousSettingDialogProps extends CommentConfigProps<any> {
  form?: any
  visible?: boolean;
  onVisibleChange?: (value: boolean) => void;
  onChange: (value: any) => void;
}

export const AnonymousSettingDialog: FC<AnonymousSettingDialogProps> = (props) => {
  const { weId, visible, onVisibleChange, onChange, form } = props;
  const { allowAnonymousSetting, defaultAnonymousByCheck } = form.datas
  const [data, setData] = useState({ allowAnonymousSetting, defaultAnonymousByCheck })
  const formStore = useMemo(() => new FormStore(), [visible]);

  useEffect(() => {
    const { initForm, isFormInit } = formStore;
    const datas = { allowAnonymousSetting, defaultAnonymousByCheck }
    if (!isFormInit) {
      const items: FormItemProps = {
        'defaultAnonymousByCheck': {
          itemType: 'SWITCH',
          value: false
        },
        'allowAnonymousSetting': {
          itemType: 'SWITCH',
          value: false
        },
      }
      const layout: FormLayoutType[] = [
        [{
          id: 'defaultAnonymousByCheck',
          label: getLabel('261575','默认启用'),
          labelSpan: LABEL_SPAN_LG,
          items: ['defaultAnonymousByCheck'],
          groupId: '',
          hide: false,
        }],
        [{
          id: 'allowAnonymousSetting',
          label: getLabel('288128','允许用户设置'),
          labelSpan: LABEL_SPAN_LG,
          items: ['allowAnonymousSetting'],
          groupId: '',
          hide: false,
        }],
      ]

      const groups: FormGroupProps[] = [];


      initForm({
        layout,
        items,
        groups,
        data: datas,
      })
    }
    setData({ allowAnonymousSetting, defaultAnonymousByCheck })
  }, [formStore, allowAnonymousSetting, defaultAnonymousByCheck])

  const handleClose = useCallback(() => {
    onVisibleChange?.(false)
  }, [onVisibleChange])

  const handleSave = useCallback(() => {
    onChange?.(data)
  }, [onChange, data])

  const handleChange = useCallback((value?: FormDatas) => {
    formStore.updateDatas({ ...formStore.getFormDatas(), ...value as FormDatas })
    setData({ ...formStore.getFormDatas(), ...value as FormDatas })
  }, [formStore])

  const footer = useMemo(() => ([
    <Button weId={`${weId || ''}_czkjxu@save}`} key="sure" type="primary" onClick={handleSave}>{getLabel("207006", "保存")}</Button>,
    <Button weId={`${weId || ''}_5dg3ht@close}`} key="cancel" onClick={handleClose}>{getLabel('223917','取消')}</Button>,
  ]), [weId, handleSave, handleClose]);

  return (
    <DialogPlus weId={`${weId || ''}_uiskhr`}
                icon="Icon-e-builder"
                onClose={handleClose}
                visible={visible}
                title={getLabel('288129','匿名设置')}
                destroyOnClose
                footer={footer}
                closable
                width={480}
    >
      <Form weId={`${props.weId || ''}_0e6g7x`}
            store={formStore}
            onChange={handleChange}
      />
    </DialogPlus>
  )
}