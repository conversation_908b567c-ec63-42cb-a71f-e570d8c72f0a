import { FC, useCallback } from "react";
import { CommentConfigProps } from "../../types";
import { Radio } from "@weapp/ui";
import { getLabel } from '@weapp/utils';
import { getGroupsByComponentType } from '../../utils';

interface ComponentTypeProps extends CommentConfigProps {
  form?: any
}

const data = [{
  id: "input",
  content: getLabel("244828", "仅评论输入"),
}, {
  id: "list",
  content: getLabel("244827", "仅评论列表"),
}, {
  id: "all",
  content: getLabel("244826", "评论输入+评论列表"),
}]

const ComponentType: FC<ComponentTypeProps> = (props) => {

  const { value, className, form, onConfigChange } = props;

  const handleChange = useCallback((value: any) => {
    const { datas } = form

    form.setState({ groups: getGroupsByComponentType(value) })

    onConfigChange?.({
      hasEdit:  value === 'input' ? 'show' : datas.hasEdit,
      componentShowType: value
    });
  }, [onConfigChange])

  return (
    <Radio weId={`${props.weId || ''}_17qble`}
      value={value || 'all'}
      className={className}
      data={data}
      onChange={handleChange}
    />
  )
}

export default ComponentType;