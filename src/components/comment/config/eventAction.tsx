import { Cors<PERSON>omponent, FormItem, Help, Switch, AnyObj } from "@weapp/ui";
import { cloneDeep, getLabel, isArray } from "@weapp/utils";
import { toJS } from "mobx";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { commentClsPrefix } from "../../../constants";
import { handleFormatSourceFields } from "../utils";
import { getCommentFormFieldsMemo, getFields } from "../utils/getApiDatas";
import { fieldSelectOption } from "../api/data";
import { ebuilderformGroupId } from "../constant";

const clsPrefix = `${commentClsPrefix}-event`
const labelSpan = 6

const CustomRenderConfig: React.FC<any> = (props) => {
    //console.log('CustomRenderConfig', props)
    const {
        refreshInfo = {}, onChange, store
    } = props;

    const comId = refreshInfo.selectedCom?.[0]
    const com = toJS(store.coms).find((com: any) => com.id === comId)
    const { useOriginalTableFields, relatedDataSource, itemFormId } = com.config || {}

    const [sourceFields, setSourceFields] = useState<Array<AnyObj>>([])

    const [commentSourceFields, setCommentSourceFields] = useState<Array<AnyObj>>([])

    const [commentFormData, setCommentFormData] = useState<any>(null);

    const handleCommentForm = useCallback(async () => {
        // 如果已经缓存，直接返回格式化后的值
        if (commentFormData) {
            const { commentFormObjId } = commentFormData || {}
            const dataset = {
                ...relatedDataSource,
                id: commentFormObjId
            }
            const sourceFields = await handleCommentFields(dataset)
            const formFieldsObj = handleFormatSourceFields(sourceFields);
            return formFieldsObj;
        }

        // 否则发请求
        if (!itemFormId) return {};
        const res = await getCommentFormFieldsMemo(itemFormId, relatedDataSource?.dataExtMemoTime)
        const { commentFormObjId } = res.data || {};
        const dataset = {
            ...relatedDataSource,
            id: commentFormObjId
        }
        const sourceFields = await handleCommentFields(dataset)
        const formFieldsObj = handleFormatSourceFields(sourceFields);

        // 缓存结果
        setCommentFormData(res.data);

        return formFieldsObj;
    }, [itemFormId, relatedDataSource, commentFormData]);

    const handleCommentFields = useCallback(async (dataset) => {
        const sourceFields = await getFields(dataset, 'field')
        setCommentSourceFields(sourceFields)
        return sourceFields
    }, [])

    const getFieldSelectOptionUrl = useCallback(async (fieldComVal: any) => {
        const { fieldName, objId } = fieldComVal || {}
        const params = {
            objId,
            fieldName,
            sourceType: 'LOGIC',
            groupId: ebuilderformGroupId
        }
        const res = await fieldSelectOption(params);
        return res.data;
    }, [])

    const handleFields = useCallback(async () => {
        const fields = await getFields(relatedDataSource, 'field');
        setSourceFields(fields)
        return fields;
    }, [relatedDataSource]);

    const getSourceField = useCallback(async () => {
        if (useOriginalTableFields) {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm(), handleFields()]).then(([formFieldsObj, fields]: [any, any]) => {
                    reslove([...fields, formFieldsObj])
                })
            })
        } else {
            return new Promise((reslove) => {
                Promise.all([handleCommentForm()]).then(([formFieldsObj]: [any]) => {
                    reslove([formFieldsObj])
                })
            })
        }
    }, [useOriginalTableFields, handleCommentForm, handleFields])

    const onFilterChange = useCallback(
        (value: any) => {
            const result = toJS(value)
            const _filter = result.filter
            const { commentFormObjId } = commentFormData || {}
            if (commentFormObjId) {
                const filterDatas = _filter?.datas?.map((item: any) => {
                    const { objId, fieldName, config } = item
                    if (objId === commentFormObjId) { // 评论表
                        const dataKey = commentSourceFields?.[0]?.fields?.find((field: AnyObj) => field.id === config?.fieldId)?.config?.dataKey || fieldName
                        return {
                            dataKey,
                            ...item,
                            customFieldName: fieldName,
                            fieldName: `c.${dataKey}`,
                            id: undefined, // 如果传id，条件会显示“已删除”（eb逻辑）；后端根据dataKey过滤，这里不传id无影响
                        }
                    } else { // 主表
                        const dataKey = sourceFields?.[0]?.fields?.find((field: AnyObj) => field.id === config?.fieldId)?.config?.dataKey || fieldName
                        return {
                            dataKey,
                            ...item,
                            customFieldName: fieldName,
                            fieldName: `i.${dataKey}`,
                            id: undefined, // 如果传id，条件会显示“已删除”（eb逻辑）；后端根据dataKey过滤，这里不传id无影响
                        }
                    }
                })

                onChange({
                    ...result,
                    filter: {
                        ..._filter,
                        datas: filterDatas
                    }
                })
            }
        },
        [refreshInfo, sourceFields, commentSourceFields, commentFormData, onChange],
    );

    const onSwitchChange = useCallback(
        (checked: boolean) => {
            onChange({ isOverrideFilter: checked });
        },
        [refreshInfo],
    );

    const valueFilter = useMemo(() => {
        if (!relatedDataSource) return {}
        let value = toJS(refreshInfo);
        const { datas } = value?.filter || {}
        return {
            ...value?.filter,
            datas: toJS(datas)?.map((data: any) => {
                const { customFieldName, fieldName } = data
                return {
                    ...data,
                    fieldName: customFieldName || fieldName
                }
            })
        }
    }, [relatedDataSource, refreshInfo])

    const customRenderFormSwitch = useCallback(() => {
        let value = toJS(refreshInfo);
        if (isArray(value)) {
            value = null;
        }

        return (
            <CorsComponent weId={`${props.weId || ''}_leiv1f`}
                app="@weapp/ebdcontainercoms"
                compName="FilterCom"
                {...props}
                value={valueFilter} // 显示字段回显filter对象
                onConfigChange={onFilterChange} // 回调
                config={{
                    ...value, // 显示字段回显filter对象
                    filter: valueFilter || {},
                    dataset: relatedDataSource, // 数据源
                }}
                comProps={{
                    fieldSelectOptionUrl: getFieldSelectOptionUrl
                }}
                // sourceField={sourceField}
                // placeholder={xxx} // 输入框placeholder
                // comId={xxx} // 组件id
                getSourceField={relatedDataSource ? getSourceField : undefined}
            />
        )
    }, [refreshInfo, valueFilter, relatedDataSource, sourceFields, commentSourceFields, commentFormData, props]);

    const customRenderSwitch = useCallback(
        () => (
            <div className={`${clsPrefix}-refresh-over`}>
                <Switch weId={`${props.weId || ''}_brmvsv`}
                    value={refreshInfo.isOverrideFilter}
                    size="sm"
                    onChange={onSwitchChange}
                />
                <Help weId={`${props.weId || ''}_2iyhos`}
                    title={
                        <>
                            <div>{getLabel('312135', '1.按钮开启，所选组件设置的筛选条件配置仅在打开页面时生效，执行按钮动作时仅根据动作中的条件设置来过滤数据；')}</div>
                            <div>{getLabel('312136', '2.按钮关闭，所选组件设置的筛选条件与动作中的条件设置关系为and关系，执行按钮动作时两者共同生效。')}</div>
                        </>
                    }
                    placement="bottom"
                />
            </div>
        ),
        [refreshInfo],
    );

    return (
        <>
            <FormItem weId={`${props.weId || ''}_89jwmw`}
                className={`${clsPrefix}-action`}
                label={getLabel('312137', '条件设置')}
                item={{
                    conditionSet: {
                        itemType: 'CUSTOM',
                        value: '',
                    },
                }}
                com={['conditionSet']}
                labelSpan={labelSpan}
                customRenderFormSwitch={customRenderFormSwitch}
            />
            <FormItem weId={`${props.weId || ''}_r4hf4z`}
                className={`${clsPrefix}-action`}
                label={getLabel('312138', '覆盖组件筛选条件')}
                item={{
                    isOverrideFilter: {
                        itemType: 'CUSTOM',
                        value: refreshInfo.isOverrideFilter,
                    },
                }}
                com={['isOverrideFilter']}
                labelSpan={labelSpan}
                customRenderFormSwitch={customRenderSwitch}
            />
        </>
    );
};

const eventAction = {
    events: ['CLICKDATA'],
    actions: [
        /** 组件支持刷新组件事件动作 */
        {
            id: 'RefreshComp',
            name: getLabel('312139', '刷新组件'),
            config: {
                /** 自定义渲染配置项 */
                customRenderConfig: (props: any) => (
                    <CustomRenderConfig weId={`${props.weId || ''}_spl69m`} {...props} />
                ),
            },
        },
    ]
}

export default eventAction