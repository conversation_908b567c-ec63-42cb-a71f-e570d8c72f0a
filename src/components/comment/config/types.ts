import { Transform, ComConfigData, IActionData, TransformContext } from "@weapp/ebdcoms";
import { Client } from "../../../types/common";
import { CommentConfigProps, CommentConfigType, CommonConfigData } from "../types";
import { DialogProps, AnyObj } from "@weapp/ui";
import { ReactNode, RefObject } from "react";

export type ConfigType = ComConfigData & {
  transform: TransformType;
};

export type TransformHandler<T, A = any> = (
  this: TransformContext,
  changes: Partial<T>,
  actData: IActionData<A>,
  client: Client,
) => T;

export type TransformType = Transform<Partial<CommentConfigType>>;

export interface CommonConfigProps extends CommentConfigProps<CommonConfigData[]> {
  title?: string;
  /** 弹出框是否展开 */
  visible?: boolean;
  /** 弹出框关闭事件 */
  onVisibleChange?: (visible: boolean) => void;
  dialogProps?: DialogProps;
  /** 说明 */
  description?: ReactNode;
  /** 列配置 */
  columnConfig?: ComConfigColType[];
  /* 自定义渲染配置按钮 */
  customRenderOperation?: (data: AnyObj, pos: number, name: string, totalData: AnyObj, handleDataChange: any) => void;
  /** 多个配置按钮 */
  multipleOptions?: Array<any>;
  /** 额外渲染 */
  customButtonRender?: (onClick: any) => void;
  showSplitLine?: boolean;
}

export interface CommonConfigContentRef {
  handleChange: () => void;
  getCurrentData?: () => void;
}

export type ComConfigColType = "name" | "customName" | "customMessage" | "iconName" | "enable" | "showName" | "operation";

export interface CommonConfigContentProps extends CommentConfigProps<CommonConfigData[]> {
  id?: string;
  ref?: RefObject<CommonConfigContentRef>;
  /** 列配置 */
  columnConfig?: ComConfigColType[];
  /* 自定义渲染配置按钮 */
  customRenderOperation?: (data: AnyObj, pos: number, name: string, totalData: AnyObj, handleDataChange: any) => void;
  /** 多个配置按钮 */
  multipleOptions?: Array<any>;
  /** 额外渲染 */
  customBodyExtraRender?: () => void;
  showSplitLine?: boolean;
}
