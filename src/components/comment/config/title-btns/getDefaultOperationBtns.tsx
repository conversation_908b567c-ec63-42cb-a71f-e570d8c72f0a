import { ClientType } from "@weapp/ebdcoms";
import { AlignType, DiyBtn, OperationBtns } from "../../../../ebConfig/operation-btns/types";
import topOperatBtns, { setTopBtnOpen } from "./topBtns";
import { CommentDesignProps } from "../../types";

const getDefaultOperationBtns = (client: ClientType, props: Partial<CommentDesignProps> = {}): OperationBtns => {

  // 兼容历史数据，根据config中的topOperatBtns属性修改搜索、
  const { config = {} } = props;
  const { topOperatBtns: configTopOperatBtns = {} } = config;
  const { btns = [] } = configTopOperatBtns;

  let defaultTopOperatBtns = { ...topOperatBtns };
  (btns as DiyBtn[]).forEach((item) => {
    const { event, isOpen } = item || {};
    defaultTopOperatBtns.btns = setTopBtnOpen(event?.to, isOpen);
  })

  return {
    topOperatBtns,
    bottomOperatBtns: {
      enabled: false,
      alignType: AlignType.CENTER,
      btns: [],
    },
  };
}

export default getDefaultOperationBtns;