import { getLabel } from "@weapp/utils";
import { DiyBtn, ShowType, BtnFrom, EventTo, AlignType } from "../../../../ebConfig/operation-btns/types";
import SortConfig from "./sort/config";
import { utils } from "@weapp/ui";
import SearchConfig from "./search/config";

export enum CustomBtnType {
  SORT = 'Comment.Sort'
}

export const defaultIconStyle = {
  fontSize: '36',
  bgColor: 'transparent',
  borderColor: 'transparent',
  color: '#999',
  radius: '3px',
};

const { getHash } = utils;

export const topBtns: DiyBtn<any>[] = [{
  id: getHash(6),
  showType: ShowType.ICON, // 'ICON'(图标) | 'FONTBTN'(文字) | 'FONTWITHICON'(图标与文字按钮)
  btnFrom: BtnFrom.DEFAULT, // 'DEFAULT'(默认)
  showContent: {
    emptyContent: {
      path: '#Icon-Advanced-search',
      title: getLabel('223918', '搜索'),
      style: defaultIconStyle,
    },
    selectedContent: {
      path: '#Icon-Advanced-search-selected',
      title: getLabel('223918', '搜索'),
      style: {
        ...defaultIconStyle,
        color: 'var(--primary)',
      },
    },
    customRenderConfig: SearchConfig,
  },
  event: {
    to: EventTo.SEARCH, // 事件名,根据自己按钮特性取名
    value: getLabel("241553", '搜索/已设条件'), // 值
    // customRenderConfig: DemoEventConfig
  },
  isOpen: true, // 默认开启
  showOrder: 5,
  isEdit: false,
  //@ts-ignore
  showTypeData: [ShowType.ICON, ShowType.FONTBTN],
}, {
  customType: CustomBtnType.SORT,
  id: getHash(6),
  showType: ShowType.ICON, // 'ICON'(图标) | 'FONTBTN'(文字) | 'FONTWITHICON'(图标与文字按钮)
  btnFrom: BtnFrom.DEFAULT, // 'DEFAULT'(默认)
  showContent: {
    emptyContent: {
      path: '#Icon-Reverse-order',
      title: getLabel('313309', '默认'),
      style: defaultIconStyle,
    },
    descContent: {
      path: '#Icon-Reverse-order',
      title: getLabel('223908', '按评论时间倒序'),
      style: defaultIconStyle,
    },
    ascContent: {
      path: '#Icon-positive-sequence',
      title: getLabel('223909', '按评论时间正序'),
      style: defaultIconStyle,
    },
    customRenderConfig: SortConfig,
  },
  event: {
    to: EventTo.SORT, // 事件名,根据自己按钮特性取名
    value: getLabel("223976", '排序'), // 值
  },
  isOpen: true, // 默认不开启
  showOrder: 5,
  isEdit: false,
  //@ts-ignore
  showTypeData: [ShowType.ICON, ShowType.FONTBTN],
}, {
  id: getHash(6),
  showType: ShowType.ICON, // 'ICON'(图标) | 'FONTBTN'(文字) | 'FONTWITHICON'(图标与文字按钮)
  btnFrom: BtnFrom.DEFAULT, // 'DEFAULT'(默认)
  showContent: {
    path: '#Icon-performance-appraisal-o',
    title: getLabel('223936', '统计'),
    style: defaultIconStyle,
  },
  event: {
    to: EventTo.STATISTICS, // 事件名,根据自己按钮特性取名
    value: getLabel("223936", '统计'), // 值
  },
  isOpen: true, // 默认不开启
  showOrder: 5,
  isEdit: false,
  //@ts-ignore
  showTypeData: [ShowType.ICON, ShowType.FONTBTN],
}]

const topOperatBtns = {
  enabled: true,
  alignType: AlignType.CENTER,
  btns: topBtns,
}

export const setTopBtnOpen = (event: EventTo, isOpen: boolean) => {
  return topBtns.map(item => {
    if (item.event.to !== event) {
      return item;
    }
    return {
      ...item,
      isOpen,
    }
  })
}

export default topOperatBtns;