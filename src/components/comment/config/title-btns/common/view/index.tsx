import { FC, useCallback, SyntheticEvent, useState, useMemo, useEffect } from "react";
import ConfigurableButton from "../../../../../../ebdcoms/configurable-button";
import { CommonViewProps } from "../types";
import { AnyObj } from "@weapp/ui";
import { OPERATION_BTN, TOP_COMMENT } from "../../../../constant/eventNames";

const CommonView: FC<CommonViewProps> = (props) => {
  const {
    btn, isDesign, events, eventId, page, config
  } = props;

  const { client } = page!;

  const isMobile = client === "MOBILE";

  const [enable, setEnable] = useState(false);
  const { disabled = false, action } = btn as any;

  const handleTopBtnChange = (topAction: string, status?: boolean) => {
    setEnable(preState => {
      const newEnable = typeof status === "boolean" ? status : !preState;
      return action === topAction && newEnable;
    });
  }

  useEffect(() => {
    //@ts-ignore
    events?.on(TOP_COMMENT, eventId, handleTopBtnChange);
    return () => {
      //@ts-ignore
      events?.off(TOP_COMMENT, eventId, handleTopBtnChange);
    }
  }, [eventId])

  const onBtnClick = useCallback((e: SyntheticEvent) => {
    if (isDesign) return;
    events?.emit(OPERATION_BTN, eventId, e, btn);
    events?.emit(TOP_COMMENT, eventId, action);
  }, [isDesign, events, eventId, btn, action]);

  const btnInfo = useMemo(() => {
    const style: AnyObj = {
      icon: "var(--primary)",
    };
    const configStyle = enable && !isMobile ? style : undefined;
    return {
      ...btn,
      config: {
        ...btn?.config,
        default: configStyle,
        hover: style,
      }
    }
  }, [btn, enable, isMobile])

  if(config.componentShowType === 'input') return null
  return (
    <ConfigurableButton
      weId={`${props.weId || ''}_g9v1dt`}
      btn={btnInfo}
      onClick={onBtnClick}
      disabled={disabled}
    />
  );
}

export default CommonView;