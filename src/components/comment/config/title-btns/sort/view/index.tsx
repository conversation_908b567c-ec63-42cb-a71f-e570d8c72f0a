import { FC, useState, useMemo, useCallback, SyntheticEvent, useEffect } from "react";
import { DiyBtn, SortVal } from "../../../../../../ebConfig/operation-btns/types";
import { CommentConfigProps } from "../../../../types";
import ConfigurableButton from "../../../../../../ebdcoms/configurable-button";
import { OPERATION_BTN } from "../../../../constant/eventNames";
import { Sort } from "../types";
import { AnyObj } from "@weapp/ui";
interface SortViewProps extends CommentConfigProps {
  btn: DiyBtn<SortVal<SortViewProps>>;
  isDesign: boolean;
  eventId: string;
}

interface OptProps {
  id: string;
  selected: boolean;
  contentKey: keyof SortVal<SortViewProps>;
}

const SortView: FC<SortViewProps> = (props) => {
  const {
    btn, events, eventId, isDesign, config, page
  } = props;

  const [options, setOptions] = useState<Array<OptProps>>([])

  useEffect(() => {
    const { defalutContent } = btn as any;
    const defaultOpts: Array<OptProps> = [
      { id: Sort.DESC, selected: defalutContent === Sort.DESC || !defalutContent, contentKey: 'descContent' },
      { id: Sort.ASC, selected: defalutContent === Sort.ASC, contentKey: 'ascContent' },
    ]
    if (config.relatedDataSource) {
      const emptyOpt: OptProps = { id: Sort.EMPTY, selected: defalutContent === Sort.EMPTY, contentKey: 'emptyContent' }
      defaultOpts.unshift(emptyOpt)
    }
    setOptions(defaultOpts)
  }, [config.relatedDataSource, btn])

  const { disabled = false, defalutContent } = btn as any;

  const defalutIsAsc = defalutContent === Sort.ASC;

  const [isAsc, setAsc] = useState(defalutIsAsc);

  if (isDesign && defalutIsAsc !== isAsc) {
    setAsc(defalutIsAsc);
  }

  const showContent = useMemo(() => {
    const selectedOption = options?.find(opt => opt.selected)
    if (selectedOption) {
      return btn?.showContent?.[selectedOption.contentKey]
    }
    return btn?.showContent?.descContent
  }, [options, btn?.showContent]);

  const onBtnClick = useCallback((e: SyntheticEvent) => {
    if (isDesign) return;
    //const changes = !isAsc;
    //setAsc(prevAsc => !prevAsc);
    const selectedIndex = options?.findIndex(opt => opt.selected)
    const newOptions = options?.map(opt => {
      return { ...opt, selected: false }
    }) || []
    if (selectedIndex > -1) {
      if (selectedIndex + 1 === newOptions.length) {
        newOptions[0].selected = true
      } else {
        newOptions[selectedIndex + 1].selected = true
      }
    } else {
      if (newOptions[0]) {
        newOptions[0].selected = true
      }
    }
    setOptions(newOptions)
    events?.emit(OPERATION_BTN, eventId, e, { ...btn, /*isAsc: changes,*/ options: newOptions });
  }, [isDesign, isAsc, events, eventId, btn, options]);

  const conBtn = useMemo(() => {
    const style: AnyObj = {
      icon: "var(--primary)",
    };
    let defaultStyle = {}
    let hoverStyle = { ...style }
    if (config.relatedDataSource) {
      /** 数据源模式下，按钮高亮 */
      const opt = options?.find(opt => opt.selected)
      if (opt?.id !== Sort.EMPTY) {
        defaultStyle = style
      } else {
        if (page?.client !== 'PC') {
          hoverStyle = {}
        }
      }
    }
    return {
      ...btn,
      showContent: showContent,
      config: {
        ...btn?.config,
        hover: hoverStyle,
        default: defaultStyle
      }
    }
  }, [btn, showContent, config.relatedDataSource, options, page])

  if (config.componentShowType === 'input') return null
  return (
    <ConfigurableButton
      weId={`${props.weId || ''}_g9v1dt`}
      btn={conBtn}
      onClick={onBtnClick}
      disabled={disabled}
    />
  );
}

export default SortView;