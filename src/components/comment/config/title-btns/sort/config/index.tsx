import { classnames, getLabel } from "@weapp/utils";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { DiyBtn, ShowType, SortVal } from "../../../../../../ebConfig/operation-btns/types";
import cloneDeep from "lodash-es/cloneDeep";
import { If, Then, Else } from "react-if";
import IconEidtor from "../../../../../../ebdcoms/icon-eidtor";
import { commentClsPrefix } from "../../../../../../constants";
import { Select, SelectOptionData, SelectValueType } from "@weapp/ui";
import { Sort } from "../types";
import { CustomBtnType, defaultIconStyle, topBtns } from "../../topBtns";
import LocaleEx from "../../../../../../ebdcoms/locale-ex";

const clsPrefix = commentClsPrefix;

interface SortConfigProps extends React.Attributes {
  btns: DiyBtn<SortVal<SortConfigProps>>[];
  pageId: string;
  rowIndex: number;
  config: any;
  onChange: (val: any) => void;
}

const SortConfig: FC<SortConfigProps> = (props) => {
  const {
    btns, pageId, rowIndex, onChange, config
  } = props;

  const cloneBtns = useMemo(() => cloneDeep(btns), [btns]);

  const btn: any = useMemo(() => cloneDeep(btns[rowIndex]), [btns, rowIndex]);

  const [tipValue, setTipValue] = useState(btn.defalutContent || Sort.DESC);

  const defaultSortBtn = useMemo(() => {
    return topBtns.find(btn => btn.customType === CustomBtnType.SORT)
  }, [])

  useEffect(() => {
    if (config.relatedDataSource) {
      setTipValue(btn.defalutContent || Sort.EMPTY)
    } else {
      setTipValue(btn.defalutContent || Sort.DESC)
    }
  }, [config.relatedDataSource])

  const onHandleChange = (key: string) => (val: any) => {
    const copyBtn = {
      ...btn,
      showContent: {
        ...btn.showContent,
      },
    };
    if (copyBtn.showType === ShowType.ICON) {
      (copyBtn.showContent as any)[key] = {
        ...val,
        style: defaultIconStyle,
      };
    } else {
      (copyBtn.showContent as any)[key] = val;
    }
    cloneBtns[rowIndex] = copyBtn;
    onChange({
      btns: cloneBtns,
    });
  };

  const handleSelectChange = useCallback((value: SelectValueType) => {
    setTipValue(value as Sort);
    const _btn = {
      ...btn,
      defalutContent: value,
    }
    cloneBtns[rowIndex] = _btn;
    onChange({
      btns: cloneBtns
    })
  }, [btn, cloneBtns, onChange, rowIndex])

  const descCls = classnames(`${clsPrefix}-top-button-desc`, `${clsPrefix}-top-button-icon`, {
    [`${clsPrefix}-top-button-icon-hasVal`]: btn.showContent?.descContent?.path,
  })

  const ascCls = classnames(`${clsPrefix}-top-button-asc`, `${clsPrefix}-top-button-icon`, {
    [`${clsPrefix}-top-button-icon-hasVal`]: btn.showContent?.ascContent?.path,
  })

  const emptyCls = classnames(`${clsPrefix}-top-button-natural`, `${clsPrefix}-top-button-icon`, {
    [`${clsPrefix}-top-button-icon-hasVal`]: btn.showContent?.emptyContent?.path,
  })

  const tipData: SelectOptionData[] = useMemo(() => {
    const data = [{
      id: Sort.DESC,
      content: btn?.showContent?.descContent?.title as string || defaultSortBtn?.showContent?.descContent?.title,
    }, {
      id: Sort.ASC,
      content: btn?.showContent?.ascContent?.title as string || defaultSortBtn?.showContent?.ascContent?.title,
    }]
    if (config.relatedDataSource) {
      data.unshift({
        id: Sort.EMPTY,
        content: btn?.showContent?.emptyContent?.title as string || defaultSortBtn?.showContent?.emptyContent?.title,
      })
    }
    return data
  }, [btn.showContent.ascContent, btn.showContent.descContent, btn.showContent.emptyContent, config.relatedDataSource])

  const [firstKey, secondKey, thirdKey] = useMemo(() => {
    if (config.relatedDataSource) {
      if (tipValue === Sort.DESC) {
        return ["descContent", "ascContent", "emptyContent"]
      } else if (tipValue === Sort.ASC) {
        return ["ascContent", "descContent", "emptyContent"]
      }
      return ["emptyContent", "descContent", "ascContent"]
    }
    if (tipValue === Sort.DESC) {
      return ["descContent", "ascContent", "emptyContent"]
    }
    return ["ascContent", "descContent", "emptyContent"]
  }, [tipValue, config.relatedDataSource])

  return (
    <div className={`${clsPrefix}-top-button`}>
      <If weId={`${props.weId || ''}_71sdxb`} condition={btn.showType === ShowType.ICON}>
        <Then weId={`${props.weId || ''}_8g50bc`}>
          <div className={`${clsPrefix}-top-button-container`}>
            <IconEidtor weId={`${props.weId || ''}_7ovcr5`}
              className={descCls}
              value={(btn.showContent as any)?.[firstKey]}
              onChange={onHandleChange(firstKey)}
              canEditStyle={false}
              canEditTitle
              pageId={pageId}
            />
            <div className="split-line">—</div>
            <IconEidtor weId={`${props.weId || ''}_gt8red`}
              className={ascCls}
              value={(btn.showContent as any)?.[secondKey]}
              onChange={onHandleChange(secondKey)}
              canEditStyle={false}
              canEditTitle
              pageId={pageId}
            />
            {config.relatedDataSource && <>
              <div className="split-line">—</div>
              <IconEidtor weId={`${props.weId || ''}_ck0cux`}
                className={emptyCls}
                value={(btn.showContent as any)?.[thirdKey]}
                onChange={onHandleChange(thirdKey)}
                canEditStyle={false}
                canEditTitle
                pageId={pageId}
              />
            </>}
          </div>
          <Select
            weId={`${props.weId || ''}_avdr1t`}
            data={tipData}
            value={tipValue}
            className={`${clsPrefix}-top-button-select`}
            onChange={handleSelectChange}
          />
        </Then>
        <Else weId={`${props.weId || ''}_y66abk`}>
          <div className={`${clsPrefix}-top-button-container`}>
            <LocaleEx weId={`${props.weId || ''}_hf9kow`}
              placeholder={getLabel('110707', '请输入展开文本')}
              onChange={onHandleChange('descContent')}
              disabled={false}
              className={`${clsPrefix}-top-button-locale`}
              value={btn.showContent?.descContent}
            />
            <div className="split-line">—</div>
            <LocaleEx weId={`${props.weId || ''}_2y3sf4`}
              placeholder={getLabel('110708', '请输入收缩文本')}
              onChange={onHandleChange('ascContent')}
              disabled={false}
              className={`${clsPrefix}-top-button-locale`}
              value={btn.showContent?.ascContent}
            />
            {config.relatedDataSource && <>
              <div className="split-line">—</div>
              <LocaleEx weId={`${props.weId || ''}_6qdxdf`}
                placeholder={getLabel('313392', '请输入默认文本')}
                onChange={onHandleChange('emptyContent')}
                disabled={false}
                className={`${clsPrefix}-top-button-locale`}
                value={btn.showContent?.emptyContent}
              />
            </>}
          </div>
        </Else>
      </If>
    </div>
  );
}

export default SortConfig;