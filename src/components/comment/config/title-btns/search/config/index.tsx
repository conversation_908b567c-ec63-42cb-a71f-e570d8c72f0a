import { classnames, getLabel } from "@weapp/utils";
import { FC, memo, useMemo } from "react";
import { DiyBtn, ShowType, SearchVal } from "../../../../../../ebConfig/operation-btns/types";
import cloneDeep from "lodash-es/cloneDeep";
import { If, Then, Else } from "react-if";
import IconEidtor from "../../../../../../ebdcoms/icon-eidtor";
import { commentClsPrefix } from "../../../../../../constants";
import { defaultIconStyle } from "../../topBtns";
import LocaleEx from "../../../../../../ebdcoms/locale-ex";

const clsPrefix = commentClsPrefix;

interface SearchConfigProps extends React.Attributes {
  btns: DiyBtn<SearchVal<SearchConfigProps>>[];
  pageId: string;
  rowIndex: number;
  onChange: (val: any) => void;
}

const SearchConfig: FC<SearchConfigProps> = (props) => {
  const {
    btns, pageId, rowIndex, onChange,
  } = props;

  const cloneBtns = useMemo(() => cloneDeep(btns), [btns]);

  const btn: any = useMemo(() => cloneDeep(btns[rowIndex]), [btns, rowIndex]);

  const onHandleChange = (key?: string) => (val: any) => {
    const copyBtn = {
      ...btn,
      showContent: {
        ...btn.showContent,
      },
    };
    if (key) {
      if (copyBtn.showType === ShowType.ICON) {
        (copyBtn.showContent as any)[key] = {
          ...val,
          style: defaultIconStyle,
        };
      } else {
        (copyBtn.showContent as any)[key] = val;
      }
    } else {
      copyBtn.showContent = {
        ...val,
        style: defaultIconStyle,
      };
    }
    cloneBtns[rowIndex] = copyBtn;
    onChange({
      btns: cloneBtns,
    });
  };

  const emptyCls = classnames({
    [`${clsPrefix}-top-button-empty`]: true,
    [`${clsPrefix}-top-button-icon`]: true,
    [`${clsPrefix}-top-button-icon-hasVal`]: btn.showContent?.emptyContent?.path,
  })

  const selectedCls = classnames({
    [`${clsPrefix}-top-button-selected`]: true,
    [`${clsPrefix}-top-button-icon`]: true,
    [`${clsPrefix}-top-button-icon-hasVal`]: btn.showContent?.selectedContent?.path,
  })

  const [firstKey, secondKey] = ["emptyContent", "selectedContent"];

  // 兼容历史数据
  if (btn?.showContent && !btn?.showContent?.emptyContent) {
    const iconCls = classnames({
      [`${clsPrefix}-top-button-selected`]: true,
      [`${clsPrefix}-top-button-icon`]: true,
      [`${clsPrefix}-top-button-icon-hasVal`]: btn.showContent?.path,
    })
    return (
      <div className={`${clsPrefix}-top-button`}>
        <If weId={`${props.weId || ''}_ajhvat`} condition={btn.showType === ShowType.ICON}>
          <Then weId={`${props.weId || ''}_qif5j9`}>
            <div className={`${clsPrefix}-top-button-container`}>
              <IconEidtor weId={`${props.weId || ''}_0pn6vh`}
                className={iconCls}
                value={btn.showContent}
                onChange={onHandleChange()}
                canEditStyle={false}
                canEditTitle
                pageId={pageId}
              />
            </div>
          </Then>
          <Else weId={`${props.weId || ''}_oqb3v2`}>
            <div className={`${clsPrefix}-top-button-container`}>
              <LocaleEx
                weId={`${props.weId || ''}_u178z3`}
                placeholder={getLabel('241594', '请输入搜索文本')}
                onChange={onHandleChange()}
                disabled={false}
                className={`${clsPrefix}-top-button-locale`}
                value={btn.showContent}
              />
            </div>
          </Else>
        </If>
      </div>
    )
  }
  

  return (
    <div className={`${clsPrefix}-top-button`}>
      <If weId={`${props.weId || ''}_ajhvat`} condition={btn.showType === ShowType.ICON}>
        <Then weId={`${props.weId || ''}_qif5j9`}>
          <div className={`${clsPrefix}-top-button-container`}>
            <IconEidtor weId={`${props.weId || ''}_0pn6vh`}
              className={emptyCls}
              value={(btn.showContent as any)?.[firstKey]}
              onChange={onHandleChange(firstKey)}
              canEditStyle={false}
              canEditTitle
              pageId={pageId}
            />
            <div className="split-line">—</div>
            <IconEidtor weId={`${props.weId || ''}_a5l3ry`}
              className={selectedCls}
              value={(btn.showContent as any)?.[secondKey]}
              onChange={onHandleChange(secondKey)}
              canEditStyle={false}
              canEditTitle
              pageId={pageId}
            />
          </div>
        </Then>
        <Else weId={`${props.weId || ''}_oqb3v2`}>
          <div className={`${clsPrefix}-top-button-container`}>
            <LocaleEx weId={`${props.weId || ''}_u178z3`}
              placeholder={getLabel('241554', '请输入展开文本')}
              onChange={onHandleChange('emptyContent')}
              disabled={false}
              className={`${clsPrefix}-top-button-locale`}
              value={btn.showContent?.emptyContent}
            />
            <div className="split-line">—</div>
            <LocaleEx weId={`${props.weId || ''}_sojz94`}
              placeholder={getLabel('241555', '请输入收缩文本')}
              onChange={onHandleChange('selectedContent')}
              disabled={false}
              className={`${clsPrefix}-top-button-locale`}
              value={btn.showContent?.selectedContent}
            />
          </div>
        </Else>
      </If>
    </div>
  );
}

export default memo(SearchConfig);