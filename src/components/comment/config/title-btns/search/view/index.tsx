import { FC, useState, useMemo, useCallback, SyntheticEvent, useEffect } from "react";
import { DiyBtn, SearchVal } from "../../../../../../ebConfig/operation-btns/types";
import { CommentConfigProps } from "../../../../types";
import ConfigurableButton from "../../../../../../ebdcoms/configurable-button";
import { OPERATION_BTN, SEARCH_COMMENT, TOP_COMMENT } from "../../../../constant/eventNames";
import { Search } from "../types";
import { AnyObj } from "@weapp/ui";

interface SearchViewProps extends CommentConfigProps {
  btn: DiyBtn<SearchVal<SearchViewProps>>;
  isDesign: boolean;
  eventId: string;
}

const SearchView: FC<SearchViewProps> = (props) => {
  const {
    btn, events, eventId, isDesign, page, config
  } = props;
  const { disabled = false, action } = btn as any;

  const { client } = page!;

  const isMobile = client === "MOBILE";

  const [isEmpty, setEmpty] = useState(true);
  const [enable, setEnable] = useState(false);

  const handleSearchDataChange = (status: boolean) => {
    setEmpty(status);
  }

  const handleSearchEnableChange = (topAction: string, status?: boolean) => {
    setEnable(preState => {
      const newEnable = typeof status === "boolean" ? status : !preState;
      return action === topAction && newEnable;
    });
  }

  useEffect(() => {
    // @ts-ignore
    events?.on(TOP_COMMENT, eventId, handleSearchEnableChange);
    // @ts-ignore
    events?.on(SEARCH_COMMENT, eventId, handleSearchDataChange);
    return () => {
      // @ts-ignore
      events?.off(TOP_COMMENT, eventId, handleSearchEnableChange);
      // @ts-ignore
      events?.off(SEARCH_COMMENT, eventId, handleSearchDataChange);
    }
  }, [eventId])

  const showContent = useMemo(() => {
    // 兼容历史数据
    if (!btn?.showContent?.emptyContent) {
      return btn?.showContent;
    }
    const content = isEmpty
      ? btn?.showContent?.emptyContent
      : btn?.showContent?.selectedContent;

    return content;

  }, [isEmpty, btn?.showContent]);

  const onBtnClick = useCallback((e: SyntheticEvent) => {
    if (isDesign) return;
    const changes = !isEmpty;
    events?.emit(OPERATION_BTN, eventId, e, { ...btn, isEmpty: changes });
    events?.emit(TOP_COMMENT, eventId, action);
  }, [isDesign, isEmpty, events, eventId, btn, action]);

  const conBtn = useMemo(() => {
    const style: AnyObj = {
      icon: "var(--primary)",
    };
    const configStyle = (enable && !isMobile) || !isEmpty ? style : undefined;
    return {
      ...btn,
      showContent,
      config: {
        ...btn?.config,
        default: configStyle,
        hover: style,
      }
    }
  }, [btn, enable, isEmpty, isMobile, showContent]);

  if(config.componentShowType === 'input') return null
  return (
    <ConfigurableButton
      weId={`${props.weId || ''}_g9v1dt`}
      btn={conBtn}
      onClick={onBtnClick}
      disabled={disabled}
    />
  );
}

export default SearchView;