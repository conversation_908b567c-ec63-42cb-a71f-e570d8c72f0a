import { Action, CommentConfigType, CommentConfigKeys } from "../types";
import { TransformHandler, TransformType } from "./types";

type TabTransformHandler = TransformHandler<CommentConfigType, Action>;

const transformHandler: TabTransformHandler = function transformHandler(changes) {
  // pc和移动端不同步的数据key
  const notSyncConfigKeys: CommentConfigKeys[] = [
    "paginationType",
    "optionConfig",
    "printHideInput",
    "printHorizontal",
    "actionConfig",
    "showText",
    "showTotal",
    "displaySetting",
  ]
  const changeValue: CommentConfigType = {};

  Object.keys(changes).forEach(key => {

    // 放行允许同步修改的数据
    if (!notSyncConfigKeys.includes(key)) {
      changeValue[key] = changes[key];
    }
  })
  return changeValue
};

// 区分pc和移动端config配置
const transform: TransformType = {
  /**
   * 修改pc端配置时触发
   */
  toMobile(changes, actData) {
    return transformHandler.call(this, changes, actData, "PC");
  },

  /**
   * 修改移动端配置时触发
   */
  toPC(changes, actData) {
    return transformHandler.call(this, changes, actData, "MOBILE");
  },
};

export default transform;