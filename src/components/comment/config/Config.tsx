import { getLabel } from "@weapp/utils";
import {
  commentClsPrefix,
  uiPropsDesignClsPrefix,
  LABEL_SPAN,
  LABEL_SPAN_LG,
  LABEL_SPAN_NONE,
  LABEL_SPAN_13
} from "../../../constants";
import { commonConfig } from "../../../ebConfig";
import createConfig from "../core/createConfig";
import ActionConfig from "./items/action-config";
import OptionConfig from "./items/option-config";
import PaginationType from "./items/PaginationType";
import transform from "./transform";
import { ConfigType } from "./types";
import { CommentConfigType } from "../types";
import { hasBlog, hasEchart, hasSearch, hasSort, getGroupsByComponentType } from "../utils";
import SelectModule from "./items/SelectModule";
import getDefaultOperationBtns from "./title-btns/getDefaultOperationBtns";
import ComponentType from './items/ComponentType';
import EditModule from './items/EditModule'
import ShowText from './items/ShowText';
import EditAvatar from './items/EditAvatar';
import EditAnonymous from './items/EditAnonymous';
import RefreshType from './items/RefreshType';
import ReplyUnderComment from "./items/ReplyUnderComment";
import ReplySettingConfig from "./items/replySetting-config";
import RelatedDataSource from "./items/RelatedDataSource";
import CustomFields from "./items/CustomFields";
import FilterCondition from "./items/FilterCondition";
import SortFields from "./items/SortFields";
import DisplaySetting from "./items/displaySetting/DisplaySetting";
import eventAction from "./eventAction";
import { formatFormDatas } from "../utils/formatFormDatas";

// @ts-ignore
export const config: ConfigType = {
  ...commonConfig,
  /** 是否使用标题配置 */
  title: true,
  /** 是否使用底部区域配置 */
  footer: true,
  /** 是否使用固定区域配置 */
  fixedArea: false,
  /** 是否使用设置项分组显示 */
  showTab: true,
  /** 区分pc与移动端配置 */
  transform: transform,
  getGroups: (config: any) => {
    return getGroupsByComponentType(config.componentShowType)
  },
  tabs: [{
    content: getLabel("223932", "全部"),
    id: "all_set",
    isAllConfig: true
  }, {
    content: getLabel("206978", "评论输入设置"),
    id: "input_set",
  }, {
    content: `${getLabel('57083','评论')}${getLabel('20442','列表') }`,
    id: "list_set",
  }],
  items: {
    moduleValue: {
      itemType: "CUSTOM",
      className: `${commentClsPrefix}-config-select`,
      customRender: SelectModule,
    },
    hasSort: {
      itemType: "SWITCH",
    },
    hasSearch: {
      itemType: "SWITCH",
    },
    hasEchart: {
      itemType: "SWITCH",
    },
    hasEdit: {
      itemType: "CUSTOM",
      className: `${commentClsPrefix}-config-select`,
      customRender: EditModule,
    },
    componentShowType: {
      itemType: "CUSTOM",
      className: `${commentClsPrefix}-config-radio-group`,
      customRender: ComponentType,
    },
    relatedDataSource: {
      itemType: "CUSTOM",
      customRender: RelatedDataSource
    },
    useOriginalTableFields: {
      itemType: "SWITCH",
    },
    customFields: {
      itemType: "CUSTOM",
      customRender: CustomFields
    },
    filterCondition: {
      itemType: "CUSTOM",
      customRender: FilterCondition
    },
    sortFields: {
      itemType: "CUSTOM",
      customRender: SortFields
    },
    showText: {
      itemType: "CUSTOM",
      customRender: ShowText,
    },
    useRichText: {
      itemType: "SWITCH",
    },
    showEditAvatar: {
      itemType: "CUSTOM",
      customRender: EditAvatar,
    },
    usePrivated: {
      itemType: "SWITCH",
    },
    useAnonymous: {
      itemType: "CUSTOM",
      customRender: EditAnonymous,
    },
    allowAnonymousSetting: {
      itemType: "SWITCH",
    },
    defaultAnonymousByCheck: {
      itemType: "SWITCH",
    },
    useBlog: {
      itemType: "SWITCH",
    },
    usePosition: {
      itemType: "SWITCH",
    },
    usePhrase: {
      itemType: "SWITCH",
    },
    useSignApprove: {
      itemType: "SWITCH",
    },
    useSignature: {
      itemType: "SWITCH",
    },
    actionConfig: {
      itemType: "CUSTOM",
      customRender: ActionConfig,
    },
    hoverShow: {
      itemType: "SWITCH",
    },
    showEmpty: {
      itemType: "SWITCH",
    },
    showListAvatar: {
      itemType: "SWITCH",
    },
    showSource: {
      itemType: "SWITCH",
    },
    showTime: {
      itemType: "SWITCH",
    },
    printHorizontal: {
      itemType: "SWITCH",
    },
    printHideInput: {
      itemType: "SWITCH",
    },
    replyUnderComment: {
      itemType: "CUSTOM",
      customRender: ReplyUnderComment
    },
    showFloorExpand: {
      itemType: "SWITCH",
    },
    replyFloorExpand: {
      itemType: "SWITCH",
    },
    replySetting: {
      itemType: "CUSTOM",
      customRender: ReplySettingConfig
    },
    displaySetting: {
      itemType: "CUSTOM",
      customRender: DisplaySetting
    },
    optionConfig: {
      itemType: "CUSTOM",
      customRender: OptionConfig,
    },
    paginationType: {
      itemType: "CUSTOM",
      className: `${commentClsPrefix}-config-select`,
      customRender: PaginationType,
    },
    pageSize: {
      itemType: "SELECT",
      className: `${commentClsPrefix}-config-select`,
      options: [{
        id: "5",
        content: "5",
      }, {
        id: "10",
        content: "10",
      }, {
        id: "20",
        content: "20",
      }, {
        id: "50",
        content: "50",
      }],
    },
    showTotal: {
      itemType: "SWITCH",
    },
    refresh: {
      itemType: "CUSTOM",
      className: `${commentClsPrefix}-config-select`,
      customRender: RefreshType,
    },
  },
  layout: [
    // [{
    //     id: 'moduleValue',
    //     label: getLabel("223922", "选择模块"),
    //     labelSpan: LABEL_SPAN,
    //     items: ['moduleValue'],
    //     groupId: 'data',
    //     wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
    //   },
    // ],
    // [{
    //     id: 'hasSort',
    //     label: getLabel("223923", "启用排序"),
    //     labelSpan: LABEL_SPAN_LG,
    //     items: ['hasSort'],
    //     groupId: 'data',
    //     wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
    //   },
    // ],
    // [{
    //     id: 'hasSearch',
    //     label: getLabel("223924", "启用高级搜索"),
    //     labelSpan: LABEL_SPAN_LG,
    //     items: ['hasSearch'],
    //     groupId: 'data',
    //     wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
    //   },
    // ],
    // [{
    //     id: 'hasEchart',
    //     label: getLabel("223925", "启用筛选"),
    //     labelSpan: LABEL_SPAN_LG,
    //     items: ['hasEchart'],
    //     groupId: 'data',
    //     wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
    //   },
    // ],
    [
      {
        id: 'componentShowType',
        label: getLabel("244829", "组件类型"),
        labelSpan: LABEL_SPAN,
        items: ['componentShowType'],
        groupId: 'showType',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'relatedDataSource',
        label: "",
        labelSpan: LABEL_SPAN_NONE,
        items: ['relatedDataSource'],
        groupId: 'dataSource',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'useOriginalTableFields',
        label: getLabel("312133", "使用原表字段"),
        labelSpan: LABEL_SPAN_LG,
        items: ['useOriginalTableFields'],
        helpTip: getLabel("312134", "“原表字段”指关联数据源里所选评论表的所属eb表单；启用后，可在数据过滤、排序设置、显示设置、刷新组件等设置中使用原表字段"),
        groupId: 'dataSource',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'customFields',
        label: getLabel("312131", "评论表自定义字段"),
        labelSpan: LABEL_SPAN_LG,
        items: ['customFields'],
        groupId: 'dataSource',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'filterCondition',
        label: "",
        labelSpan: LABEL_SPAN_NONE,
        items: ['filterCondition'],
        groupId: 'dataFilter',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'sortFields',
        label: "",
        labelSpan: LABEL_SPAN_NONE,
        items: ['sortFields'],
        groupId: 'dataSort',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'hasEdit',
        label: getLabel("206985", "评论输入框"),
        labelSpan: LABEL_SPAN,
        items: ['hasEdit'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showText',
        label: getLabel("206986", "输入框默认展开"),
        labelSpan: LABEL_SPAN_LG,
        items: ['showText'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'useRichText',
        label: getLabel("206987", "启用富文本"),
        labelSpan: LABEL_SPAN_LG,
        items: ['useRichText'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showEditAvatar',
        label: getLabel("206988", "输入框显示头像"),
        labelSpan: LABEL_SPAN_LG,
        items: ['showEditAvatar'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'usePrivated',
        label: getLabel("206989", "启用私评"),
        labelSpan: LABEL_SPAN_LG,
        items: ['usePrivated'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'useAnonymous',
        label: getLabel('288127','启用匿名'),
        labelSpan: LABEL_SPAN_LG,
        items: ['useAnonymous'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'allowAnonymousSetting',
        label: getLabel('288128','允许用户设置'),
        labelSpan: LABEL_SPAN_LG,
        items: ['allowAnonymousSetting'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'defaultAnonymousByCheck',
        label: getLabel('261575','默认启用'),
        labelSpan: LABEL_SPAN_LG,
        items: ['defaultAnonymousByCheck'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'useBlog',
        label: getLabel("223926", "启用发布日报"),
        labelSpan: LABEL_SPAN_LG,
        items: ['useBlog'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'usePosition',
        label: getLabel("223927", "启用位置"),
        labelSpan: LABEL_SPAN_LG,
        items: ['usePosition'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'usePhrase',
        label: getLabel("223928", "启用常用短语"),
        labelSpan: LABEL_SPAN_LG,
        items: ['usePhrase'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'useSignApprove',
        label: getLabel("244069", "启用手写签批"),
        labelSpan: LABEL_SPAN_LG,
        items: ['useSignApprove'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'useSignature',
        label: getLabel("244068", "启用电子签名"),
        labelSpan: LABEL_SPAN_LG,
        items: ['useSignature'],
        groupId: 'input',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'actionConfig',
        label: "",
        labelSpan: LABEL_SPAN_NONE,
        items: ['actionConfig'],
        groupId: 'inputBox',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showEmpty',
        label: getLabel("206990", "默认展示空状态"),
        labelSpan: LABEL_SPAN_LG,
        items: ['showEmpty'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showListAvatar',
        label: getLabel("206991", "列表显示头像"),
        labelSpan: LABEL_SPAN_LG,
        items: ['showListAvatar'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showSource',
        label: getLabel("206992", "显示评论来源"),
        labelSpan: LABEL_SPAN_LG,
        items: ['showSource'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showTime',
        label: getLabel("206993", "显示评论时间"),
        labelSpan: LABEL_SPAN_LG,
        items: ['showTime'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'printHorizontal',
        label: getLabel("206994", "打印时水平展示"),
        labelSpan: LABEL_SPAN_LG,
        items: ['printHorizontal'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'printHideInput',
        label: getLabel("206995", "打印时隐藏评论输入框"),
        labelSpan: LABEL_SPAN_LG,
        items: ['printHideInput'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'replyUnderComment',
        label: getLabel("255854", "回复显示在评论下方"),
        labelSpan: LABEL_SPAN_13,
        items: ['replyUnderComment'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showFloorExpand',
        label: getLabel('255856', '回复列表允许折叠'),
        labelSpan: LABEL_SPAN_LG,
        items: ['showFloorExpand'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'replyFloorExpand',
        label: getLabel('260948', '回复列表默认展开'),
        labelSpan: LABEL_SPAN_LG,
        items: ['replyFloorExpand'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'replySetting',
        label: getLabel('255857', '回复操作设置'),
        labelSpan: LABEL_SPAN_LG,
        items: ['replySetting'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'displaySetting',
        label: getLabel('233629', '显示设置'),
        labelSpan: LABEL_SPAN_LG,
        items: ['displaySetting'],
        groupId: 'display',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'optionConfig',
        label: "",
        labelSpan: LABEL_SPAN_NONE,
        items: ['optionConfig'],
        groupId: 'operation',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'hoverShow',
        label: getLabel("255850", "鼠标悬停显示评论操作"),
        labelSpan: LABEL_SPAN_LG,
        items: ['hoverShow'],
        groupId: 'operation',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'paginationType',
        label: getLabel("206996", "分页模式"),
        labelSpan: LABEL_SPAN,
        items: ['paginationType'],
        groupId: 'pagination',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'pageSize',
        label: getLabel("206997", "每页条数"),
        labelSpan: LABEL_SPAN,
        items: ['pageSize'],
        groupId: 'pagination',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'showTotal',
        label: getLabel("223929", "显示总数"),
        labelSpan: LABEL_SPAN,
        items: ['showTotal'],
        groupId: 'pagination',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'refresh',
        label: getLabel("244067", "定时刷新"),
        labelSpan: LABEL_SPAN,
        items: ['refresh'],
        groupId: 'otherSetting',
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
  ],
  references: {
    moduleValue: ['hasSort', 'hasSearch', 'hasEchart', "useBlog"],
    hasEdit: ['showText', 'showEditAvatar'],
    enableModule: ['actionConfig', 'optionConfig'],
    componentShowType: ['hasEdit', 'showText', 'showEditAvatar'],
    showText: ['showEditAvatar'],
    replyUnderComment: ['optionConfig'],
    actionConfig: ['optionConfig'],
    useRichText: ['optionConfig'],
    usePrivated: ['optionConfig'],
    useAnonymous: ['optionConfig'],
    allowAnonymousSetting: ['optionConfig'],
    defaultAnonymousByCheck: ['optionConfig'],
    usePosition: ['optionConfig'],
    usePhrase: ['optionConfig'],
    useSignApprove: ['optionConfig'],
    useSignature: ['optionConfig'],
    optionConfig: ['displaySetting'],
    relatedDataSource: ['customFields', 'filterCondition', 'sortFields', 'displaySetting', 'optionConfig', 'topOperatBtns', 'titleEnabled', 'title'],
    useOriginalTableFields: ['filterCondition', 'sortFields', 'displaySetting']
  },
  formatFormDatas,
  customHide: function (col: any) {
    const _this = this as any;
    let hide = false;
    let { id, label } = col;
    const { client, data = {} } = _this?.props || {};
    const { moduleValue, hasEdit, showFloorExpand, replyUnderComment, relatedDataSource } = data as CommentConfigType;
    if (id === "isHorizontal") {
      hide = client === "MOBILE";
    }
    if (id === "showText") {
      hide = hasEdit === "hidden";
    }
    if (id === "showEditAvatar") {
      hide = hasEdit === "hidden";
    }
    if (id === "printHorizontal") {
      hide = client === "MOBILE";
    }
    if (id === "usePhrase") {
      hide = client === "PC";
    }
    if (id === "usePosition") {
      hide = client === "PC";
    }
    if (id === "useSignature") {
      hide = client === "PC";
    }
    if (id === "useSignApprove") {
      hide = client === "PC";
    }
    // if (id === "hasSort") {
    //   hide = !hasSort(moduleValue!);
    // }
    // if (id === "hasSearch") {
    //   hide = !hasSearch(moduleValue!);
    // }
    // if (id === "hasEchart") {
    //   hide = !hasEchart(moduleValue!);
    // }
    if (id === "useBlog") {
      hide = !hasBlog(moduleValue!) || true;
    }
    if (id === "moduleValue") {
      hide = true;
    }
    if (id === "allowAnonymousSetting") {
      hide = true;
    }
    if (id === "defaultAnonymousByCheck") {
      hide = true;
    }
    if (id === "hoverShow") {
      hide = client === "MOBILE";
    }
    if (['showFloorExpand', 'replySetting'].includes(id)) {
      hide = !replyUnderComment
    }
    if (id === 'replyFloorExpand') {
      hide = !(showFloorExpand && replyUnderComment)
    }
    if (['useOriginalTableFields', 'customFields'].includes(id)) {
      hide = !relatedDataSource
    }
    if (['showListAvatar', 'showSource', 'showTime'].includes(id)) {
      hide = true;
    }
    return { ...col, hide, label };
  },
  // @ts-ignore
  getDefaultOperationBtns,
  eventAction
}

export default createConfig(config);
