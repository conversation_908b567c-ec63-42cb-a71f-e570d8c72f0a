import { createRef } from "react"
import { classnames, eventEmitter } from "@weapp/utils"
import { appName, commentClsPrefix } from "../../../constants"
import { CommentEvents } from "../constant"
import { CustomFields } from "../config/items/displaySetting/constants";
import { openCommentHrmCard } from "../utils";

const prefixCls = commentClsPrefix;

type SlotType = 'avatar' | 'user' | 'source' | 'time' | 'content' | 'operate' | 'operateBottomArea'

const createMPlugin = (pluginName: string, eventNameSuffix: string = '') => {
  return class MCommentPlugin {
    name = pluginName

    slotRefs: any = {}

    registerSlotRef = (refObj: any) => {
      const { rowId } = refObj
      const index = this.slotRefs[rowId]?.findIndex((obj: any) => obj.id === refObj.id) ?? -1
      if (index <= -1) {
        if (this.slotRefs[rowId]) {
          this.slotRefs[rowId].push(refObj)
        } else {
          this.slotRefs[rowId] = [refObj]
        }
      } else {
        this.slotRefs[rowId].splice(index, refObj)
      }
    }

    unRegisterSlotRef = (refObj: any) => {
      const { rowId } = refObj
      this.slotRefs[rowId] = this.slotRefs[rowId].filter((obj: any) => obj.id !== refObj.id)
    }

    clearSlotRefs = () => {
      this.slotRefs = {}
    }

    eventNames: string[] = []

    updateSlotRefs = (type: 'register' | 'unregister', refObj: any, comId: string) => {
      switch (type) {
        case 'register':
          this.registerSlotRef(refObj)
          break
        case 'unregister':
          this.unRegisterSlotRef(refObj)
          break
      }
      const { id } = refObj
      // 触发comment渲染
      this.eventNames.forEach(eventName => {
        if (id.includes('config') && eventName.includes('config')) {
          eventEmitter.emit(appName, eventName, this.slotRefs)
        }
        if (!id.includes('config') && !eventName.includes('config')) {
          eventEmitter.emit(appName, eventName, this.slotRefs)
        }
      })
      //eventEmitter.emit(appName, `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${comId}${eventNameSuffix}`, this.slotRefs)
    }

    customRenderCellContent = (opts: any, type: SlotType, hook: any) => {
      const { data, field, value } = opts
      const { id: rowId, comId, isConfig } = data
      const { id: fieldId, uid: fieldUid } = field
      const id = `${comId}_${rowId}_${fieldUid}` + (isConfig ? '_config' : '')
      // 查找当前ref，存在则直接引用
      const currentRef = this.slotRefs[rowId]?.find((obj: any) => obj.id === id)?.ref
      const ref: any = currentRef?.current ? currentRef : createRef()
      const target = document.getElementById(id)
      if (!target) { // 找不到target，就通过updateSlotRefs触发渲染（根据currentRef?.current判断可能会不准确，因此改用id查找dom节点）
        this.updateSlotRefs('register', { rowId, fieldId, fieldUid, id, type, ref }, comId)
      }
      const cls = classnames(`${prefixCls}-custom-cell`, {
        [`${prefixCls}-custom-cell-fullwidth`]: type === 'operateBottomArea'
      })
      const onclick = () => () => {
        console.log('onclick')
      }
      return <div id={id} ref={ref} onClick={onclick()} className={cls}></div>
    }

    renderCell1 = (hook: any) => {
      //console.log('renderCell')
      return (opts: any, ...args: any) => {
        //console.log('renderCell',opts.field.id)
        let customRenderContent: any = <></>
        const { field, value, data } = opts
        const { employeeId, name } = data?.commentor || {}
        const { department, subcompany, position } = data?.otherParam?.hrmInfo || {}
        const { id } = field
        switch (id) {
          case CustomFields.COMMENTER: // 评论人
            customRenderContent = this.customRenderCellContent(opts, 'avatar', hook)
            break
          case CustomFields.COMMENTER_NAME: // 评论人姓名
            customRenderContent = employeeId ? <span className={`${prefixCls}-hrm-span`} onClick={openCommentHrmCard(employeeId, '', true)}>{name}</span> : hook(opts, ...args)
            break
          case CustomFields.COMMENTER_DEPARTMENT: // 评论人部门
            customRenderContent = department?.id ? <span className={`${prefixCls}-hrm-span`} onClick={openCommentHrmCard(department.id, 'department', true)}>{department.name}</span> : hook(opts, ...args)
            break
          case CustomFields.COMMENTER_SUB: // 评论人分部
            customRenderContent = subcompany?.id ? <span className={`${prefixCls}-hrm-span`} onClick={openCommentHrmCard(subcompany.id, 'subcompany', true)}>{subcompany.name}</span> : hook(opts, ...args)
            break
          case CustomFields.COMMENTER_POST: // 评论人岗位
            customRenderContent = position?.id ? <span className={`${prefixCls}-hrm-span`} onClick={openCommentHrmCard(position.id, 'position', true)}>{position.name}</span> : hook(opts, ...args)
            break
          case CustomFields.CONTENT: // 评论内容
            customRenderContent = this.customRenderCellContent(opts, 'content', hook)
            break
          case CustomFields.SOURCE: // 来源
            customRenderContent = this.customRenderCellContent(opts, 'source', hook)
            break
          case CustomFields.TIME: // 时间
            customRenderContent = this.customRenderCellContent(opts, 'time', hook)
            break
          case CustomFields.OPTION: // 操作按钮
            customRenderContent = this.customRenderCellContent(opts, 'operate', hook)
            break
          case CustomFields.OPTIONAREA: // 评论操作区域
            customRenderContent = this.customRenderCellContent(opts, 'operateBottomArea', hook)
            break
          case CustomFields.USER: // 评论人
          case CustomFields.M_USER:
            customRenderContent = this.customRenderCellContent(opts, 'user', hook)
            break
          default:
            customRenderContent = hook(opts, ...args)
        }
        return (
          <>
            {customRenderContent}
          </>
        )
      }
    }

    /*renderListItem = (hook: any) => {
      return (rowDatas: any, index: any, ...args: any) => {
        return <div className="custom-renderListItem">{hook(rowDatas, index, ...args)}</div>
      }
    }

    renderLayout = (hook: any) => {
      return (rowDatas: any, index: any, ...args: any) => {
        return <div className="custom-renderLayout">{hook(rowDatas, index, ...args)}</div>
      }
    }*/

    getListCellStyle = (...args: any) => {
      const [styleObj, opts] = args
      const { style = {} } = styleObj
      const { id } = opts?.field || {}
      if ([CustomFields.OPTION, CustomFields.M_USER].includes(id)) { // 操作按钮，移动端评论人
        // 样式改造
        styleObj.style = {
          ...style,
          'flex-grow': '1'
        }
      }
      return [styleObj, opts]
    }
  }
}

export default createMPlugin