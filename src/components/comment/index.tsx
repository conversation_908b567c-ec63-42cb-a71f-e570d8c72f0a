import { Loadable } from "@weapp/ui";
import CommentConfig from "./config";
import CommentDesign from "./design";
import CommentView from "./view";
import { CommentTitleType } from "./components/type";

export default {
  Design: CommentDesign,
  View: CommentView,
  Config: CommentConfig,
};


export { default as CommentHeader } from "./components/header-com";


export const CommentTitle = Loadable({
  name: 'Condition', loader: () => import(
    /* webpackChunkName: "comment_title" */
    './components/Title'
  )
}) as CommentTitleType;