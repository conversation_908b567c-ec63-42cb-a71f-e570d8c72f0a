import { AnyObj } from "@weapp/ui";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../types";
import qs from "qs";
import { request } from "@weapp/utils";
import { listConfig } from "../config/items/option-config/contants";

export const findCommentCount = (urlModule: ModuleKey, params: AnyObj) => {
  return request({
    url: `/api/${urlModule}/comment/findCommentCount`,
    method: "POST",
    data: qs.stringify(params),
  })
}

export const addBlog = (params?: AnyObj) => {
  return request({
    url: "/api/blog/prior/addBlog",
    method: "POST",
    data: params
  })
}

export const getCommentTopShareList = (params?: AnyObj) => {
  return request({
    ...listConfig,
    data: params
  })
}

/** 预绑定&查询评论物理表 */
export const getCommentForm = (params?: AnyObj) => {
  return request({
    url: "/api/ebuilder/form/common/ebCommentFormSetting/getCommentForm",
    method: "POST",
    data: params
  })
}

/** 正式绑定评论物理表 */
export const toggleSettingBindType = (params?: AnyObj) => {
  return request({
    url: "/api/ebuilder/form/common/ebCommentFormSetting/toggleSettingBindType",
    method: "POST",
    data: params
  })
}

export const createUrl = "/api/ebuilder/form/common/ebComment/createComment"

/** 新建评论 */
export const createComment = (params?: AnyObj) => {
  return request({
    url: createUrl,
    method: "POST",
    data: params
  })
}

export const commentUrl = "/api/ebuilder/form/common/ebComment/pageQueryAdvancedSearch"

/** 查询评论 */
export const pageQueryAdvancedSearch = (params?: AnyObj) => {
  return request({
    url: commentUrl,
    method: "POST",
    data: params
  })
}

export const queryChildrenUrl = "/api/ebuilder/form/common/ebComment/queryChildrenComments"

/** 查询子评论 */
export const queryChildrenComments = (params?: AnyObj) => {
  return request({
    url: queryChildrenUrl,
    method: "POST",
    data: params
  })
}

export const deleteUrl = "/api/ebuilder/form/common/ebComment/deleteComment"

/** 删除评论 */
export const deleteComment = (params?: AnyObj) => {
  return request({
    url: deleteUrl,
    method: "POST",
    data: params
  })
}

export const updataUrl = "/api/ebuilder/form/common/ebComment/updateComment"

/** 编辑评论 */
export const updateComment = (params?: AnyObj) => {
  return request({
    url: updataUrl,
    method: "POST",
    data: params
  })
}

export const getFormIdByObjIdUrl = '/api/ebuilder/form/common/ebCommentFormSetting/getFormIdByObjId'

/** 根据objid获取itemFormId */
export const getFormIdByObjId = (params?: AnyObj) => {
  return request({
    url: getFormIdByObjIdUrl,
    method: "GET",
    params
  })
}

export const deleteCommentFormSettingUrl = '/api/ebuilder/form/common/ebCommentFormSetting/deleteCommentFormSetting'

/** 删除评论物理表绑定设置 */
export const deleteCommentFormSetting = (params?: AnyObj) => {
  return request({
    url: deleteCommentFormSettingUrl,
    method: "GET",
    params
  })
}

export const commentCommentedUrl = '/api/ebuilder/form/common/ebComment/commentCommented'

/** 查询直接子评论 */
export const commentCommented = (params?: AnyObj) => {
  return request({
    url: commentCommentedUrl,
    method: "POST",
    data: params
  })
}

export const statisticUrl = '/api/ebuilder/form/common/ebComment/selectCommentCountAdvancedSearch'

export const getCommentFormFieldsUrl = '/api/ebuilder/form/common/ebCommentFormSetting/getCommentFormFields'

/** 查询评论表字段 */
export const getCommentFormFields = (params?: AnyObj) => {
  return request({
    url: getCommentFormFieldsUrl,
    method: "POST",
    data: params
  })
}

export const updateTransmitCountUrl = '/api/ebuilder/form/common/ebComment/updateTransmitCount'

/** 更新转任务数量 */
export const updateTransmitCount = (params?: AnyObj) => {
  return request({
    url: updateTransmitCountUrl,
    method: "POST",
    data: params
  })
}

/** 筛选组件使用sourceField自定义数据源时，传入获取选项接口路径 */
export const fieldSelectOptionUrl = '/api/ebuilder/common/ds/options'

/** 筛选组件使用sourceField自定义数据源时，传入获取选项接口路径 */
export const fieldSelectOption = (params?: AnyObj) => {
  return request({
    url: fieldSelectOptionUrl,
    method: "GET",
    params: params
  })
}