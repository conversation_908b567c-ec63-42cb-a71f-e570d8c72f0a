import { utils } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import { ClientType } from "@weapp/ebdcoms";
import { CommonConfigData } from "../types";

const { deepClone } = utils

export type InitDataConfig = {
  client?: ClientType,
  value?: CommonConfigData[]
};

export type ReplySettingConfigData = {
  hoverShowReplyOption?: boolean,
  options?: CommonConfigData[]
}

export const initActionConfig = (config: InitDataConfig) => {

  const { client } = config;

  const result =[{
    id: "at",
    name: getLabel("207009", "提到"),
    iconName: "Icon-@My-new-feedback-o",
    enable: true,
    showName: true,
  },
  {
    id: "emoticon",
    name: getLabel("207010", "表情"),
    iconName: "Icon-smiling-face",
    enable: true,
    showName: true,
  },
  {
    id: "associated",
    name: getLabel("207011", "事项关联"),
    iconName: "Icon-share-o",
    enable: true,
    showName: true,
  },
  {
    id: "upload",
    name: getLabel("207012", "附件"),
    iconName: "Icon-enclosure-o",
    enable: true,
    showName: true,
  },
  client === "PC" && {
    id: "address",
    name: getLabel("207013", "位置"),
    iconName: "Icon-Mobile-attendance-o",
    enable: false,
    showName: true,
  },
  client === "PC" && {
    id: "phrase",
    name: getLabel("207014", "常用短语"),
    iconName: "Icon-Keep-a-record",
    enable: false,
    showName: true,
  },
  client === "PC" && {
    id: "signApprove",
    name: getLabel('179321', '手写签批'),
    iconName: "Icon-autograph02",
    enable: false,
    showName: true,
  },
  client === "PC" && {
    id: "signature",
    name: getLabel('67426', '电子签名'),
    iconName: "Icon-rename",
    enable: false,
    showName: true,
  },
  client === "MOBILE" && {
    id: "voiceToText",
    name: getLabel("207015", "语音转文字"),
    iconName: "Icon-Voice-to-text01",
    enable: client === "MOBILE",
    showName: true,
  },
  client === "MOBILE" && {
    id: "recording",
    name: getLabel("207016", "语音"),
    iconName: "Icon-Message-voice-o",
    enable: client === "MOBILE",
    showName: true,
  }]
  return result.filter(item => item) as CommonConfigData[];
}

export const initOptionConfig = (config: InitDataConfig = {}) => {

  const { client } = config;

  const result = [
    {
      id: "delete",
      name: getLabel("207017", "删除"),
      iconName: "Icon-delete",
      enable: true,
      showName: client === "PC",
    }, {
      id: "reply",
      name: getLabel("207018", "回复"),
      iconName: "Icon-Comment-reply",
      enable: true,
      showName: client === "PC",
    }, {
      id: "quote",
      name: getLabel("261112", "引用"),
      iconName: "Icon-quote01",
      enable: false,
      showName: false,
    }, {
      id: "task",
      name: getLabel("207019", "转任务"),
      iconName: "Icon-share02",
      enable: true,
      showName: client === "PC",
    }, {
      id: "edit",
      name: getLabel("207020", "编辑"),
      iconName: "Icon-daily-o",
      enable: true,
      showName: client === "PC",
    }, {
    //   id: "topic",
    //   name: getLabel("223960", "话题"),
    //   iconName: "Icon-topic-of-conversation-o",
    //   enable: true,
    //   showName: client === "PC",
    // }, {
      id: "upvote",
      name: getLabel("223961", "赞"),
      iconName: "Icon-satisfied-o",
      enable: true,
      showName: client === "PC",
    }, {
      id: "pinToTop",
      name: getLabel('261113', '置顶'),
      iconName: "Icon-Mark-top",
      enable: false,
      showName: false,
    }, {
      id: "unpin",
      name: getLabel('261114', '取消置顶'),
      iconName: "Icon-Cancel-topping",
      enable: false,
      showName: false,
    }, {
      id: "sort",
      customName: [undefined, undefined],
      customMessage: [undefined, undefined],
      name: [getLabel('223920','正序'), getLabel('223919','倒序')],
      iconName: ["Icon-positive-sequence02-mcolor", "Icon-Reverse-order02-mcolor"],
      showName: client === "PC",
      settingData: ["asc", "desc"]
    }
  ]

  return result.filter(item => item) as CommonConfigData[];
}

export const initReplySettingConfig = (config: InitDataConfig = {}) => {

  const replyResult = {
    hoverShowReplyOption: true,
    options: [
      {
        id: "delete",
        name: getLabel("207017", "删除"),
        iconName: "Icon-delete",
        enable: true,
      }, {
        id: "edit",
        name: getLabel("207020", "编辑"),
        iconName: "Icon-daily-o",
        enable: true,
      }, {
        id: "reply",
        name: getLabel("207018", "回复"),
        iconName: "Icon-Comment-reply",
        enable: true,
      }, {
        id: "task",
        name: getLabel("207019", "转任务"),
        iconName: "Icon-share02",
        enable: true,
      }
    ]
  }

  return replyResult as ReplySettingConfigData
}

export const getPaginationType = (config: InitDataConfig = {}) => {

  const { client } = config;

  const paginationType = client === "MOBILE" ? "scroll" : "more";

  return paginationType;
}