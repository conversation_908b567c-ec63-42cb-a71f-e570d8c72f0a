import { AnyObj, FormInitAllDatas, FormLayoutType } from "@weapp/ui";
import { getLabel } from "@weapp/utils";

export const layout: Array<FormLayoutType> = [
  [
    { id: "content", label: getLabel('240119','评论内容'), items: ['operate_content', 'value_content'], labelSpan: 8, groupId: 'commonGroup', hide: false, needQuickSearch: true },
    { id: "commentor", label: getLabel('240120','评论人'), items: ['operate_commentor', 'value_commentor'], labelSpan: 8, groupId: 'commonGroup', hide: false },
    { id: "add_time", label: getLabel('208509','评论时间'), items: ['value_time'], labelSpan: 8, groupId: 'commonGroup', hide: false },
  ],
  [
    { id: "type", label: getLabel('240122','用户类型'), items: ['operate_type', 'value_type'], labelSpan: 8, groupId: 'otherGroup', hide: false },
  ]
]
  
export const groups = [{
  "id": "commonGroup",
  "title": "",
  "visible": true,
  "custom": false
}, {
  "id": "otherGroup",
  "title": getLabel("240121", "其他条件"),
  "visible": true,
  "custom": true
}]

export const initItems = {
  'value_time': {
    itemType: "DATETIMEPICKER",
    style: { width: "100%" },
    customParameterName: 'value',
    isRange: true,
    customParameters: {
      type: "date",
    },
  },
}

export const searchInitDatas = {
  items: initItems
} as unknown as FormInitAllDatas;

export const customSearchConfig = {
  groups,
  layout
}

export const cascadeRules: AnyObj = {
  'operate_content': {
    'like': ["operate_content", "value_content"],
    'unlike': ["operate_content", "value_content"],
    'isnull': ["operate_content"],
    'isnotnull': ["operate_content"],
  },
}