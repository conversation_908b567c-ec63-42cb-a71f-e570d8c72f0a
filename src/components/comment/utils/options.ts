import { IComData } from '@weapp/designer';
import { setCommentIdInfo } from './getApiDatas';

export const handleBeforeCreate = async (com: IComData, refCom?: IComData) => {

  //const commentIdInfo = await setCommentIdInfo(com, refCom);

  // targetId置空，组件初始化时重新生成
  if (com.config.targetId) {
    com.config.targetId = undefined
  }
  if (refCom?.config?.targetId) {
    refCom.config.targetId = undefined
  }

  return true;
}
