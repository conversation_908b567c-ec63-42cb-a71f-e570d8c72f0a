import { AnyObj, CommentOptionType, utils, HrmCard } from '@weapp/ui';
import modules from "../constant/modules";
import { CommentConfigType, ModuleKey } from "../types";
import { cloneDeep, getLabel, getLocaleDate, getLocaleDateTime } from "@weapp/utils";
import groups from '../constant/groups';
import { Page } from '@weapp/ebdcoms';
import { getInitTempateData, getInitTempateDataM } from '../config/items/displaySetting/constants';
import ebdcoms from '../../../utils/ebdcoms';

const { deepClone } = utils
const { openHrmCard, openMobileHrmCard } = HrmCard;

/**
 * 格式化显示时间(包括时区)
 * @param time 需要格式化的时间
 * @param type 格式化类型
 */
export const formatDate = (time?: number | string | Date, type: "time" | "data" = "data") => {
  const formatFunc = type === "time" ? getLocaleDateTime : getLocaleDate;
  return formatFunc(time)?.toString?.();
}

// 获取crm下的二级模块
const crmModules = modules.filter(item => item.urlModule === "crm");
const crmModuleKeys = crmModules.map(item => item.id);

export const hasSearch = (module: ModuleKey) => {
  const searchAdvancedModules: ModuleKey[] = [
    "customer",
    "saleChance",
    "contract",
    "production",
    "competitor",
    "project",
  ];
  return searchAdvancedModules.includes(module);
}

export const hasEchart = (module: ModuleKey) => {
  const classificationModules: ModuleKey[] = [
    "customer",
    "saleChance",
    "contract",
    "production",
    "competitor",
  ];
  return classificationModules.includes(module);
}

export const hasSort = (module: ModuleKey) => {
  const sortModules: ModuleKey[] = [
    ...crmModuleKeys,
  ];
  return sortModules.includes(module);
}

export const hasBlog = (module: ModuleKey) => {
  const sortModules: ModuleKey[] = [
    "project",
  ];
  return sortModules.includes(module);
}

export const getParentId = (props: any) => {
  const { store, id, pid } = props;
  return pid || store?.getParentComs?.(id!)?.[0]?.id
}

export const checkModFunc = {
  hasSearch,
  hasEchart,
  hasSort,
  hasBlog
}

export const optionToMenu = (data: CommentOptionType[]) => {
  return data?.map((item: any) => {
    const { title, iconName } = item;
    return {
      ...item,
      content: title,
      icon: iconName,
    }
  })
}

/**
 * html文本转纯文本
 * @param html html格式文本内容
 */
export const html2text = (html: string) => {
  const tempDivElement = document.createElement("div");

  tempDivElement.innerHTML = html;

  return tempDivElement.textContent || tempDivElement.innerText || "";
}

export const getGroupsByComponentType = (type: string) => {
  const hiddenGroupByInput = ["display", "operation", "pagination", "otherSetting"]
  const hiddenGroupByList = ["input", "inputBox"]
  const filterConfig: string[] = type === 'input' ? hiddenGroupByInput :
    type === 'list' ? hiddenGroupByList : []

  return [
    {
      custom: false,
      id: "build-in-group",
      title: '',
      visible: true,
    },
    ...groups?.filter?.((i: any) => !(filterConfig.includes(i.id)))
  ]
}

export function isDescendantOrSelf(parentNode: any, childNode: any) {
  return (parentNode || document.body).contains(childNode);
}

export const getDisplayConfig = (config: CommentConfigType, page?: Page) => {
  const { client } = page || {};
  const templateData = client === 'PC' ? getInitTempateData() : getInitTempateDataM()
  const { displaySetting, showListAvatar, showSource: showListSource, showTime: showListTime } = config!;
  const { template: pcTemplate, mTemplate, displayMode, displayContent = [], displayContentAdvanced, sourceConfig, split, splitColor } = displaySetting || {}
  const template = client === 'PC' ? pcTemplate : mTemplate
  const showAvatar = displayContent.find((tag: any) => tag.id === 'avatar')?.checked
  const showSource = displayContent.find((tag: any) => tag.id === 'source')?.checked
  const showTime = displayContent.find((tag: any) => tag.id === 'time')?.checked
  const showSplitline = templateData.find(t => t.id === template)?.showSplitline
  const showCustomField = displayContent.find((tag: any) => tag.id === 'customField')?.checked
  const showDepartment = displayContent.find((tag: any) => tag.id === 'department')?.checked
  const showDepartmentBelow = templateData.find(t => t.id === template)?.showDepartmentBelow
  const showOptionConfig = displayContent.find((tag: any) => tag.id === 'optionConfig')?.checked

  if (displayMode === 'normal') {
    let props: any = {
      showListAvatar: showAvatar ?? showListAvatar, // 兼容历史数据
      showSource: showSource ?? showListSource, // 兼容历史数据
      showTime: showTime ?? showListTime, // 兼容历史数据
      templateId: template,
      splitline: showSplitline ? split : '',
      splitlineColor: splitColor,
      showCustomField: showCustomField ?? true,
      showDepartment,
      showDepartmentBelow,
      sourceConfig
    }
    if (showOptionConfig === false) {
      props.optionConfig = []
    }
    return props
  } else {
    let props = {
      splitline: showSplitline ? split : '',
      splitlineColor: splitColor,
      sourceConfig
    }
    return props
  }
}

export const handleFormatSourceFields = (commentFormDatasourceFieldList: any) => {
  const formFieldsObj = commentFormDatasourceFieldList?.[0] || {}
  return {
    ...formFieldsObj,
    name: getLabel('309759', '评论表'),
  }
}

export const isJSONString = (str: string) => {
  try {
    var obj = JSON.parse(str);
    if (obj && typeof obj === 'object') {
      return true;
    }
    return false;
  } catch {
    return false;
  }
}

/** 打开人力卡片 */
export const openCommentHrmCard = (id: string, type: string = '', isMobile?: boolean) => {
  if (isMobile) {
    return () => openMobileHrmCard(id, type, 'bs/hrm')
  }
  return (e: any) => openHrmCard(e, id, type, 'bs/hrm', { path: 'card' })
}

/** 获取url参数 */
export const getUrlParams = (pageId?: string) => ebdcoms.excu('getUrlParams')(pageId) as any;

/** 获取页面参数 */
export const getPageParams = (pageId?: string) => ebdcoms.get().getEbParams('pageParams', pageId);

/** 处理评论过滤条件，替换fieldName（后端接口需要） */
export const handleCommentFilterDatas = (filterCondition: any, page?: Page) => {
  const { filter, filterIsNull, dynamicFieldsValue = [] } = deepClone(filterCondition) || {}
  // 处理页面变量、url变量等
  const pageVar = getPageParams(page?.id).pageVar
  const urlParams = getUrlParams(page?.id) || {}
  return filter?.datas?.map((item: any) => {
    const { conditions, datas } = item
    if (datas) { // 条件嵌套
      return {
        ...item,
        datas: handleCommentFilterDatas(datas, page)
      }
    }
    const urlItemKey = Object.keys(urlParams).find((item: any) => `{${item}}` === conditions.value)
    if (conditions?.value === '{userid}') { // 当前人员
      const userInfo = window.TEAMS?.currentUser || {};
      const { username = "", id = "" } = userInfo;
      conditions.value = [{ id, name: username }]
    } else if (/\{pageVar_[^}]+\}/.test(conditions?.value)) { // 处理页面变量，替换真实值
      const item = pageVar?.find((item: any) => `{${item.varKey}}` === conditions.value)
      if (item) {
        if (!filterIsNull || item.defVal) {
          conditions.value = item.defVal
        }
      }
    } else if (urlItemKey) { // 处理url变量
      if (!filterIsNull || urlParams[urlItemKey]) {
        conditions.value = urlParams[urlItemKey]
      }
    } else if (conditions?.value?.dynamicField) {
      // 当前列表字段id
      const fieldId = conditions.value.dynamicField.field?.id || ''
      const fieldValue = dynamicFieldsValue.find((f: any) => f.id === fieldId)?.value
      if (fieldValue !== undefined) {
        conditions.value = fieldValue
      }
    }
    return item
  })
}

export const handleDataOptions = (dataOptions: any) => {
  return dataOptions?.map((option: any) => {
    const { content, optionId } = option
    return {
      id: optionId,
      name: content,
      ...option
    }
  })
}

/** 视图处理cardLayout，规范compType，参考nlist-getCompleteCardLayout方法 */
export const handleCompeleteCardLayout = (cardLayout: any, listField: any) => {
  let grid = cardLayout?.grid;
  if (grid) {
    grid = (cardLayout?.grid || []).map((g: any[]) => g.map((row: any[]) => row
      .map((item: any) => {
        const configField = item.field;
        let compType = configField.compType;
        if (!compType) {
          compType = listField.find((f: any) => f.id === configField.id)?.compType
        }
        return {
          ...item,
          field: {
            ...configField,
            compType,
          },
        };
      })
      .filter(Boolean)));
  }
  return {
    ...cardLayout,
    grid,
  };
}

/** template预览中，始终用text类型展示 */
export const handleGridToText = (grid: any) => {
  return grid.map((col: any) => {
    return col.map((row: any) => {
      return row.map((item: any) => {
        return {
          ...item,
          field: {
            ...item.field,
            compType: 'Text',
            type: 'String'
          }
        }
      })
    })
  })
}