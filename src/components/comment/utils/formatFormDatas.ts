import { utils } from "@weapp/ui";
import topBtns, { CustomBtnType } from "../config/title-btns/topBtns";
import { CommentConfigType } from "../types";
import { EventTo } from "../../../ebConfig/operation-btns/types";
import { Sort } from "../config/title-btns/sort/types";

const { deepClone } = utils

/**
 * 处理 Config 数据
 */
export const formatFormDatas = (config: CommentConfigType) => {
    const topOperatBtns = formatTopOperatBtns(config)
    return {
        ...config,
        topOperatBtns
    }
}

/**
 * 处理右上角按钮
 * @param config 
 * @returns 
 */
export const formatTopOperatBtns = (config: CommentConfigType) => {
    const { relatedDataSource, topOperatBtns } = config
    const showContent = topBtns.btns.find(btn => btn.customType === CustomBtnType.SORT)?.showContent
    const { emptyContent } = showContent as any
    let newTopOperatBtns = deepClone(topOperatBtns)
    const sortBtn = newTopOperatBtns.btns.find((btn: any) => btn.event.to === EventTo.SORT)

    // 数据源模式，需要处理历史数据，sort排序增加【默认】按钮
    if (relatedDataSource && sortBtn.showContent) {
        const ascContentStyle = sortBtn.showContent.ascContent?.style
        if (ascContentStyle) {
            sortBtn.showContent.ascContent.style = {
                ...ascContentStyle,
                color: 'var(--primary)'
            }
        }
        const descContentStyle = sortBtn.showContent.descContent?.style
        if (descContentStyle) {
            sortBtn.showContent.descContent.style = {
                ...descContentStyle,
                color: 'var(--primary)'
            }
        }

        if (!sortBtn.showContent.emptyContent) {
            sortBtn.showContent.emptyContent = emptyContent
            sortBtn.defalutContent = Sort.EMPTY
        }
        if (!sortBtn.defalutContent) { // 切换成数据源，没有默认值，则选中默认
            sortBtn.defalutContent = Sort.EMPTY
        }
    } else if (!relatedDataSource && sortBtn.defalutContent) {
        if (sortBtn.defalutContent === Sort.EMPTY) {
            sortBtn.defalutContent = Sort.DESC
        }
    }
    return newTopOperatBtns
}