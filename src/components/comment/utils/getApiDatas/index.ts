import { IComData } from '@weapp/designer';
import { designApi } from "../../api";
import { corsImport, memoize } from '@weapp/utils';
import { CommentConfigType } from '../../types';
import { AnyObj } from '@weapp/ui';
import { getCommentFormFields } from '../../api/data';

export const setCommentIdInfo = async (com: IComData, refCom?: IComData) => {
  // 获取评论id
  const result = await designApi.getCommentId();
  const { status, data = [] } = result;
  // 接口请求失败时返回false
  if (!status) return false;
  const { config } = com;
  // 获取id值直接添加进config'中
  config.targetId = data[0];
  if (refCom?.config) {
    refCom.config.targetId = data[0];
  }

  return true;
}

export const handleCommentIdInfo = async (config: AnyObj, onConfigChange: (value: CommentConfigType, act?: any) => void) => {
  const { targetId } = config || {}
  if (!targetId) {
    // 获取评论id
    const result = await designApi.getCommentId();
    const { status, data = [] } = result;

    if (status) {
      const commentId = data[0];
      onConfigChange({ targetId: commentId })
    }
  }
}

/**
 * 获取数据源字段信息，前端memo处理 减少请求
 */
export const getFields = (
  dataset: any,
  effectType?: string,
  needPic?: boolean,
  needDetail?: boolean,
  customParam?: any,
  getFieldsOtherParams?: any,
) => corsImport('@weapp/components').then(
  ({ dsUtils }) => new Promise((resolve) => {
    dsUtils
      .getFields(dataset, effectType, needPic, needDetail, customParam, getFieldsOtherParams)
      .then((datas: any) => {
        resolve(datas);
      })
      .catch(() => resolve([]));
  }) as Promise<any>,
);

export const getCommentFormFieldsWithoutMemo = async (itemFormId: string, dataExtMemoTime?: string) => {
  const result = await getCommentFormFields({ itemFormId })
  return result
}

export const getCommentFormFieldsMemo = memoize(
  getCommentFormFieldsWithoutMemo,
  (itemFormId, dataExtMemoTime) => `${itemFormId}_${dataExtMemoTime}`
)