import { AnyObj } from "@weapp/ui";
import { RouteComponentProps, useHistory, useLocation, useRouteMatch } from "react-router-dom";

function useRouter<P extends AnyObj = {}>(props?: P): P & RouteComponentProps {

  const history = useHistory();
  const location = useLocation();
  const match = useRouteMatch();

  return { ...props, history, location, match } as P & RouteComponentProps;
};

export default useRouter