import { corsImport } from "@weapp/utils";
import { useState, useCallback, useEffect } from "react";
import { FILTER_CHANGE, GET_TOTAL } from "../constant/eventNames";
import { CommentFilterType, ComBtnDataType } from "../types";

function useTitle(props: any) {
  const { store, id, pid, config, events } = props;
  const { titleEnabled } = config;
  
  const [total, setTotal] = useState(0);

  const parentId = pid || store?.getParentComs?.(id!)?.[0]?.id;

  const handChange = useCallback((key: CommentFilterType, data: ComBtnDataType) => {
    events?.emit(FILTER_CHANGE, id, key, data);
  }, [events, id])

  useEffect(() => {
    // 评论数据改变回调
    events?.on(GET_TOTAL, id as any, setTotal)
    return () => {
      events?.off(GET_TOTAL, id as any)
    }
  }, [id])

  useEffect(() => {
    corsImport("@weapp/ebdnavcoms").then(modules => {
      // 触发选项卡顶部渲染事件改变方法
      const { moveIn, moveOut } = modules?.constants?.EVENT_NAME?.NEWTAB_EVENT_NAME || {};
      if (parentId) {
        //@ts-ignore
        events?.on(moveIn, parentId, handleDragIn);
        //@ts-ignore
        events?.on(moveOut, parentId, handleDragOut);
      }
    })
    return () => {
      corsImport("@weapp/ebdnavcoms").then(modules => {
        // 触发选项卡顶部渲染事件改变方法
        const { moveIn, moveOut } = modules?.constants?.EVENT_NAME?.NEWTAB_EVENT_NAME || {};
        if (parentId) {
          //@ts-ignore
          events?.off(moveIn, parentId);
          //@ts-ignore
          events?.off(moveOut, parentId);
        }
      })
    }
  }, [events, parentId])

  useEffect(() => {
    corsImport("@weapp/ebdnavcoms").then(modules => {
      // 触发选项卡顶部渲染事件改变方法
      const eventName = modules?.constants?.EVENT_NAME?.NEWTAB_EVENT_NAME?.renderExtraContent;
      if (parentId && eventName) {
        const headerContent = ["config", {
          app: "@weapp/ui-props-design",
          compName: "CommentHeader",
          config,
          onDataChange: handChange
        }]
        // title生效时选项卡顶部渲染空
        const tabConfig = titleEnabled ? ["dom", null] : headerContent;
        events?.emit(eventName, parentId, ...tabConfig);
      }
    })
    
  }, [config, id, parentId]);
}