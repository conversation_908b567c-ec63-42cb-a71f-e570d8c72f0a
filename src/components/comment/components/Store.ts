import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../types";
import { AnyObj, ReplyOption, getRichTextFromText } from "@weapp/ui";
import { action, computed, observable, runInAction } from "mobx";
import { dataApi } from "../api";
import BaseStore from "../../../store";
import { getLabel } from "@weapp/utils";
import { formatDate } from "../utils";

type CommentFilterType = "employee" | "contact";

class CommentStore extends BaseStore<CommentStore> {

  @observable filterData = [];
  @observable commentType: CommentFilterType = "employee";
  @observable startDate = "";
  @observable endDate = "";

  /** 是否需要添加日报 */
  @observable blogStatus = false;

  @computed
  get createParams() {
    const dayWeibo = this.blogStatus ? new Date() : undefined;
    return {
      dayWeibo,
    }
  }

  @computed
  get updateParams() {
    const dayWeibo = this.blogStatus ? new Date() : undefined;
    return {
      dayWeibo,
    }
  }

  @action
  setCommentType = (value: CommentFilterType) => {
    this.commentType = value;
  }

  @action
  setDate = (value: string[] = []) => {
    this.startDate = value[0];
    this.endDate = value[1];
  }

  @action
  handleChangeBlog = (value: boolean) => {
    this.blogStatus = value;
  }

  /**
   * 获取统计数据
   * @param id 数据id
   * @param urlModule 接口url拼接module
   * @param module 接口参数添加module
   */
  @action
  getStatistic = async (id: string, urlModule: ModuleKey, module: string) => {
    const commentFilter: AnyObj = { commentType: this.commentType }

    // 过滤条件
    if (this.startDate) {
      commentFilter.commentCurrentDate = this.startDate;
    }
    if (this.endDate) {
      commentFilter.commentSelectDate = this.endDate;
    }
    const commentFilterStr = JSON.stringify(commentFilter);
    let params = {
      id: id,
      module: module,
      commentFilter: commentFilterStr
    };

    const result = await dataApi.findCommentCount(urlModule, params);
    const { data = [] } = result;

    if (data) {
      runInAction(() => {
        this.filterData = data.map((el: any = {}) => {
          let newEl = {
            id: el.id,
            value: el.commentTotal,
            name: el.commentor?.username
          }
          return newEl;
        });
      })
    }
  }

  /**
   * 添加日报
   * @param commentData 评论数据
   */
  @action
  addBlog = async (commentData?: ReplyOption) => {
    //添加失败则不执行后续操作
    if (!commentData) {
      return ;
    }

    const { content, originContent, attachments, atLinkList, userData, targetName = "", relevanceList } = commentData;
    // 转日报内容
    let commentValue = originContent || content;
    commentValue = `${content} <br/><br/>${getLabel('136127', '来源项目：')}${targetName}<br/><br/>`;
    commentValue = getRichTextFromText(commentValue, userData).replaceAll("_weaverMte_", "")
    // 附件id
    const attachmentIds = attachments?.map((item: any) => item.id)?.join(",") || "";
    // at信息
    const atDatas = atLinkList?.map((item: any) => ({ ...item, id: null }));
    const blogParams = {
      attachmentIds,
      isSyn: false, //@人 日报不发消息
      blog: {
        content: getRichTextFromText(commentValue, userData).replaceAll("_weaverMte_", ""),
        blogTime: formatDate(),
        atLinkList: atDatas,
        relevanceList: relevanceList
      }
    }
    const result = await dataApi.addBlog(blogParams);
    return result;
  }
  
}

export default CommentStore;