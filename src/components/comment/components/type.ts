import { CommentProps, MCommentProps } from "@weapp/ui";
import { RouteComponentProps } from "react-router-dom";
import { DesignProps } from "@weapp/ebdcoms";
import { CommentViewProps, CommentDesignProps, ComBtnDataType, CommentConfigType, CommentFilterType } from "../types";

export interface BaseEbCommentProps {
  /** 是否是design展示 */
  isDesign?: boolean;
  ebProps?: CommentViewProps | CommentDesignProps;
  /** 是否使用自动发布日报功能 */
  useBlog?: boolean;
  /** 组件显示类型 */
  componentShowType?: string
  /** 评论区域点击事件 */
  onCommentAreaClick?: (areaType: string, e: any) => void;
  /** 是否显示部门 */
  showDepartment?: boolean;
  /** 部门显示在下方 */
  showDepartmentBelow?: boolean;
}

export interface EbCommentProps extends CommentProps, BaseEbCommentProps {
  refresh?: string
  showCustomField?: boolean;
}

export interface MEbCommentProps extends MCommentProps, RouteComponentProps, BaseEbCommentProps {
  showEditAvatar?: boolean;
  hasEdit?: boolean;
  refresh?: string
  showText?: boolean;
  showCustomField?: boolean;
}

export interface MEbCommentEditProps extends MEbCommentProps {
  handleOpenEdit: () => void
  avatarUrl: string
  username: string
}

export interface CommentHeaderProps extends DesignProps<CommentConfigType> {
  weId: string;
  /** icon按钮点击回调事件 */
  onDataChange?: (id: CommentFilterType, data: ComBtnDataType) => void;
}

export type CommentHeaderType = React.ComponentType<CommentHeaderProps>;

export interface CommentTitleProps extends DesignProps<CommentConfigType> {
  weId: string;
}

export type CommentTitleType = React.ComponentType<CommentTitleProps>;