import { PureComponent } from 'react';
import { classnames, isEqual } from '@weapp/utils';
import { AnyObj, CorsComponent } from '@weapp/ui';
import { commentClsPrefix } from '../../../../constants';
import { CommentConfigType } from '../../types';
import { commentCommonFields, moduleData } from '../../constant';
import { toJS } from 'mobx';

const prefixCls = commentClsPrefix;

export interface CustomFieldsComConfig {
    value?: AnyObj;
    readOnly?: boolean;
    data?: AnyObj;
    type?: 'reply' | 'edit' | 'show' | 'quote';
}
interface CustomFieldsProps {
    config?: CommentConfigType;
    isMobile?: boolean;
    comConfig?: CustomFieldsComConfig;
    commentForm?: AnyObj;
    unMount?: (commentData?: AnyObj) => void;
    onMount?: (commentData?: AnyObj, getCustomFieldsDatas?: any) => void;
}

interface CustomFieldsState {
    commentForm?: AnyObj;
}

export class CustomFields extends PureComponent<CustomFieldsProps, CustomFieldsState> {

    constructor(props: CustomFieldsProps) {
        super(props);
        this.state = {
            commentForm: undefined
        }
    }

    static getDerivedStateFromProps(nextProps: CustomFieldsProps, prevState: CustomFieldsState) {
        const { commentForm } = nextProps
        let newState: any = {}
        if (!isEqual(commentForm, prevState.commentForm)) {
            newState = {
                ...newState,
                commentForm
            }
        }
        return newState
    }

    componentDidUpdate(prevProps: Readonly<CustomFieldsProps>, prevState: Readonly<CustomFieldsState>, snapshot?: any): void {
        const { comConfig, commentForm } = this.props
        const { data, type } = comConfig || {}
        if (type === 'show' && !isEqual(data?.formData, prevProps.comConfig?.data?.formData)) { // 展示时
            const { commentFormFieldList = [] } = commentForm || {}
            const fields = commentFormFieldList.filter((field: any) => !commentCommonFields.includes(field.dataKey))
            const currentFormDatas: any = {}
            fields?.forEach((field: any) => {
                const { dataDetails = [] } = data?.formData || {}
                const detail = dataDetails.find((item: any) => item.formField.id === field.id)
                if (detail) {
                    currentFormDatas[field.id] = detail
                }
            })
            this.formStore.setFormData?.(data?.formData)
        }
    }

    componentDidMount() {
        const { comConfig, onMount } = this.props
        const { data } = comConfig || {}
        onMount?.(data, this.getCustomFieldsDatas)
    }

    componentWillUnmount() {
        const { comConfig, unMount } = this.props
        const { data } = comConfig || {}
        unMount?.(data)
    }

    getCustomFieldsDatas = () => {
        return this.formStore?.getFormData?.()
    }

    formStore: any = null

    onFormRenderComplete = (formStore: any) => {
        this.formStore = formStore
    }

    resetFormData = () => {
        this.formStore?.setFormData?.()
    }

    onFormChange = (value: any, fieldId: string) => {

    }

    getCustomConfig = () => {
        const { commentForm } = this.state
        const { comConfig } = this.props
        const { readOnly } = comConfig || {}
        const { commentFormFieldList = [] } = commentForm || {}
        const customConfig: any = {}
        commentFormFieldList.forEach((field: any) => {
            if (commentCommonFields.includes(field.dataKey)) {
                customConfig[field.id] = {
                    isShow: false,
                }
            } else {
                customConfig[field.id] = {
                    isReadOnly: readOnly,
                }
            }
        })
        return customConfig
    }

    render() {
        const { commentForm } = this.state
        const { isMobile, comConfig } = this.props
        const { type, data } = comConfig || {}
        const cls = classnames({
            [`${prefixCls}-custom-form`]: !isMobile,
            [`${prefixCls}-m-custom-form`]: isMobile,
            [`${prefixCls}-m-custom-form-edit`]: isMobile && (type !== 'show')
        })

        return <div className={cls}>
            <CorsComponent weId={`${this.props.weId || ''}_bbelks`}
                app="@weapp/formbuilder"
                compName="Form"
                formId={commentForm?.commentFormId}
                dataId={data?.id}
                module={moduleData.ebuilderformModule}
                apiModule={"ebuilder"}
                isMobile={isMobile}
                layoutMultiId={''}
                customConfig={this.getCustomConfig()}
                // 获取 formStore
                onFormRenderComplete={this.onFormRenderComplete}
                onChange={this.onFormChange}
                // 不传formData，会自动通过formId和dataId请求接口获取数据
                //formData={type && ['edit', 'show'].includes(type) ? comConfig?.data?.formData : undefined}
                formItemClassName={`${prefixCls}-custom-formitem`}
            />
        </div>
    }
}