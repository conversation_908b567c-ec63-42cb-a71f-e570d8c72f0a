import { observer } from 'mobx-react';
import { PureComponent } from 'react';
import { getLabel } from '@weapp/utils';
import { AnyObj, Button, Dialog, DialogProps, Form, FormStore } from '@weapp/ui';
import { commentClsPrefix } from '../../../../constants';
import { CommentConfigType } from '../../types';

const prefixCls = commentClsPrefix;

interface TargetDataProps {
    config?: CommentConfigType;
    dialogProps?: DialogProps;
    promiseRef: AnyObj;
    commentForm?: AnyObj;
}

interface TargetDataState {

}

export class TargetData extends PureComponent<TargetDataProps, TargetDataState> {

    constructor(props: TargetDataProps) {
        super(props);
        this.state = {

        }
    }

    store = new FormStore()

    onTargetSure = () => {
        this.store.validate().then(res => {
            if (JSON.stringify(res.errors) === '{}') {
                const { promiseRef, dialogProps, commentForm } = this.props || {}
                const { resolve, rejcet, data } = promiseRef || {}
                const formdata = this.store.getFormDatas()
                const { targetData } = formdata
                const targetId = targetData?.[0]?.id

                const field = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_target_id')
                const { formId, id: fieldId } = field || {}
                const result = {
                    formId,
                    formField: {
                        id: fieldId,
                    },
                    content: targetId,
                    dataText: { content: targetId },
                }
                if (data?.comment?.formData?.dataDetails) {
                    data.comment.formData.dataDetails.push(result)
                }
                resolve(data)
                dialogProps?.onClose?.()
            }
        })
    }

    render() {
        const { config, dialogProps, promiseRef } = this.props || {}

        return <Dialog
            weId={`${this.props.weId || ''}_e8w1rx`}
            title={getLabel('312090', '请填写关联数据')}
            closable
            destroyOnClose
            placement={'middle'}
            scale
            width={600}
            footer={
                [<Button weId={`${this.props.weId || ''}_nbtpya@${0}`} type={"primary"} onClick={this.onTargetSure}>{getLabel('261599', '确定')}</Button>, <Button weId={`${this.props.weId || ''}_ftfhpu@${1}`} onClick={dialogProps?.onClose}>{getLabel('223917', '取消')}</Button>]
            }
            {...dialogProps}
        >
            <Content weId={`${this.props.weId || ''}_du2v78`}
                config={config}
                promiseRef={promiseRef}
                formStore={this.store}
            />
        </Dialog>
    }
}

interface ContentProps extends TargetDataProps {
    formStore: FormStore;
}

class Content extends PureComponent<ContentProps, any> {

    constructor(props: ContentProps) {
        super(props);
        this.state = {

        }
    }

    componentDidMount() {
        const { config, formStore } = this.props || {}
        const { relatedDataSource } = config || {}
        const objId = relatedDataSource.id
        formStore.initForm({
            data: {},
            items: {
                targetData: {
                    itemType: 'BROWSER',
                    browserBean: {
                        type: "ebuilder",
                        module: "ebuilder/form",
                        commonParams: {
                            // eb特殊格式，开启权限校验
                            ebBrowserForEditParam: {
                                objId
                            }
                        },
                        browserDialogProps: {
                            title: getLabel('312091', '关联数据')
                        },
                        // 隐藏新建按钮
                        extraButtons: []
                    },
                    required: true
                }
            },
            layout: [[{ id: 'targetData', label: getLabel('312091', '关联数据'), items: ['targetData'], labelSpan: 6, groupId: '', hide: false }]],
            groups: []
        })
    }

    componentWillUnmount() {
        this.props.formStore?.clear()
    }

    render() {
        const { formStore } = this.props || {}

        return <Form weId={`${this.props.weId || ''}_e8w1rx`}
            store={formStore}
        />
    }
}