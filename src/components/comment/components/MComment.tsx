import { <PERSON><PERSON>bj, FormDatas, Icon, MAvatar, MBrowser, MComment, MDialog, ReplyOption, SearchAdvancedPanelProps, SearchAdvancedStore, utils } from "@weapp/ui";
import { classnames, getLabel, isEqual, request, RequestOptionConfig, RequestResult } from "@weapp/utils";
import { createRef, PureComponent, ReactNode } from "react";
import { withRouter } from "react-router-dom";
import { commentClsPrefix } from "../../../constants";
import commentData from "../mock/commentData.json";
import commentReplyData from "../mock/commentReplyData.json";
import { CommentProvider } from "../context/ComContext";
import CommentStore from "./Store";
import { GET_TOTAL, OPERATION_BTN, SEARCH_COMMENT } from "../constant/eventNames";
import MBlog from "./blog/index.m";
import { formatDate, handleCommentFilterDatas, hasBlog, isJSONString, openCommentHrmCard } from "../utils";
import { observer } from "mobx-react";
import { MEbCommentProps } from './type';
import { DiyBtn } from "../../../ebConfig/operation-btns/types";
import { cascadeRules, customSearchConfig, searchInitDatas } from "../utils/search";
import MEbCommentEdit from './MEbCommentEdit'
import { commentCommentedUrl, commentUrl, createUrl, deleteUrl, queryChildrenUrl, statisticUrl, updataUrl } from "../api/data";
import { commentCommonFields, CommentEvents, moduleData } from "../constant";
import { CustomFields, CustomFieldsComConfig } from "./form/customFields";
import { getCommentFormFieldsMemo } from "../utils/getApiDatas";

const { MBrowserPanel } = MBrowser;
const { MCommentEdit } = MComment;
const { formatParentPath, getRandom } = utils;
const { toast } = MDialog

interface MEbCommentState {
  outerFilter: any;
  commentForm?: AnyObj;
  customSearchConditions: AnyObj[];
  /** 数据源模式，三种按钮切换 */
  sortId: 'asc' | 'desc' | 'empty' | '';
  /** 用于组件刷新 */
  commentKey: string;
}

const prefixCls = commentClsPrefix;

@observer
class MEbComment extends PureComponent<MEbCommentProps, MEbCommentState> {

  commentRef = createRef<any>();

  targetPromiseRef: AnyObj = {
    data: {},
    resolve: null,
    reject: null
  }

  store = new CommentStore();

  /** 组件内备份评论总数，便于新增时计算总数 */
  commentTotal = 0;

  contextValue = {
    store: this.store,
  }

  private intervalId: NodeJS.Timeout | undefined | number;

  mEbCommentEditRef = createRef<any>();
  constructor(props: MEbCommentProps) {
    super(props);
    this.state = {
      outerFilter: {},
      commentForm: undefined,
      customSearchConditions: [],
      sortId: '',
      commentKey: `${Math.random() * 1000}`
    }
    this.intervalId = undefined;
  }

  componentDidMount() {
    const { id, events } = this.props.ebProps!;
    events?.on(OPERATION_BTN, id as any, this.handleFilterChange);

    events?.on(CommentEvents.COMMENT_UPDATE_COMMENTFORM, id as any, this.updateCommentForm);
  }

  componentDidUpdate(prevProps: MEbCommentProps) {
    const { relatedDataSource: prevRelatedDataSource, filterCondition: prevFilterCondition, sortFields: prevSortFields } = prevProps.ebProps?.config || {}
    const { relatedDataSource, filterCondition, sortFields } = this.props.ebProps?.config || {}
    if (prevProps.refresh !== this.props.refresh) {
      this.stopRefreshing();
      this.startRefreshing();
    }
    // 数据源、过滤条件、排序条件变化时，刷新评论列表
    if (!isEqual(prevRelatedDataSource?.id, relatedDataSource?.id) || !isEqual(prevFilterCondition, filterCondition) || !isEqual(prevSortFields, sortFields)) {
      this.commentRef.current?.refresh?.()
    }
  }

  componentWillUnmount() {
    const { id, events } = this.props.ebProps!;
    events?.off(OPERATION_BTN, id as any);

    this.stopRefreshing();

    events?.off(CommentEvents.COMMENT_UPDATE_COMMENTFORM, id as any);
  }

  updateCommentForm = (commentForm: AnyObj) => {
    this.setState({ commentForm })
  }

  getCommentFormFields = async () => {
    const { config } = this.props.ebProps || {}
    const { itemFormId, relatedDataSource } = config || {}
    if (!itemFormId) return;
    const res = await getCommentFormFieldsMemo(itemFormId, relatedDataSource?.dataExtMemoTime)
    this.setState({ commentForm: res.data })
  }

  startRefreshing() {
    const { refresh } = this.props;
    if (refresh && refresh !== 'noRefresh') {
      let timeValue: number = 0

      if (refresh.indexOf('custom_') > -1) {
        timeValue = refresh.split('_')[1] === 'default' ? 5 * 60 : Number(refresh.split('_')[1]) * 60
      } else {
        const [time, unit] = refresh.split('_')
        timeValue = unit === 'second' ? Number(time) : Number(time) * 60
      }
      this.intervalId = setInterval(this.refresh, timeValue * 1000)
    };
  }

  stopRefreshing() {
    clearInterval(this.intervalId);
  }

  handleOpenEdit = () => {
    const { history, targetId } = this.props;

    const path = formatParentPath(this.props);
    history.push(`${path}/${targetId}/addComment`);
  }

  setCommentTotal = (total: number) => {
    const { events, id } = this.props.ebProps!;
    this.commentTotal = total;
    events?.emit(GET_TOTAL, id, total);
  }

  handleFilterChange = (e: any, btn: DiyBtn) => {
    const { action, options, event } = btn as any;
    switch (action || event.to) {
      case "statistics": {
        this.commentRef.current?.onStatistic?.();
        break;
      }
      case "search": {
        this.commentRef.current?.handleOpenSearch?.();
        break;
      }
      case "sort": {
        const sortId = options.find((opt: any) => opt.selected)?.id
        this.setState({ sortId }, () => {
          this.commentRef.current?.onSortChange?.();
        })
        break;
      }
      case "REFRESHCOM": {
        this.refreshCom();
        break;
      }
    }
  }

  handleAfterGetComment = async (result: RequestResult<any>) => {
    const { isDesign, ebProps } = this.props;
    const { replyUnderComment, relatedDataSource } = ebProps?.config!;
    if (isDesign) {
      if (!relatedDataSource || !result.data.page.result.length) {
        result = replyUnderComment ? commentReplyData : commentData;
      }
    }
    const { totalCount = 0 } = result?.data?.page || {};
    this.setCommentTotal(totalCount);
    if (result.data?.page?.result) {
      const random = getRandom() // 配合高级显示更新视图
      const comments = result.data.page.result
      result.data.page.result = comments.map((item: any) => {
        return {
          ...item,
          random
        }
      })
    }
    return result;
  }

  handleAfterDelete = async (result: RequestResult, commentData?: ReplyOption) => {
    const { ebProps } = this.props;
    const { replyUnderComment } = ebProps?.config!;
    const { status } = result;
    const { parent } = commentData || {};
    // 评论成功则数量加一（回复按楼层显示除外）
    if (status && !(parent && replyUnderComment)) {
      this.setCommentTotal(this.commentTotal - 1);
    }
    return result;
  }

  handleCreateCancel = () => {
    const { setMobxState } = this.store;
    setMobxState({ blogStatus: false });
    this.handleCancel();
  }

  handleCancel = () => {
    const { history } = this.props;
    const path = formatParentPath(this.props);
    history.push(path);
  }

  refresh = () => {
    this.commentRef.current?.onRefresh?.();
  }

  /** 刷新Comment组件 */
  refreshCom = () => {
    this.setState({ commentKey: `${Math.random() * 1000}` })
  }

  handleCreated = (data: ReplyOption) => {
    this.refresh();
    this.handleCreateCancel();
  }

  handleSearchDataChange = () => {
    const searchAdvancedStore = this.commentRef.current?.searchAdvancedStore;

    if (!searchAdvancedStore) {
      return;
    }
    const { formStore } = searchAdvancedStore as SearchAdvancedStore;
    const { getFormDatas } = formStore;
    const formDatas = getFormDatas();

    let isSearchEmpty = true;
    Object.keys(formDatas).forEach((key) => {
      // 是否是内容
      const isValue = key.match(/^value_/);
      const value = formDatas[key];
      let isValueEmpty = true;
      if (isValue) {
        isValueEmpty = utils.isEmpty(value);
      }
      const isOperateContent = key.match(/^operate_content/);
      if (isOperateContent) {
        const nullList = ["isnotnull", "isnull"];
        isValueEmpty = !nullList.includes(value)
      }
      isSearchEmpty = isSearchEmpty && isValueEmpty;
    })

    const { id, events } = this.props.ebProps!;
    events?.emit(SEARCH_COMMENT, id, isSearchEmpty);
  }

  handleSearchChange = (value?: FormDatas, otherParams?: any) => {
    const searchAdvancedStore = this.commentRef.current?.searchAdvancedStore;
    if (!searchAdvancedStore) {
      return;
    }
    const { layoutConfig: { id: layoutId } } = otherParams;

    const { formStore } = searchAdvancedStore as SearchAdvancedStore;
    if (value) {
      Object.keys(value).forEach(key => {
        const dataValue = value[key] as string;
        const rule = cascadeRules[key]?.[dataValue];
        if (rule) {
          const { setLayoutProps } = formStore;
          setLayoutProps(layoutId, {
            items: rule,
          });
        }
      })
    }
    this.handleSearchDataChange();
  }

  handleAfterDeleteCondition = () => {
    this.handleSearchDataChange();
  }

  handleSearchReset = () => {
    this.handleSearchDataChange();
  }

  unChooseSnap = () => {
    this.handleSearchDataChange();
  }

  onChooseSnap = async (id: string) => {
    const searchAdvancedStore: SearchAdvancedStore = this.commentRef.current?.searchAdvancedStore;
    if (!searchAdvancedStore) {
      return;
    }
    const { commonFilterStore: { checkSnaps } } = searchAdvancedStore;
    await checkSnaps(id);
    this.handleSearchDataChange();
  }

  searchAdvancedProps: Partial<SearchAdvancedPanelProps> = {
    onChange: this.handleSearchChange,
    onReset: this.handleSearchReset,
    unChooseSnap: this.unChooseSnap,
    onChooseSnap: this.onChooseSnap,
    formProps: {
      // @ts-ignore
      afterDeleteCondition: this.handleAfterDeleteCondition
    }
  }

  customRenderHeader = (target: any, com: ReactNode) => {
    const userInfo = window.TEAMS?.currentUser || {};
    const { username = "", avatar = {} } = userInfo;
    const avatarUrl = avatar && (avatar.downloadUrl || avatar.previewUrl);

    return (
      <>
        {com}
        <MEbCommentEdit weId={`${this.props.weId || ''}_vxw3rn`}
          {...this.props}
          // @ts-ignore
          ref={this.mEbCommentEditRef}
          handleOpenEdit={this.handleOpenEdit}
          avatarUrl={avatarUrl}
          username={username}
        />
      </>
    )
  }

  renderExtraConent = () => {

    const { useBlog, ebProps } = this.props;
    const { moduleValue } = ebProps?.config!;

    const blog = hasBlog(moduleValue!) && useBlog && (
      <MBlog
        weId={`${this.props.weId || ''}_5fdae2`}
      />
    )

    return (
      <div
        className={`${prefixCls}-m-extra-content`}
      >
        {blog}
      </div>
    )
  }

  handleAfterCreate = async (result: RequestResult) => {
    const { addBlog } = this.store;
    const { status, data } = result;
    // if (status) {
    //   await addBlog(data?.data);
    // }
    return result;
  }

  handleAfterReply = async (result: RequestResult) => {
    const { ebProps } = this.props;
    const { replyUnderComment } = ebProps?.config!;
    const { status } = result;
    const { parent } = result.data?.data || {};
    // 评论成功则数量加一（回复按楼层显示除外）
    if (status && !(parent && replyUnderComment)) {
      this.setCommentTotal(this.commentTotal + 1);
    }
    return result;
  }

  renderItemHeaderInfo = (data: ReplyOption) => {
    const { dayWeibo, id } = data;

    const leftContent: ReactNode[] = [];
    const rightContent: ReactNode[] = [];
    if (dayWeibo) {
      const blogData = formatDate(dayWeibo);
      const blogNode = (
        <div
          className={`${prefixCls}-m-blog-info`}
          key="blog"
        >
          <Icon
            weId={`${this.props.weId || ''}_my9ejp`}
            name="Icon-Cloud-synchronization"
            size="lg"
          />
          <span>
            {getLabel("223896", "已同步发布{0}日报", [blogData])}
          </span>
        </div>
      )
      rightContent.push(blogNode);
    }

    return (
      <div
        className={`${prefixCls}-m-header-info`}
        key={id}
      >
        <div className={`${prefixCls}-m-header-left`}>
          {leftContent}
        </div>
        <div className={`${prefixCls}-m-header-right`}>
          {rightContent}
        </div>
      </div>
    )
  }

  get isAdvanced() {
    const { config } = this.props.ebProps || {}
    return config?.displaySetting?.displayMode === 'advanced'
  }

  /** 获取评论显示相关配置（显示部门） */
  getDisplayProps = () => {
    if (!this.isAdvanced) {
      return {
        renderItemHeaderMore: this.renderItemHeaderMore,
        renderItemHeaderBelow: this.renderItemHeaderBelow
      }
    }
    return {}
  }

  renderItemHeaderMore = (data?: AnyObj) => {
    const { showDepartment, showDepartmentBelow } = this.props
    const { department } = data?.otherParam?.hrmInfo || {}
    if (showDepartment && !showDepartmentBelow) {
      if (department?.id) {
        return <span className={`${prefixCls}-m-hrm-span`} onClick={openCommentHrmCard(department.id, 'department', true)}>{department.name}</span>
      }
    }
    return <></>
  }

  renderItemHeaderBelow = (data?: AnyObj) => {
    const { showDepartment, showDepartmentBelow } = this.props
    const { department } = data?.otherParam?.hrmInfo || {}
    if (showDepartment && showDepartmentBelow) {
      if (department?.id) {
        return <span className={`${prefixCls}-m-hrm-span-below`} onClick={openCommentHrmCard(department.id, 'department', true)}>{department.name}</span>
      }
    }
    return <></>
  }

  getDataKeyValueObj = (params: AnyObj = {}, replyData?: ReplyOption, isUpdate?: boolean) => {
    const { attachments, attachmentsList, client, comment } = params
    const { content, originContent, imAtContent, longitude, latitude, module, privy, address, parent, rootId, targetId } = comment || {}
    const result: AnyObj = isUpdate ? { // 评论更新
      'comment_origin_content': originContent, // 评论内容
      'comment_plain_content': imAtContent, // 纯文本评论
      'comment_parent': parent?.id, // 上级评论
      'comment_privy': privy ? '1' : '0', // 是否私评
    } : {
      'comment_origin_content': originContent, // 评论内容
      'comment_plain_content': imAtContent, // 纯文本评论
      'comment_parent': parent?.id, // 上级评论
      'comment_root_id': rootId, // 根评论
      'comment_privy': privy ? '1' : '0', // 是否私评
      'comment_module': moduleData.ebuilderformModule, // 所属模块
      'comment_longitude': longitude, // 经度
      'comment_latitude': latitude, // 纬度
      'comment_address': address, // 评论位置
      'comment_client': client, // 客户端
    }
    const { commentForm } = this.state
    const { commentFormFieldList } = commentForm || {}
    // 过滤评论表中不存在的字段
    Object.keys(result).forEach(key => {
      const exsit = commentFormFieldList?.find((field: any) => field.dataKey === key)
      if (!exsit) {
        delete result[key]
      }
      if (result[key] === undefined) { // 过滤没有值的字段
        delete result[key]
      }
    })
    if (replyData) { // 回复评论时，不需要选择target
      Object.assign(result, {
        'comment_target_id': targetId, // 事项id
      })
    }
    return result
  }

  getDataDetailItem = (id: string, value: any, type?: 'dataKey' | 'fieldId') => {
    const { commentForm } = this.state
    let field: any = {}
    if (!type || type === 'dataKey') {
      field = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === id)
    } else if (type === 'fieldId') {
      field = commentForm?.commentFormFieldList?.find((field: any) => field.id === id)
    }
    const { formId, id: fieldId } = field || {}
    let result: AnyObj = {
      formId,
      formField: {
        id: fieldId,
      },
    }
    if (typeof value === 'string') {
      result = {
        ...result,
        content: value,
        dataText: { content: value },
      }
    } else {
      result = {
        ...result,
        ...value
      }
    }
    return result
  }

  // 处理自定义表单字段
  handleExtraFormData = (customFormDatas: any) => {
    const { commentForm } = this.state
    const commonFiledIds = commentForm?.commentFormFieldList
      ?.filter((field: any) => !commentCommonFields.includes(field.dataKey))
      ?.reduce((acc: any, field: any) => {
        if (field.fields) {
          acc.push(...field.fields.map((f: any) => f.id))
        } else {
          acc.push(field.id)
        }
        return acc
      }, [])
    const { dataDetails, form } = customFormDatas || {}
    if (!dataDetails) {
      return []
    }
    const extraFormData = commonFiledIds.map((fieldId: string) => {
      const detail = dataDetails.find((d: any) => d.formField.id === fieldId)
      return {
        formId: form.id,
        formField: { id: fieldId },
        ...detail,
      }
    })
    return extraFormData
  }

  createCommentBySource = async (params?: AnyObj, replyData?: ReplyOption, fullParams?: AnyObj, resolve?: (v: any) => void, reject?: (v: any) => void) => {
    const { commentForm } = this.state
    const commentDataObj = this.getDataKeyValueObj(fullParams, replyData)
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const contentField = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_origin_content')
    const { form } = contentField || {}
    const dataDetails = Object.entries(commentDataObj).map(([key, value]) => {
      return this.getDataDetailItem(key, value)
    })
    const customFormDatas = this.customFieldsRef.current?.getCustomFieldsDatas?.() || {}
    const extraFormData = this.handleExtraFormData(customFormDatas)
    const data = {
      ...fullParams,
      comment: {
        ...fullParams?.comment,
        targetId: undefined,
        formData: {
          module: moduleData.ebuilderformModule,
          form,
          formLayout: { id: '-1' },
          dataDetails: [...dataDetails, ...extraFormData]
        },
      },
      itemFormId: itemFormId
    }
    const dataResolve = (v: any) => {
      resolve?.(v)
    }
    if (replyData) { // 是回复
      dataResolve?.(data)
    } else {
      this.targetPromiseRef = {
        data,
        resolve: dataResolve,
        reject
      }
      this.onOpenTargetDialog()
    }
  }

  onOpenTargetDialog = () => {
    const { history, targetId } = this.props;

    const path = formatParentPath(this.props);
    history.push(`${path}/${targetId}/addComment/chooseTarget`);
  }

  onBeforeCreateBySource = (params?: AnyObj, replyData?: ReplyOption, fullParams?: AnyObj) => {
    return new Promise((resolve, reject) => {
      this.createCommentBySource(params, replyData, fullParams, resolve, reject)
    })
  }

  onBeforeCreateBySourceForEdit = (params?: AnyObj, fullParams?: AnyObj) => {
    return new Promise((resolve, reject) => {
      this.createCommentBySource(params, undefined, fullParams, resolve, reject)
    })
  }

  /** 获取过滤条件 */
  get filters() {
    const { outerFilter } = this.state
    const { config, page } = this.props.ebProps || {}
    const { filterCondition } = config || {}
    const { filter, isOverrideFilter } = outerFilter || {}
    const filterDatas = handleCommentFilterDatas(outerFilter || {}, page) || []
    let filters = filterCondition?.filter || {}
    const filtersDatas = handleCommentFilterDatas(filterCondition || {}, page) || []
    if (filter?.datas) {
      if (isOverrideFilter) { // 外部搜索条件覆盖
        filters = {
          ...filter,
          datas: filterDatas
        }
      } else {
        filters = {
          ...filter,
          datas: [
            ...filterDatas,
            ...filtersDatas
          ]
        }
      }
    } else {
      filters = {
        ...filters,
        datas: filtersDatas
      }
    }
    return filters
  }

  /** 右上角时间排序 */
  handleCommentFilter = (commentFilter?: string) => {
    if (commentFilter) {
      const { sortId } = this.state
      if (sortId === 'empty' || sortId === '') {
        return []
      }
      const filter = isJSONString(commentFilter) ? JSON.parse(commentFilter) : commentFilter
      const { conditions } = filter
      const sortFields = conditions?.map((item: any) => {
        const { id, orderWay } = item
        if (id === 'ADD_TIME') {
          return {
            tableAlias: 'c',
            field: 'create_time',
            ascending: sortId === 'asc'
          }
        }
      })
      return sortFields
    }
    return []
  }

  deleteQueryKeys = ['targetId', 'commentFilter', 'orderWay']

  getCommentBySource = async (params?: AnyObj, resolve?: (v: any) => void) => {
    const { customSearchConditions, commentForm } = this.state
    const { config } = this.props.ebProps || {}
    const { itemFormId, sortFields: customSortFields } = config || {}
    if (!commentForm) { // filters需要根据表单字段更新
      await this.getCommentFormFields()
    }
    const { commentFilter } = params || {}
    const originSortFields = this.handleCommentFilter(commentFilter)
    const sortFields = [...originSortFields, ...customSortFields]
    const data: any = {
      ...params,
      module: moduleData.ebuilderformModule,
      itemFormId: itemFormId,
      filters: this.filters,
      commentConditionResultParam: {
        sorts: sortFields || [],
        conditions: customSearchConditions
      }
    }
    // 去除数据源模式不需要的参数
    this.deleteQueryKeys.forEach(key => {
      delete data[key]
    })
    resolve?.(data)
  }

  onBeforeGetCommentBySource = (params?: AnyObj) => {
    return new Promise((resolve) => {
      this.getCommentBySource(params, resolve)
    })
  }

  deleteCommentBySource = async (params?: AnyObj, commentData?: ReplyOption, resolve?: (v: any) => void) => {
    const { commentForm } = this.state
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const contentField = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_origin_content')
    const { form, formId, id: fieldId } = contentField || {}
    const data: any = {
      ...params,
      comment: {
        ...params?.comment,
        module: moduleData.ebuilderformModule,
        formData: {
          module: moduleData.ebuilderformModule,
          form,
          formLayout: { id: '-1' },
          dataDetails: [
            {
              formId,
              formField: {
                id: fieldId,
              },
            }
          ]
        }
      },
      itemFormId: itemFormId
    }
    resolve?.(data)
  }

  onBeforeDeleteBySource = (params?: AnyObj, commentData?: ReplyOption) => {
    return new Promise((resolve) => {
      this.deleteCommentBySource(params, commentData, resolve)
    })
  }

  updateCommentBySource = async (params?: AnyObj, fullParams?: AnyObj, replyData?: ReplyOption, resolve?: (v: any) => void) => {
    const { commentForm } = this.state
    const commentDataObj = this.getDataKeyValueObj(fullParams, replyData, true)
    const { id } = fullParams || {}
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const contentField = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_origin_content')
    const { form, formId, id: fieldId } = contentField || {}
    const dataDetails = Object.entries(commentDataObj).map(([key, value]) => {
      return this.getDataDetailItem(key, value)
    })
    const customFormDatas = this.customFieldsRef.current?.getCustomFieldsDatas?.() || {}
    const extraFormData = this.handleExtraFormData(customFormDatas)
    const data = {
      ...fullParams,
      comment: {
        ...fullParams?.comment,
        module: moduleData.ebuilderformModule,
        targetId: undefined,
        formData: {
          id,
          module: moduleData.ebuilderformModule,
          form,
          formLayout: { id: '-1' },
          dataDetails: [...dataDetails, ...extraFormData]
        },
      },
      itemFormId: itemFormId
    }
    resolve?.(data)
  }

  onBeforeUpdateBySource = (params?: AnyObj, commentData?: ReplyOption, fullParams?: AnyObj) => {
    return new Promise((resolve) => {
      this.updateCommentBySource(params, fullParams, commentData, resolve)
    })
  }

  /** 处理评论右上角按钮高级搜索参数 */
  handleCustomSearch = (commentFilter?: string) => {
    if (commentFilter) {
      const filter = isJSONString(commentFilter) ? JSON.parse(commentFilter) : commentFilter
      const { conditions } = filter
      const filterFields = this.getCustomSearchConditions(conditions)
      return filterFields
    }
    return []
  }

  /** 自定义评论右上角按钮高级搜索参数 */
  getCustomSearchConditions = (conditions: any[]) => {
    const operatorTransformer: any = {
      like: 'LIKE',
      unlike: 'NOT_LIKE',
      isnull: 'IS_NULL',
      isnotnull: 'NOT_NULL',
      eq: 'EQUAL',
      neq: 'NOT_EQUAL'
    }
    const results: any = []
    conditions.forEach(con => {
      const { id, operate, value, values, dateEnd, dateStart } = con
      if (id === 'CONTENT_SNAP') {
        let condition = {
          tableAlias: 'c',
          field: 'comment_origin_content',
          value,
          operator: operatorTransformer[operate]
        }
        results.push(condition)
      } else if (id === 'commentor') {
        let condition = {
          tableAlias: 'c',
          field: 'creator',
          value: values,
          operator: operatorTransformer[operate]
        }
        results.push(condition)
      } else if (id === 'add_time') {
        let condition1 = {
          tableAlias: 'c',
          field: 'create_time',
          value: dateStart,
          valueType: 'DATE',
          operator: 'GREATER_EQUAL'
        }
        let condition2 = {
          tableAlias: 'c',
          field: 'create_time',
          value: dateEnd,
          valueType: 'DATE',
          operator: 'LESS_EQUAL'
        }
        results.push(condition1, condition2)
      } else if (id === 'e_type') {
        let condition = {
          tableAlias: 'e',
          field: 'type',
          value,
          operator: operatorTransformer[operate]
        }
        results.push(condition)
      }
    })
    return results
  }

  /** 自定义评论右上角按钮高级搜索参数 */
  handleCustomSearchConditions = (conditions: any[], searchAdvancedStore?: SearchAdvancedStore) => {
    const results = this.getCustomSearchConditions(conditions)
    this.setState({ customSearchConditions: results })
    return []
  }

  /** 获取数据源模式公共参数，eb评论接口需要appId和objId参数 */
  get commonParams() {
    const { config } = this.props.ebProps || {}
    const { relatedDataSource } = config || {}
    const { appId, id: objId } = relatedDataSource || {}
    const commonParams = { // eb评论接口需要appId和objId参数
      appId,
      otherParam: {
        objId
      }
    }
    return commonParams
  }

  /** 使用数据源模式 */
  getDataSourcePropsForEdit = () => {
    const { config } = this.props.ebProps || {}
    const commonParams = this.commonParams
    if (config?.relatedDataSource) {
      return {
        objectModule: moduleData.ebuilderformObjectModule,
        commentParams: commonParams,
        createParams: commonParams,
        deleteParams: commonParams,
        updataParams: commonParams,
        viewParams: commonParams,
        module: moduleData.ebuilderformModule,
        useFullParams: true,
        createUrl,
        onBeforeCreate: this.onBeforeCreateBySourceForEdit,
        renderExtraConent: this.renderExtraConentForEdit
      }
    }
    return {}
  }

  /** 自定义接口请求 */
  fetchDataBySource = (options: RequestOptionConfig) => {
    const commonParams = this.commonParams
    const { url } = options
    if (url === commentCommentedUrl) {
      options.method = 'POST'
      const parentKey = 'comment.parent.id'
      const parentId = options.params?.[parentKey]
      options.data = {
        ...options.params,
        ...commonParams,
        comment: {
          parent: { id: parentId }
        }
      }
      delete options.data?.[parentKey]
      delete options.params
    }
    return request(options)
  }

  renderExtraConentForEdit = () => {
    const customFields = this.renderCustomFields({})
    if (this.hasCustomFields) {
      return (
        <>
          {this.renderExtraConent()}
          {customFields}
        </>
      )
    }
    return this.renderExtraConent()
  }

  /** 是否有自定义字段 */
  get hasCustomFields() {
    const { commentForm } = this.state
    const { commentFormFieldList = [] } = commentForm || {}
    const fields = commentFormFieldList.filter((field: any) => !commentCommonFields.includes(field.dataKey))
    return !!fields.length
  }

  /** 是否显示自定义字段 */
  get showCustomFields() {
    const { showCustomField } = this.props
    return this.hasCustomFields && showCustomField
  }

  customFieldsRef = createRef<any>()

  /** 数据源模式，评论输入框下渲染自定义字段 */
  renderCustomFields = (comConfig?: CustomFieldsComConfig) => {
    const { commentForm } = this.state
    const { config } = this.props.ebProps || {}
    return <CustomFields weId={`${this.props.weId || ''}_n2jgdz`}
      config={config}
      isMobile
      comConfig={comConfig}
      commentForm={commentForm}
      ref={this.customFieldsRef}
    />
  }

  /** 使用数据源模式 */
  getDataSourceProps = () => {
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const commonParams = this.commonParams
    if (config?.relatedDataSource) {
      return {
        objectModule: moduleData.ebuilderformObjectModule,
        commentParams: commonParams,
        createParams: commonParams,
        deleteParams: commonParams,
        updataParams: commonParams,
        viewParams: commonParams,
        module: moduleData.ebuilderformModule,
        useFullParams: true,
        createUrl,
        onBeforeCreate: this.onBeforeCreateBySource,
        commentUrl,
        onBeforeGetComment: this.onBeforeGetCommentBySource,
        deleteUrl,
        onBeforeDelete: this.onBeforeDeleteBySource,
        updataUrl: updataUrl,
        onBeforeUpdate: this.onBeforeUpdateBySource,
        getCustomSearchConditions: this.handleCustomSearchConditions,
        renderMoreEditContent: this.renderMoreEditContent,
        // 子评论
        queryChildrenUrl,
        queryChildrenParams: { itemFormId, filters: this.filters, ...commonParams },
        // 直接子评论
        commentCommentedUrl,
        commentCommentedParams: { itemFormId, module: moduleData.ebuilderformModule },
        // 统计
        statisticUrl,
        onBeforeSelectCommentCountAdvancedSearch: this.onBeforeSelectCommentCountAdvancedSearchBySource,
        fetchData: this.fetchDataBySource
      }
    }
    return {}
  }

  selectCommentCountAdvancedSearchBySource = async (params?: AnyObj, resolve?: (v: any) => void) => {
    const { customSearchConditions } = this.state
    const { config } = this.props.ebProps || {}
    const { itemFormId, sortFields } = config || {}

    const { commentFilter } = params || {}
    const originSearchConditions = this.handleCustomSearch(commentFilter)
    const searchConditions = [...customSearchConditions, ...originSearchConditions]

    const commonParams = this.commonParams
    const data: any = {
      ...params,
      ...commonParams,
      module: moduleData.ebuilderformModule,
      itemFormId: itemFormId,
      filters: this.filters,
      commentConditionResultParam: {
        sorts: sortFields || [],
        conditions: searchConditions
      }
    }
    // 去除数据源模式不需要的参数
    this.deleteQueryKeys.forEach(key => {
      delete data[key]
    })
    resolve?.(data)
  }

  onBeforeSelectCommentCountAdvancedSearchBySource = (params?: AnyObj) => {
    return new Promise((resolve) => {
      this.selectCommentCountAdvancedSearchBySource(params, resolve)
    })
  }

  renderMoreEditContent = (data?: ReplyOption, type?: 'edit' | 'reply' | 'quote') => {
    const isReply = (type === 'edit' && !!data?.parent) || type === 'reply'
    const { commentForm } = this.state
    const { config } = this.props.ebProps || {}
    if (!isReply && this.hasCustomFields) {
      return <CustomFields weId={`${this.props.weId || ''}_n2jgdz`}
        config={config}
        isMobile
        comConfig={{
          type,
          data
        }}
        commentForm={commentForm}
        ref={this.customFieldsRef}
      />
    }
    return <></>
  }

  onCancel = () => {
    console.log('onCancel')
  }

  onOk = (targetData: any) => {
    const { data, resolve } = this.targetPromiseRef
    const { commentForm } = this.state
    const targetId = targetData?.[0]?.id

    if (targetId) {
      const field = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_target_id')
      const { formId, id: fieldId } = field || {}
      const result = {
        formId,
        formField: {
          id: fieldId,
        },
        content: targetId,
        dataText: { content: targetId },
      }
      if (data?.comment?.formData?.dataDetails) {
        data.comment.formData.dataDetails.push(result)
      }
      resolve(data)

      this.props.history.go(-1)
    } else {
      toast({
        content: getLabel('312096', '请选择关联数据')
      })
    }
  }

  onClear = () => {
    this.props.history.go(-1)
  }

  onClose = (...res: any) => {
    //console.log('onClose', res)
  }

  renderCommentTarget = () => {
    const { ebProps } = this.props;
    const { relatedDataSource } = ebProps?.config!;
    return (
      <>
        <div className={`${prefixCls}-m-target-title`}>{getLabel('312097', '选择关联数据')}</div>
        <MBrowserPanel weId={`${this.props.weId || ''}_vcq6x2`}
          type={'ebuilder'}
          module={'ebuilder/form'}
          commonParams={{
            // eb特殊格式，开启权限校验
            ebBrowserForEditParam: {
              objId: relatedDataSource?.id
            }
          }}
          className={`${prefixCls}-m-target-browser`}
          onOk={this.onOk}
          onCancel={this.onCancel}
          onClear={this.onClear}
          onClose={this.onClose}
          notCloseDialog
        />
      </>
    )
  }

  /** 一般模式下渲染自定义字段 */
  renderCustomMoreContent = (data: ReplyOption) => {
    const isReply = !!data?.parent
    if (!isReply && this.showCustomFields) {
      return this.renderCustomFields({ readOnly: true, data, type: 'show' })
    }
    return <></>
  }

  renderCommentEditCom = (inDialog: boolean = true) => {
    const { isDesign, ebProps, ...resProps } = this.props;
    const { createParams } = this.store;

    const {
      targetId,
      module,
      objectModule,
      actionConfig,
      usePrivated,
      usePhrase,
      usePosition,
      associativeProps,
      useRichText,
      useAddress,
      useSignApprove,
      useSignature,
      //@ts-ignore
      allowAnonymousSetting,
      //@ts-ignore
      useAnonymous,
      //@ts-ignore
      defaultAnonymousByCheck,
    } = resProps;

    const path = formatParentPath(this.props);

    const cls = classnames(`${prefixCls}-m-add`, {
      [`${prefixCls}-m-add-input-only`]: !inDialog,
    })

    return (
      <div className={cls}>
        <MCommentEdit
          weId={`${this.props.weId || ''}_vtv281`}
          onCancel={this.handleCreateCancel}
          targetId={targetId}
          renderExtraConent={this.renderExtraConent}
          module={module || "share"}
          useRichText={useRichText}
          usePrivated={usePrivated}
          useAddress={useAddress}
          createUrl={`/api/${objectModule || "bcw"}/common/comment/createComment`}
          onCreated={this.handleCreated}
          onAfterCreate={this.handleAfterCreate}
          createParams={createParams}
          hiddenMoreBtn={false}
          actionConfig={actionConfig as any}
          usePhrase={usePhrase}
          usePosition={usePosition}
          associativeProps={associativeProps}
          useSignApprove={useSignApprove}
          useSignature={useSignature}
          //@ts-ignore
          useAnonymous={useAnonymous}
          //@ts-ignore
          allowAnonymousSetting={allowAnonymousSetting}
          //@ts-ignore
          defaultAnonymousByCheck={defaultAnonymousByCheck}
          {...this.getDataSourcePropsForEdit()}
        />
        <MDialog weId={`${this.props.weId || ''}_3l6798`}
          path={`${path}/${targetId}/addComment/chooseTarget`}
          isRouteLayout
        >
          {this.renderCommentTarget()}
        </MDialog>
      </div>
    )
  }

  render() {
    const { commentKey } = this.state
    const { isDesign, ebProps, ...resProps } = this.props;
    const {
      targetId,
      className,
      componentShowType,
      showText,
      showEditAvatar,
    } = resProps;

    const cls = classnames(`${prefixCls}-m`, className, {
      [`${prefixCls}-m-container-input-only`]: componentShowType === 'input',
      [`${prefixCls}-m-container-list-only`]: componentShowType === 'list',
    })

    const path = formatParentPath(this.props);

    if (componentShowType === 'input' && showText && !showEditAvatar) {
      return this.renderCommentEditCom(false)
    }

    const content = (
      <>
        <MComment
          weId={`${this.props.weId || ''}_mukcrz`}
          key={commentKey}
          //@ts-ignore
          customRenderHeader={this.customRenderHeader}
          onAfterGetComment={this.handleAfterGetComment}
          renderItemHeaderInfo={this.renderItemHeaderInfo}
          onAfterCreate={this.handleAfterReply}
          wrappedComponentRef={this.commentRef}
          searchInitDatas={searchInitDatas}
          // @ts-ignore
          customSearchConfig={customSearchConfig}
          onAfterDelete={this.handleAfterDelete}
          // @ts-ignore
          searchAdvancedProps={this.searchAdvancedProps}
          optionMax={4}
          {...this.props}
          className={cls}
          {...this.getDataSourceProps()}
          renderCustomMoreContent={this.renderCustomMoreContent}
          {...this.getDisplayProps()}
        />
        <MDialog
          weId={`${this.props.weId || ''}_5y3j8j`}
          path={`${path}/${targetId}/addComment`}
          isRouteLayout
        >
          {this.renderCommentEditCom()}
        </MDialog>
      </>
    )

    return (
      <CommentProvider
        weId={`${this.props.weId || ''}_gprr69`}
        value={this.contextValue}
      >
        {content}
      </CommentProvider>
    )
  }
}

export default withRouter(MEbComment);