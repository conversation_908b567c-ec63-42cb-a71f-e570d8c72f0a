import { Checkbox } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import { FC } from "react";
import { commentClsPrefix } from "../../../../constants";
import { useCommentStore } from "../../context/ComContext";
import { observer } from "mobx-react";
import { BlogProps } from "./type";

const prefixCls = commentClsPrefix;

const Blog: FC<BlogProps> = (props) => {

  const { className } = props;

  const { blogStatus, handleChangeBlog } = useCommentStore();

  return (
    <div className={className || `${prefixCls}-pc-blog`}>
      <Checkbox
        weId={`${props.weId || ''}_ssyji6`}
        value={blogStatus}
        //@ts-ignore
        onChange={handleChangeBlog}
      >
        {getLabel("223898", "同步发布日报")}
      </Checkbox>
    </div>
  )
}

export default observer(Blog);