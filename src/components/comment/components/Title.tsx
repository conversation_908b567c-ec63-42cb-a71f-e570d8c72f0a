import { FC, useCallback, useEffect, useState } from "react";
import { ComBtnDataType, CommentFilterType } from "../types";
import CommentHeader from "./header-com";
import { corsImport } from "@weapp/utils";
import { commentClsPrefix } from "../../../constants";
import { FILTER_CHANGE, GET_TOTAL } from "../constant/eventNames";
import { AnyObj } from "@weapp/ui";
import { getParentId } from "../utils";
import { CommentTitleProps } from "./type";

const prefixCls = commentClsPrefix;

const CommentTitle:FC <CommentTitleProps> = (props) => {

  const { id, config, events } = props;
  const { titleEnabled, title, showTotal } = config;
  
  const [total, setTotal] = useState(0);

  const parentId = getParentId(props);

  const handChange = useCallback((key: CommentFilterType, data: ComBtnDataType) => {
    events?.emit(FILTER_CHANGE, id, key, data);
  }, [events, id])

  useEffect(() => {
    // 评论数据改变回调
    events?.on(GET_TOTAL, id as any, setTotal)
    return () => {
      events?.off(GET_TOTAL, id as any)
    }
  }, [id])
  
  const renderHeaderCom = (parentComId?: string, hasHeader?: boolean) => {
    // corsImport("@weapp/ebdnavcoms").then(modules => {
    //   // 触发选项卡顶部渲染事件改变方法
    //   const { renderExtraContent: eventName } = modules?.constants?.EVENT_NAME?.NEWTAB_EVENT_NAME || {};
    //   if (parentComId && eventName) {
    //     const headerContent = ["config", {
    //       app: "@weapp/ui-props-design",
    //       compName: "CommentHeader",
    //       config,
    //       onDataChange: handChange
    //     }]
    //     // title生效时选项卡顶部渲染空
    //     const tabConfig = hasHeader ? headerContent : ["dom", null];
    //     events?.emit(eventName, parentComId, ...tabConfig);
    //   }
    // })
  }

  const handleDragOut = (params: AnyObj) => {
    const { target } = params;
    const { id: targetId } = target;
    // 拖入拖出的不是当前评论组件，不处理内容
    if (id !== targetId) {
      return ;
    }
    renderHeaderCom(parentId, false);
  }

  const handleDragIn = (params: AnyObj) => {
    const { target, id: _parentId } = params;
    const { id: targetId } = target;
    if (id !== targetId) {
      return ;
    }
    renderHeaderCom(_parentId, !titleEnabled);
  }

  useEffect(() => {
    // corsImport("@weapp/ebdnavcoms").then(modules => {
    //   // 触发选项卡顶部渲染事件改变方法
    //   const { moveIn, moveOut } = modules?.constants?.EVENT_NAME?.NEWTAB_EVENT_NAME || {};
    //   if (parentId) {
    //     //@ts-ignore
    //     events?.on(moveIn, parentId, handleDragIn);
    //     //@ts-ignore
    //     events?.on(moveOut, parentId, handleDragOut);
    //   }
    // })
    // return () => {
    //   corsImport("@weapp/ebdnavcoms").then(modules => {
    //     // 触发选项卡顶部渲染事件改变方法
    //     const { moveIn, moveOut } = modules?.constants?.EVENT_NAME?.NEWTAB_EVENT_NAME || {};
    //     if (parentId) {
    //       //@ts-ignore
    //       events?.off(moveIn, parentId);
    //       //@ts-ignore
    //       events?.off(moveOut, parentId);
    //     }
    //   })
    // }
  }, [events, parentId])

  useEffect(() => {
    renderHeaderCom(parentId, !titleEnabled)
  }, [config, parentId, titleEnabled]);

  if (!titleEnabled) {
    return null;
  }

  // const headerIcons = (
  //   <CommentHeader
  //     {...props}
  //     weId={`${props.weId || ''}_r5k8ew`}
  //     onDataChange={handChange}
  //   />
  // )
  const titleContent = (title as any)?.nameAlias ?? title;

  return (
    <div
      className={`${prefixCls}-title`}
    >
      <div className={`${prefixCls}-title-content`}>
        {titleContent}
        {showTotal && `(${total})`}
      </div>
      {/* {headerIcons} */}
    </div>
  )
}

export default CommentTitle