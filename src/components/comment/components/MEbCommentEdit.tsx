import { observer } from 'mobx-react';
import { PureComponent } from 'react';
import { MEbCommentEditProps } from './type';
import { classnames, getLabel } from '@weapp/utils';
import { MAvatar } from '@weapp/ui';
import { commentClsPrefix } from '../../../constants';

const prefixCls = commentClsPrefix;

interface MEbCommentEditState {
}

@observer
export default class MEbCommentEdit extends PureComponent<MEbCommentEditProps, MEbCommentEditState> {
  constructor(props: MEbCommentEditProps) {
    super(props);
    this.state = {
    }
  }

  handleOpenEdit = () => {
    const { isDesign, handleOpenEdit } = this.props;
    if (!isDesign) {
      handleOpenEdit?.()
    }
  }

  render() {
    const { showEditAvatar, hasEdit, avatarUrl, username } = this.props;

    if (!hasEdit) return null
    const cls = classnames(`${prefixCls}-m-edit`)
    return (
      <div
        className={cls}
        onClick={this.handleOpenEdit}
      >
        {showEditAvatar && (
          <div
            className={`${prefixCls}-m-edit-avatar`}
          >
            <MAvatar
              weId={`${this.props.weId || ''}_e9u98a`}
              name={username}
              url={avatarUrl}
            />
          </div>
        )}
        <div className={`${prefixCls}-m-edit-body`}>
          <div className={`${prefixCls}-m-edit-text`}>
            {getLabel("206977", "请输入评论内容...")}
          </div>
        </div>
      </div>
    );
  }
}
