import { Any<PERSON>bj, Button, Comment, CommentProps, CorsComponent, Dialog, Empty, FormDatas, Icon, ListProps, ReplyOption, SearchAdvancedPanelProps, SearchAdvancedStore, utils } from "@weapp/ui";
import { RequestOptionConfig, RequestResult, classnames, getLabel, isEqual, request } from "@weapp/utils";
import { PureComponent, ReactNode, createRef } from "react";
import commentData from "../mock/commentData.json";
import commentReplyData from "../mock/commentReplyData.json";
import { commentClsPrefix } from "../../../constants";
import { GET_TOTAL, OPERATION_BTN, SEARCH_COMMENT, TOP_COMMENT } from "../constant/eventNames";
import { CommentProvider } from "../context/ComContext";
import CommentStore from "./Store";
import { formatDate, handleCommentFilterDatas, hasBlog, isJSONString, openCommentHrmCard } from "../utils";
import Blog from "./blog";
import { observer } from "mobx-react";
import { EbCommentProps } from "./type";
import { DiyBtn, EventTo } from "../../../ebConfig/operation-btns/types";
import { cascadeRules, customSearchConfig, searchInitDatas } from "../utils/search";
import { commentCommentedUrl, commentUrl, createUrl, deleteCommentFormSetting, deleteUrl, queryChildrenUrl, statisticUrl, updataUrl, updateTransmitCountUrl } from "../api/data";
import { commentCommonFields, CommentEvents, moduleData } from "../constant";
import { CustomFields, CustomFieldsComConfig } from "./form/customFields";
import { TargetData } from "./form/TargetData";
import { getCommentFormFieldsMemo } from "../utils/getApiDatas";

type CustomFormDatasTools = {
  [_: string]: {
    getCustomFieldsDatas?: () => AnyObj;
  }
}

interface EbCommentState {
  editVisible: boolean;
  /** 输入框展开状态缓存 */
  visibelChangeType: "props" | "state";
  outerFilter: any;
  commentForm?: AnyObj;
  /** 编辑评论输入框数据 */
  editCustomFormDatasTools: CustomFormDatasTools;
  /** 回复评论输入框数据 */
  replyCustomFormDatasTools: CustomFormDatasTools;
  customSearchConditions: AnyObj[];
  targetDialogVisible: boolean;
  /** 数据源模式，三种按钮切换 */
  sortId: 'asc' | 'desc' | 'empty' | '';
  /** 用于组件刷新 */
  commentKey: string;
}


const prefixCls = commentClsPrefix;

const emptyIconStyle = {
  width: 150,
  height: 150,
}

@observer
export default class EbComment extends PureComponent<EbCommentProps, EbCommentState> {

  commentRef = createRef<any>();

  targetPromiseRef: AnyObj = {
    data: {},
    resolve: null,
    reject: null
  }

  store = new CommentStore();

  /** 组件内备份评论总数，便于新增时计算总数 */
  commentTotal = 0;

  contextValue = {
    store: this.store,
  }
  private intervalId: NodeJS.Timeout | undefined | number;

  constructor(props: EbCommentProps) {
    super(props);
    this.state = {
      editVisible: false,
      visibelChangeType: "props",
      outerFilter: {},
      commentForm: undefined,
      editCustomFormDatasTools: {},
      replyCustomFormDatasTools: {},
      customSearchConditions: [],
      targetDialogVisible: false,
      sortId: '',
      commentKey: `${Math.random() * 1000}`
    }

    this.intervalId = undefined;
  }

  static getDerivedStateFromProps(nextProps: EbCommentProps, prevState: EbCommentState) {
    const newState: Partial<EbCommentState> = {};
    const { showText } = nextProps;
    const { visibelChangeType } = prevState;

    // config文本框展开配置项从选中转为非选中时收起文本框
    // visibelChangeType记录editVisible属性修改来源，state修改时不触发受控
    if (showText === false && visibelChangeType === "props") {
      newState.editVisible = false;
    }
    if (showText === true) {
      newState.editVisible = true;
      newState.visibelChangeType = "props";
    }
    return newState;
  }

  componentDidMount() {
    const { id, events } = this.props.ebProps!;
    events?.on(OPERATION_BTN, id as any, this.handleFilterChange);

    this.startRefreshing();

    events?.on(CommentEvents.COMMENT_UPDATE_COMMENTFORM, id as any, this.updateCommentForm);
  }

  componentDidUpdate(prevProps: EbCommentProps) {
    const { relatedDataSource: prevRelatedDataSource, filterCondition: prevFilterCondition, sortFields: prevSortFields } = prevProps.ebProps?.config || {}
    const { relatedDataSource, filterCondition, sortFields } = this.props.ebProps?.config || {}
    if (prevProps.refresh !== this.props.refresh) {
      this.stopRefreshing();
      this.startRefreshing();
    }
    // 数据源、过滤条件、排序条件变化时，刷新评论列表
    if (!isEqual(prevRelatedDataSource?.id, relatedDataSource?.id) || !isEqual(prevFilterCondition, filterCondition) || !isEqual(prevSortFields, sortFields)) {
      this.commentRef.current?.refresh?.()
    }
  }

  componentWillUnmount() {
    const { id, events } = this.props.ebProps!;
    events?.off(OPERATION_BTN, id as any);

    this.stopRefreshing();

    events?.off(CommentEvents.COMMENT_UPDATE_COMMENTFORM, id as any);
  }

  updateCommentForm = (commentForm: AnyObj) => {
    this.setState({ commentForm })
  }

  startRefreshing() {
    const { refresh } = this.props;
    if (refresh && refresh !== 'noRefresh') {
      let timeValue: number = 0

      if (refresh.indexOf('custom_') > -1) {
        timeValue = refresh.split('_')[1] === 'default' ? 5 * 60 : Number(refresh.split('_')[1]) * 60
      } else {
        const [time, unit] = refresh.split('_')
        timeValue = unit === 'second' ? Number(time) : Number(time) * 60
      }
      if (timeValue > 0) {
        this.intervalId = setInterval(this.refresh, timeValue * 1000)
      }
    };
  }

  stopRefreshing() {
    clearInterval(this.intervalId);
  }

  getCommentFormFields = async () => {
    const { config, onConfigChange } = this.props.ebProps || {}
    const { itemFormId, relatedDataSource } = config || {}
    if (!itemFormId) return;
    const res = await getCommentFormFieldsMemo(itemFormId, relatedDataSource?.dataExtMemoTime)
    this.setState({ commentForm: res.data })
    onConfigChange?.({ commentForm: res.data })
  }

  setCommentTotal = (total: number) => {
    const { events, id } = this.props.ebProps!;
    this.commentTotal = total;
    events?.emit(GET_TOTAL, id, total);
  }

  handleFilterChange = (e: any, btn: DiyBtn) => {
    const { action, options, event } = btn as any;
    switch (action || event.to) {
      case "statistics": {
        this.commentRef.current?.onStatistic?.();
        break;
      }
      case "search": {
        const { onShowSearch, searchConditionId } = this.commentRef.current || {};
        onShowSearch?.(searchConditionId);
        break;
      }
      case "sort": {
        const sortId = options.find((opt: any) => opt.selected)?.id
        this.setState({ sortId }, () => {
          this.commentRef.current?.onSortChange?.();
        })
        break;
      }
      case "REFRESHCOM": {
        this.refreshCom();
        break;
      }
    }
  }

  renderEditOptions = () => {

    const { useBlog, ebProps } = this.props;
    const { moduleValue } = ebProps?.config!;

    const blogConfig = [
      <Blog weId={`${this.props.weId || ''}_m5kuym`} />
    ]

    if (hasBlog(moduleValue!) && useBlog) {
      return blogConfig;
    }

    return [];
  }

  handleEditSubmitted = (data?: ReplyOption) => {
    const { blogStatus, addBlog } = this.store;
    blogStatus && addBlog(data);
  }

  handleEditVisibleChange = () => {
    this.setState({ editVisible: true, visibelChangeType: "state" });
  }

  handleAfterGetComment = async (result: RequestResult<any>) => {
    const { isDesign, ebProps } = this.props;
    const { replyUnderComment, relatedDataSource } = ebProps?.config!;
    if (isDesign) {
      if (!relatedDataSource || !result.data.page.result.length) {
        // design展示数据mock
        result = replyUnderComment ? commentReplyData : commentData;
      }
    }
    const { totalCount = 0 } = result?.data?.page || {};
    this.setCommentTotal(totalCount);
    return result;
  }

  handleAfterCreate = async (result: RequestResult) => {
    const { ebProps } = this.props;
    const { replyUnderComment } = ebProps?.config!;
    const { status } = result;
    const { parent } = result.data?.data || {};
    // 评论成功则数量加一（回复按楼层显示除外）
    if (status && !(parent && replyUnderComment)) {
      this.setCommentTotal(this.commentTotal + 1);
    }
    return result;
  }

  handleAfterDelete = async (result: RequestResult, commentData?: ReplyOption) => {
    const { ebProps } = this.props;
    const { replyUnderComment } = ebProps?.config!;
    const { status } = result;
    const { parent } = commentData || {};
    // 评论成功则数量加一（回复按楼层显示除外）
    if (status && !(parent && replyUnderComment)) {
      this.setCommentTotal(this.commentTotal - 1);
    }
    return result;
  }

  refresh = () => {
    this.commentRef.current?.refresh?.();
  }

  /** 刷新Comment组件 */
  refreshCom = () => {
    this.setState({ commentKey: `${Math.random() * 1000}` })
  }

  handleSearchDataChange = () => {
    const searchAdvancedStore = this.commentRef.current?.searchAdvancedStore;

    if (!searchAdvancedStore) {
      return;
    }
    const { formStore } = searchAdvancedStore as SearchAdvancedStore;
    const { getFormDatas } = formStore;
    const formDatas = getFormDatas();

    let isSearchEmpty = true;
    Object.keys(formDatas).forEach((key) => {
      // 是否是内容
      const isValue = key.match(/^value_/);
      const value = formDatas[key];
      let isValueEmpty = true;
      if (isValue) {
        isValueEmpty = utils.isEmpty(value);
      }
      const isOperateContent = key.match(/^operate_content/);
      if (isOperateContent) {
        const nullList = ["isnotnull", "isnull"];
        isValueEmpty = !nullList.includes(value)
      }
      isSearchEmpty = isSearchEmpty && isValueEmpty;
    })

    const { id, events } = this.props.ebProps!;
    events?.emit(SEARCH_COMMENT, id, isSearchEmpty);
  }

  handleSearchChange = (value?: FormDatas, otherParams?: any) => {
    const searchAdvancedStore = this.commentRef.current?.searchAdvancedStore;
    if (!searchAdvancedStore) {
      return;
    }
    const { layoutConfig: { id: layoutId } } = otherParams;

    const { formStore } = searchAdvancedStore as SearchAdvancedStore;
    if (value) {
      Object.keys(value).forEach(key => {
        const dataValue = value[key] as string;
        const rule = cascadeRules[key]?.[dataValue];
        if (rule) {
          const { setLayoutProps } = formStore;
          setLayoutProps(layoutId, {
            items: rule,
          });
        }
      })
    }
    this.handleSearchDataChange();
  }

  handleChangeLabel = () => {
    this.handleSearchDataChange();
  }

  handleSearchReset = () => {
    this.handleSearchDataChange();
  }

  handleChooseSnapChange = () => {
    this.handleSearchDataChange();
  }

  handleSearchCacnel = () => {
    const {
      events, id,
    } = this.props.ebProps!;
    events?.emit(TOP_COMMENT, id, EventTo.SEARCH);
  }

  handleStatisticCancel = () => {
    const {
      events, id,
    } = this.props.ebProps!;
    events?.emit(TOP_COMMENT, id, EventTo.STATISTICS);
  }

  handleAfterDeleteCondition = () => {
    this.handleSearchDataChange();
  }

  listProps: ListProps = {
    bodyKey: "eb-comment-list",
  }

  unChooseSnap = () => {
    this.handleSearchDataChange();
  }

  onChooseSnap = async (id: string) => {
    const searchAdvancedStore: SearchAdvancedStore = this.commentRef.current?.searchAdvancedStore;
    if (!searchAdvancedStore) {
      return;
    }
    const { commonFilterStore: { checkSnaps } } = searchAdvancedStore;
    await checkSnaps(id);
    this.handleSearchDataChange();
  }

  searchAdvancedPanelProps: Partial<SearchAdvancedPanelProps> = {
    onChange: this.handleSearchChange,
    onCancel: this.handleSearchCacnel,
    onReset: this.handleSearchReset,
    unChooseSnap: this.unChooseSnap,
    onChooseSnap: this.onChooseSnap,
    formProps: {
      // @ts-ignore
      afterDeleteCondition: this.handleAfterDeleteCondition
    }
  }

  renderOptionsInfo = () => {

  }

  renderItemHeaderInfo = (data: ReplyOption) => {
    const { dayWeibo, id } = data;

    const leftContent: ReactNode[] = [];
    const rightContent: ReactNode[] = [];
    if (dayWeibo) {
      const blogData = formatDate(dayWeibo);
      const blogNode = (
        <div
          className={`${prefixCls}-pc-blog-info`}
          key="blog"
        >
          <Icon
            weId={`${this.props.weId || ''}_my9ejp`}
            name="Icon-Cloud-synchronization"
            size="lg"
          />
          <span>
            {getLabel("223896", "已同步发布{0}日报", [blogData])}
          </span>
        </div>
      )
      rightContent.push(blogNode);
    }

    return (
      <div
        className={`${prefixCls}-pc-header-info`}
        key={id}
      >
        <div className={`${prefixCls}-pc-header-left`}>
          {leftContent}
        </div>
        <div className={`${prefixCls}-pc-header-right`}>
          {rightContent}
        </div>
      </div>
    )
  }

  renderEmpty = () => {
    return (
      <div className={`${prefixCls}-pc-empty`}>
        <Empty
          weId={`${this.props.weId || ''}_nqoefm`}
          description={getLabel('213265', '暂无数据')}
          image={(
            <Icon
              weId={`${this.props.weId || ''}_qvky27`}
              name="Icon-empty-Nodata-mcolor"
              style={emptyIconStyle}
            />
          )}
        />
      </div>
    )
  }

  /** 获取评论显示相关配置（显示部门） */
  getDisplayProps = () => {
    if (!this.isAdvanced) {
      return {
        renderItemHeaderMore: this.renderItemHeaderMore
      }
    }
    return {}
  }

  renderItemHeaderMore = (data?: AnyObj) => {
    const { showDepartment, showDepartmentBelow } = this.props
    const { department } = data?.otherParam?.hrmInfo || {}
    if (showDepartment && !showDepartmentBelow) {
      if (department?.id) {
        return <span className={`${prefixCls}-hrm-span`} onClick={openCommentHrmCard(department.id, 'department')}>{department.name}</span>
      }
    }
    return <></>
  }

  get isAdvanced() {
    const { config } = this.props.ebProps || {}
    return config?.displaySetting?.displayMode === 'advanced'
  }

  /** 处理评论右上角按钮高级搜索参数 */
  handleCustomSearch = (commentFilter?: string) => {
    if (commentFilter) {
      const filter = isJSONString(commentFilter) ? JSON.parse(commentFilter) : commentFilter
      const { conditions } = filter
      const filterFields = this.getCustomSearchConditions(conditions)
      return filterFields
    }
    return []
  }

  /** 获取自定义评论右上角按钮高级搜索参数 */
  getCustomSearchConditions = (conditions: any[]) => {
    const operatorTransformer: any = {
      like: 'LIKE',
      unlike: 'NOT_LIKE',
      isnull: 'IS_NULL',
      isnotnull: 'NOT_NULL',
      eq: 'EQUAL',
      neq: 'NOT_EQUAL'
    }
    const results: any = []
    conditions.forEach(con => {
      const { id, operate, value, values, dateEnd, dateStart } = con
      if (id === 'CONTENT_SNAP') {
        let condition = {
          tableAlias: 'c',
          field: 'comment_plain_content',
          value,
          operator: operatorTransformer[operate]
        }
        results.push(condition)
      } else if (id === 'commentor') {
        let condition = {
          tableAlias: 'c',
          field: 'creator',
          value: values,
          operator: operatorTransformer[operate]
        }
        results.push(condition)
      } else if (id === 'add_time') {
        let condition1 = {
          tableAlias: 'c',
          field: 'create_time',
          value: dateStart,
          valueType: 'DATE',
          operator: 'GREATER_EQUAL'
        }
        let condition2 = {
          tableAlias: 'c',
          field: 'create_time',
          value: dateEnd,
          valueType: 'DATE',
          operator: 'LESS_EQUAL'
        }
        results.push(condition1, condition2)
      } else if (id === 'e_type') {
        let condition = {
          tableAlias: 'e',
          field: 'type',
          value,
          operator: operatorTransformer[operate]
        }
        results.push(condition)
      }
    })
    return results
  }

  /** 自定义评论右上角按钮高级搜索参数 */
  handleCustomSearchConditions = (conditions: any[], searchAdvancedStore?: SearchAdvancedStore) => {
    const results = this.getCustomSearchConditions(conditions)
    this.setState({ customSearchConditions: results })
    return []
  }

  /** 获取数据源模式公共参数，eb评论接口需要appId和objId参数 */
  get commonParams() {
    const { config } = this.props.ebProps || {}
    const { relatedDataSource } = config || {}
    const { appId, id: objId } = relatedDataSource || {}
    const commonParams = { // eb评论接口需要appId和objId参数
      appId,
      otherParam: {
        objId
      }
    }
    return commonParams
  }

  /** 使用数据源模式 */
  getDataSourceProps = () => {
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const commonParams = this.commonParams
    if (config?.relatedDataSource) {
      return {
        objectModule: moduleData.ebuilderformObjectModule,
        commentParams: commonParams,
        createParams: commonParams,
        deleteParams: commonParams,
        updataParams: commonParams,
        viewParams: commonParams,
        module: moduleData.ebuilderformModule,
        useFullParams: true,
        createUrl,
        onBeforeCreate: this.onBeforeCreateBySource,
        commentUrl,
        onBeforeGetComment: this.onBeforeGetCommentBySource,
        deleteUrl,
        onBeforeDelete: this.onBeforeDeleteBySource,
        updataUrl: updataUrl,
        onBeforeUpdate: this.onBeforeUpdateBySource,
        getCustomSearchConditions: this.handleCustomSearchConditions,
        //@ts-ignore
        renderExtraConent: this.renderExtraConent,
        // 子评论
        queryChildrenUrl,
        queryChildrenParams: { itemFormId, filters: this.filters, ...commonParams },
        // 直接子评论
        commentCommentedUrl,
        commentCommentedParams: { itemFormId, module: moduleData.ebuilderformModule },
        // 统计
        statisticUrl,
        onBeforeSelectCommentCountAdvancedSearch: this.onBeforeSelectCommentCountAdvancedSearchBySource,
        // 更新转任务数量
        updateTransmitCountUrl,
        onBeforeUpdateTransmitCount: this.onBeforeUpdateTransmitCountBySource,
        fetchData: this.fetchDataBySource,
        renderCustomMoreContent: this.renderCustomMoreContent
      }
    }
    return {}
  }

  /** 自定义接口请求 */
  fetchDataBySource = (options: RequestOptionConfig) => {
    const commonParams = this.commonParams
    const { url } = options
    if (url === updateTransmitCountUrl) { // 更新转任务数量
      options.method = 'POST'
      options.data = options.params
      delete options.params
    }
    if (url === commentCommentedUrl) {
      options.method = 'POST'
      const parentKey = 'comment.parent.id'
      const parentId = options.params?.[parentKey]
      options.data = {
        ...options.params,
        ...commonParams,
        comment: {
          parent: { id: parentId }
        }
      }
      delete options.data?.[parentKey]
      delete options.params
    }
    return request(options)
  }

  deleteQueryKeys = ['targetId', 'commentFilter', 'orderWay']

  selectCommentCountAdvancedSearchBySource = async (params?: AnyObj, resolve?: (v: any) => void) => {
    const { customSearchConditions } = this.state
    const { config } = this.props.ebProps || {}
    const { itemFormId, sortFields } = config || {}

    const { commentFilter } = params || {}
    const originSearchConditions = this.handleCustomSearch(commentFilter)
    const searchConditions = [...customSearchConditions, ...originSearchConditions]

    const { relatedDataSource } = config || {}
    const { appId, id: objId } = relatedDataSource || {}
    const commonParams = { // eb评论接口需要appId和objId参数
      appId,
      otherParam: {
        objId
      }
    }
    const data: any = {
      ...params,
      ...commonParams,
      module: moduleData.ebuilderformModule,
      itemFormId: itemFormId,
      filters: this.filters,
      commentConditionResultParam: {
        sorts: sortFields || [],
        conditions: searchConditions
      },

    }
    // 去除数据源模式不需要的参数
    this.deleteQueryKeys.forEach(key => {
      delete data[key]
    })
    resolve?.(data)
  }

  onBeforeSelectCommentCountAdvancedSearchBySource = (params?: AnyObj) => {
    return new Promise((resolve) => {
      this.selectCommentCountAdvancedSearchBySource(params, resolve)
    })
  }

  updateTransmitCountBySource = async (params?: AnyObj, resolve?: (v: any) => void) => {
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const data: any = {
      comment: {
        id: params?.commentId,
      },
      itemFormId: itemFormId
    }
    resolve?.(data)
  }

  onBeforeUpdateTransmitCountBySource = (params?: AnyObj) => {
    return new Promise((resolve) => {
      this.updateTransmitCountBySource(params, resolve)
    })
  }

  onReplyCustomFieldsMount = (commentData?: AnyObj, getCustomFieldsDatas?: any) => {
    this.setState((state) => {
      Object.assign(state.replyCustomFormDatasTools, {
        [commentData?.id]: { getCustomFieldsDatas }
      })
      return { replyCustomFormDatasTools: state.replyCustomFormDatasTools }
    })
  }

  clearReplyCustomFormDatas = (commentData: AnyObj) => {
    this.setState((state) => {
      delete state.replyCustomFormDatasTools[commentData.id]
      return { replyCustomFormDatasTools: state.replyCustomFormDatasTools }
    })
  }

  onEditCustomFieldsMount = (commentData?: AnyObj, getCustomFieldsDatas?: any) => {
    this.setState((state) => {
      Object.assign(state.editCustomFormDatasTools, {
        [commentData?.id]: { getCustomFieldsDatas }
      })
      return { editCustomFormDatasTools: state.editCustomFormDatasTools }
    })
  }

  clearEditCustomFormDatas = (commentData: AnyObj) => {
    this.setState((state) => {
      delete state.editCustomFormDatasTools[commentData.id]
      return { editCustomFormDatasTools: state.editCustomFormDatasTools }
    })
  }

  /** 是否有自定义字段 */
  get hasCustomFields() {
    const { commentForm } = this.state
    const { commentFormFieldList = [] } = commentForm || {}
    const fields = commentFormFieldList.filter((field: any) => !commentCommonFields.includes(field.dataKey))
    return !!fields.length
  }

  /** 是否显示自定义字段 */
  get showCustomFields() {
    const { showCustomField } = this.props
    return this.hasCustomFields && showCustomField
  }

  /** 新增评论 */
  addCustomFieldsRef = createRef<any>()

  /** 数据源模式，评论输入框下渲染自定义字段 */
  renderExtraConent = (data: ReplyOption, type: 'reply' | 'edit') => {
    const isReply = !!data?.parent || type === 'reply'
    const { commentForm } = this.state
    const { config } = this.props.ebProps || {}
    // 默认为新增评论
    let props: any = {
      ref: this.addCustomFieldsRef
    }
    /*if (type === 'reply') {
      props = {
        unMount: this.clearReplyCustomFormDatas,
        onMount: this.onReplyCustomFieldsMount,
      }
    } else */if (type === 'edit') {
      props = {
        unMount: this.clearEditCustomFormDatas,
        onMount: this.onEditCustomFieldsMount,
      }
    }
    if (!isReply && this.hasCustomFields) {
      return <CustomFields weId={`${this.props.weId || ''}_395rqq`}
        config={config}
        comConfig={{
          type,
          data
        }}
        commentForm={commentForm}
        {...props}
      />
    }
    return <></>
  }

  renderCustomFields = (comConfig?: CustomFieldsComConfig) => {
    const { commentForm } = this.state
    const { config } = this.props.ebProps || {}
    return <CustomFields weId={`${this.props.weId || ''}_395rqq`}
      config={config}
      comConfig={comConfig}
      commentForm={commentForm}
    />
  }

  getDataKeyValueObj = (params: AnyObj = {}, replyData?: ReplyOption, isUpdate?: boolean) => {
    const { attachments, attachmentsList, client, comment } = params
    const { content, originContent, imAtContent, longitude, latitude, module, privy, address, rootId, targetId } = comment || {}
    const parent = comment?.parent || replyData?.parent
    const result: AnyObj = isUpdate ? { // 评论更新
      'comment_origin_content': originContent, // 评论内容
      'comment_plain_content': imAtContent, // 纯文本评论
      'comment_parent': parent?.id, // 上级评论
      'comment_privy': privy ? '1' : '0', // 是否私评
    } : {
      'comment_origin_content': originContent, // 评论内容
      'comment_plain_content': imAtContent, // 纯文本评论
      'comment_parent': parent?.id, // 上级评论
      'comment_root_id': rootId, // 根评论
      'comment_privy': privy ? '1' : '0', // 是否私评
      'comment_module': moduleData.ebuilderformModule, // 所属模块
      'comment_longitude': longitude, // 经度
      'comment_latitude': latitude, // 纬度
      'comment_address': address, // 评论位置
      'comment_client': client, // 客户端
    }
    const { commentForm } = this.state
    const { commentFormFieldList } = commentForm || {}
    // 过滤评论表中不存在的字段
    Object.keys(result).forEach(key => {
      const exsit = commentFormFieldList?.find((field: any) => field.dataKey === key)
      if (!exsit) {
        delete result[key]
      }
      if (result[key] === undefined) { // 过滤没有值的字段
        delete result[key]
      }
    })
    if (replyData) { // 回复、编辑评论时，不需要选择target
      Object.assign(result, {
        'comment_target_id': targetId, // 事项id
      })
    }
    return result
  }

  getDataDetailItem = (id: string, value: any, type?: 'dataKey' | 'fieldId') => {
    const { commentForm } = this.state
    let field: any = {}
    if (!type || type === 'dataKey') {
      field = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === id)
    } else if (type === 'fieldId') {
      field = commentForm?.commentFormFieldList?.find((field: any) => field.id === id)
    }
    const { formId, id: fieldId } = field || {}
    let result: AnyObj = {
      formId,
      formField: {
        id: fieldId,
      },
    }
    if (typeof value === 'string') {
      result = {
        ...result,
        content: value,
        dataText: { content: value },
      }
    } else {
      result = {
        ...result,
        ...value
      }
    }
    return result
  }

  // 处理自定义表单字段
  handleExtraFormData = (customFormDatas: any) => {
    const { commentForm } = this.state
    const commonFiledIds = commentForm?.commentFormFieldList
      ?.filter((field: any) => !commentCommonFields.includes(field.dataKey))
      ?.reduce((acc: any, field: any) => {
        if (field.fields) {
          acc.push(...field.fields.map((f: any) => f.id))
        } else {
          acc.push(field.id)
        }
        return acc
      }, [])
    const { dataDetails, form } = customFormDatas || {}
    if (!dataDetails) {
      return []
    }
    const extraFormData = commonFiledIds.map((fieldId: string) => {
      const detail = dataDetails?.find((d: any) => d.formField.id === fieldId)
      return {
        formId: form.id,
        formField: { id: fieldId },
        ...detail,
      }
    })
    return extraFormData
  }

  createCommentBySource = async (params?: AnyObj, replyData?: ReplyOption, fullParams?: AnyObj, resolve?: (v: any) => void, reject?: (v: any) => void) => {
    const { commentForm, replyCustomFormDatasTools } = this.state
    const commentDataObj = this.getDataKeyValueObj(fullParams, replyData)
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const contentField = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_origin_content')
    const { form } = contentField || {}
    const dataDetails = Object.entries(commentDataObj).map(([key, value]) => {
      return this.getDataDetailItem(key, value)
    })
    const addCustomFormDatas = this.addCustomFieldsRef.current?.getCustomFieldsDatas?.()
    const replyCustomFormDatas = replyCustomFormDatasTools[replyData?.id]?.getCustomFieldsDatas?.()
    const customFormDatas = (replyData ? replyCustomFormDatas : addCustomFormDatas) || {}
    const extraFormData = this.handleExtraFormData(customFormDatas)
    const data = {
      ...fullParams,
      comment: {
        ...fullParams?.comment,
        targetId: undefined,
        formData: {
          module: moduleData.ebuilderformModule,
          form,
          formLayout: { id: '-1' },
          dataDetails: [...dataDetails, ...extraFormData]
        },
      },
      itemFormId: itemFormId
    }
    const dataResolve = (v: any) => {
      resolve?.(v)
      // 清除文本框下当前自定义字段数据
      this.addCustomFieldsRef.current?.resetFormData?.()
    }
    if (replyData) { // 是回复
      dataResolve?.(data)
    } else {
      this.targetPromiseRef = {
        data,
        resolve: dataResolve,
        reject
      }
      this.onOpenTargetDialog()
    }
  }

  onBeforeCreateBySource = (params?: AnyObj, replyData?: ReplyOption, fullParams?: AnyObj) => {
    return new Promise((resolve, reject) => {
      this.createCommentBySource(params, replyData, fullParams, resolve, reject)
    })
  }

  /** 获取过滤条件 */
  get filters() {
    const { outerFilter } = this.state
    const { config, page } = this.props.ebProps || {}
    const { filterCondition } = config || {}
    const { filter, isOverrideFilter } = outerFilter || {}
    const filterDatas = handleCommentFilterDatas(outerFilter || {}, page) || []
    let filters = filterCondition?.filter || {}
    const filtersDatas = handleCommentFilterDatas(filterCondition || {}, page) || []
    if (filter?.datas) {
      if (isOverrideFilter) { // 外部搜索条件覆盖
        filters = {
          ...filter,
          datas: filterDatas
        }
      } else {
        filters = {
          ...filter,
          datas: [
            ...filterDatas,
            ...filtersDatas
          ]
        }
      }
    } else {
      filters = {
        ...filters,
        datas: filtersDatas
      }
    }
    return filters
  }

  /** 右上角时间排序 */
  handleCommentFilter = (commentFilter?: string) => {
    if (commentFilter) {
      const { sortId } = this.state
      if (sortId === 'empty' || sortId === '') {
        return []
      }
      const filter = isJSONString(commentFilter) ? JSON.parse(commentFilter) : commentFilter
      const { conditions } = filter
      const sortFields = conditions?.map((item: any) => {
        const { id, orderWay } = item
        if (id === 'ADD_TIME') {
          return {
            tableAlias: 'c',
            field: 'create_time',
            ascending: sortId === 'asc'
          }
        }
      })
      return sortFields
    }
    return []
  }

  getCommentBySource = async (params?: AnyObj, resolve?: (v: any) => void) => {
    const { customSearchConditions, commentForm } = this.state
    const { config } = this.props.ebProps || {}
    const { itemFormId, sortFields: customSortFields } = config || {}
    if (!commentForm) { // filters需要根据表单字段更新
      await this.getCommentFormFields()
    }
    const { commentFilter } = params || {}
    const originSortFields = this.handleCommentFilter(commentFilter)
    const sortFields = [...originSortFields, ...customSortFields]
    const data: any = {
      ...params,
      module: moduleData.ebuilderformModule,
      itemFormId: itemFormId,
      filters: this.filters,
      commentConditionResultParam: {
        sorts: sortFields || [],
        conditions: customSearchConditions
      }
    }
    // 去除数据源模式不需要的参数
    this.deleteQueryKeys.forEach(key => {
      delete data[key]
    })
    resolve?.(data)
  }

  onBeforeGetCommentBySource = (params?: AnyObj) => {
    return new Promise((resolve) => {
      this.getCommentBySource(params, resolve)
    })
  }

  deleteCommentBySource = async (params?: AnyObj, commentData?: ReplyOption, resolve?: (v: any) => void) => {
    const { commentForm } = this.state
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const contentField = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_origin_content')
    const { form, formId, id: fieldId } = contentField || {}
    const data: any = {
      ...params,
      comment: {
        ...params?.comment,
        module: moduleData.ebuilderformModule,
        formData: {
          module: moduleData.ebuilderformModule,
          form,
          formLayout: { id: '-1' },
          dataDetails: [
            {
              formId,
              formField: {
                id: fieldId,
              },
            }
          ]
        }
      },
      itemFormId: itemFormId
    }
    resolve?.(data)
  }

  onBeforeDeleteBySource = (params?: AnyObj, commentData?: ReplyOption) => {
    return new Promise((resolve) => {
      this.deleteCommentBySource(params, commentData, resolve)
    })
  }

  updateCommentBySource = async (params?: AnyObj, fullParams?: AnyObj, replyData?: ReplyOption, resolve?: (v: any) => void) => {
    const { commentForm, editCustomFormDatasTools } = this.state
    const commentDataObj = this.getDataKeyValueObj(fullParams, replyData, true)
    const { id } = fullParams || {}
    const { config } = this.props.ebProps || {}
    const { itemFormId } = config || {}
    const contentField = commentForm?.commentFormFieldList?.find((field: any) => field.dataKey === 'comment_origin_content')
    const { form, formId, id: fieldId } = contentField || {}
    const dataDetails = Object.entries(commentDataObj).map(([key, value]) => {
      return this.getDataDetailItem(key, value)
    })
    const editCustomFormDatas = editCustomFormDatasTools[id]?.getCustomFieldsDatas?.()
    const extraFormData = this.handleExtraFormData(editCustomFormDatas)
    const data = {
      ...fullParams,
      comment: {
        ...fullParams?.comment,
        module: moduleData.ebuilderformModule,
        targetId: undefined,
        formData: {
          id,
          module: moduleData.ebuilderformModule,
          form,
          formLayout: { id: '-1' },
          dataDetails: [...dataDetails, ...extraFormData]
        },
      },
      itemFormId: itemFormId
    }
    resolve?.(data)
  }

  onBeforeUpdateBySource = (params?: AnyObj, commentData?: ReplyOption, fullParams?: AnyObj) => {
    return new Promise((resolve) => {
      this.updateCommentBySource(params, fullParams, commentData, resolve)
    })
  }

  refreshComment = (filter?: any) => {
    const getComment = this.commentRef.current?.getComment
    this.setState({
      outerFilter: filter || {}
    }, () => {
      getComment?.({}, false)
    })
  }

  onOpenTargetDialog = () => {
    this.setState({
      targetDialogVisible: true
    })
  }

  onTargetDialogClose = () => {
    this.targetPromiseRef.reject?.()
    this.setState({
      targetDialogVisible: false
    })
  }

  /** 一般模式下,comment内容渲染自定义字段 */
  renderCustomMoreContent = (data: ReplyOption) => {
    const isReply = !!data?.parent
    if (!isReply && this.showCustomFields) {
      return this.renderCustomFields({ readOnly: true, data, type: 'show' })
    }
    return <></>
  }

  render() {
    const { editVisible, targetDialogVisible, commentForm, commentKey } = this.state;
    const { createParams, updateParams } = this.store;
    const { isDesign, ebProps, className, ...resProps } = this.props;

    const cls = classnames({
      [`${prefixCls}-pc`]: true,
      [`${prefixCls}-pc-design`]: isDesign,
      [`${prefixCls}-pc-advanced`]: this.isAdvanced
    });

    if (isDesign) {
      resProps.textVisible = editVisible;
    }

    const { paginationType, componentShowType } = resProps;

    const containerCls = classnames({
      [`${prefixCls}-pc-container`]: true,
      [`${prefixCls}-pc-container-part`]: paginationType === "part",
      [`${prefixCls}-pc-container-input-only`]: componentShowType === 'input',
      [`${prefixCls}-pc-container-list-only`]: componentShowType === 'list',
    })

    const content = (
      <div className={containerCls}>
        <TargetData weId={`${this.props.weId || ''}_72cher`}
          config={ebProps?.config}
          dialogProps={
            {
              visible: targetDialogVisible,
              onClose: this.onTargetDialogClose,
            }
          }
          promiseRef={this.targetPromiseRef}
          commentForm={commentForm}
        />
        {/*@ts-ignore*/}
        <Comment
          weId={`${this.props.weId || ''}_mukcrz`}
          key={commentKey}
          onEdit={this.handleEditVisibleChange}
          onAfterGetComment={this.handleAfterGetComment}
          onAfterCreate={this.handleAfterCreate}
          onAfterDelete={this.handleAfterDelete}
          className={cls}
          customRenderEmpty={this.renderEmpty}
          onEditSubmitted={this.handleEditSubmitted}
          renderItemHeaderInfo={this.renderItemHeaderInfo}
          ref={this.commentRef}
          createParams={createParams}
          updataParams={updateParams}
          searchInitDatas={searchInitDatas}
          customSearchConfig={customSearchConfig}
          onStatisticCancel={this.handleStatisticCancel}
          renderEditOptions={this.renderEditOptions}
          listProps={this.listProps}
          searchAdvancedPanelProps={this.searchAdvancedPanelProps}
          {...resProps as CommentProps}
          {...this.getDataSourceProps()}
          {...this.getDisplayProps()}
        />
      </div>
    )

    return (
      <CommentProvider
        weId={`${this.props.weId || ''}_dcjqqj`}
        value={this.contextValue}
      >
        {content}
      </CommentProvider>
    )
  }
}