import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { ComBtnDataType, CommentSortBy } from "../../types";
import { classnames, getLabel } from "@weapp/utils";
import IconPlus from "../../../../ui-plus/icon-plus";
import { commentClsPrefix } from "../../../../constants";
import { checkModFunc } from "../../utils";
import { CommentHeaderProps } from "../type";

const prefixCls = commentClsPrefix;

function polyfill() {
  const dom = document.querySelector(".ebcom-tab");
  const className = dom?.className;
  dom && (dom.className = `${className} ebcoms-newtab-tab-top`)
}

const CommentHeader:FC <CommentHeaderProps> = (props) => {

  const { config, onDataChange } = props;

  const [sort, setSort] = useState<CommentSortBy>("desc");
  const [activeKey, setActiveKey] = useState("");

  useEffect(() => {
    // polyfill();
  }, [])

  const optionData: ComBtnDataType[] = useMemo(() => [{
    id: "hasSearch",
    icon: 'Icon-search',
    title: getLabel("223918", "搜索"),
  }, {
    id: "hasSort",
    icon: 'Icon-Reverse-order',
    title: getLabel("223919", "倒序"),
    isSelected: sort === "asc",
    selectIcon: "Icon-positive-sequence",
    selectTitle: getLabel("223920", "正序")
  }, {
    id: "hasEchart",
    icon: 'Icon-performance-appraisal-o',
    title: getLabel("223921", "筛选"),
  }], [sort])

  const handleClick = useCallback((e: React.MouseEvent<HTMLSpanElement, MouseEvent>, data?: ComBtnDataType) => {
    const { id, isSelected } = data!;
    (data as any) = {
      ...data,
      isSelected: !isSelected
    }
    switch(id) {
      case "hasSort": {
        const sort: CommentSortBy = !isSelected ? "asc" : "desc";
        setSort(sort);
        break;
      }
      default: {
        const activeId = activeKey !== id ? id : "";
        setActiveKey(activeId);
      }
    }
    onDataChange?.(id!, data!);
  }, [activeKey, onDataChange])

  const list = optionData?.map((item, index) => {
    const { id, icon, title, selectIcon, selectTitle, isSelected } = item;
    const hasIcon = config?.[id];
    const { moduleValue } = config;
    const checkShow = checkModFunc[id];
    if (!hasIcon || !checkShow(moduleValue!)) {
      return null;
    }
    const iconName = isSelected ? (selectIcon || icon) : icon;
    const titleContent = isSelected ? (selectTitle || title) : title;

    const cls = classnames({
      [`${prefixCls}-header-active`]: activeKey === id,
    })

    return (
      <IconPlus
        weId={`${props.weId || ''}_s7396u@${index}`}
        key={id}
        name={iconName}
        className={cls}
        data={item}
        title={titleContent}
        onClick={handleClick}
      />
    )
  })

  return (
    <div
      className={`${prefixCls}-header`}
    >
      {list}
    </div>
  )
}

export default CommentHeader