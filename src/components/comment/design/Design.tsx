import { createRef, PureComponent, ReactNode } from 'react';
import { DesignOptions, IComData } from '@weapp/designer';
import ebMiddleware from '../../../eb-middleware';
import { classnames, classUseMemo, eventEmitter, getLabel } from '@weapp/utils';
import getCommentProps from '../props/commentProps';
import { commentDefConfig } from '../constant/defConfig';
import { handleBeforeCreate } from '../utils/options';
import { CommentDesignProps } from '../types';
import MComment from '../components/MComment';
import Comment from '../components/Comment';
import { appName, commentClsPrefix } from '../../../constants';
import { CommentTitle } from '../index';
import CommentStore from '../components/Store';
import design from '../core/design';
import { customOperations } from '../config/title-btns/customOperations';
import { getDisplayConfig, isDescendantOrSelf, handleDataOptions, handleCompeleteCardLayout, handleGridToText } from '../utils';
import { AnyObj, CorsComponent, ReplyOption } from '@weapp/ui';
import { CommentEvents } from '../constant';
import ReactDOM from 'react-dom';
import pluginStore from '../components/pluginStore';
import { runInAction } from 'mobx';
import createPlugin from '../plugin/commentPlugin';
import { toggleSettingBindType } from '../api/data';
import { CustomFields } from '../config/items/displaySetting/constants';
import { handleCommentIdInfo } from '../utils/getApiDatas';

const prefixCls = commentClsPrefix;

interface CommentDesignState {
  activeArea: string
  slots: any;
}
@design
export class CommentDesign extends PureComponent<CommentDesignProps, CommentDesignState> {

  static defaultOpts?: DesignOptions = {
    onBeforeCreate: handleBeforeCreate, // 生成id迁移至didMount，否则选项卡复制会有问题
    layoutSizes: {
      //@ts-ignore
      gl: {
        // 宽度12列，默认4列
        w: 6,
        h: 36,
      }
    },
    // @ts-ignore
    customOperations,
    mask: false,
    // design视图显示底部footer按钮
    operationBtns: true,
    onBeforeCopyCom: (com: IComData) => { // 复制评论，删除targetId，在onBeforeSave重新生成
      if (com.config.targetId) {
        com.config.targetId = undefined
      }
      return com
    },
    onBeforeSave: (com, store) => {
      return new Promise(async (resolve) => {
        // 正式绑定评论物理表
        const { commentForm_Id, commentForm = {}, targetId } = com.config || {}
        if (!targetId) {
          resolve({ error: getLabel('315220', '生成评论id失败') })
        }
        if (commentForm.id || commentForm_Id) {
          const res = await toggleSettingBindType({ id: commentForm_Id || commentForm.id })
          if (res.code === 200) {
            resolve(true)
          } else {
            resolve({ error: res.msg })
          }
        } else {
          resolve(true)
        }
      })
    },
  }

  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: CommentDesignProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: commentDefConfig,
  };

  static renderTitle = (self: any, nextProps: CommentDesignProps) => {
    return (
      <CommentTitle
        {...nextProps}
        weId={`${nextProps.weId || ''}_21fhk6`}
      />
    );
  }

  store = new CommentStore();

  commentRef = createRef<any>();

  wrapRef = createRef<HTMLDivElement>();

  contextValue = {
    store: new CommentStore(),
  }
  private commentInputDom: any;
  private commentListDom: any;

  constructor(props: CommentDesignProps) {
    super(props);
    this.state = {
      activeArea: "",
      slots: {}
    }
    this.commentInputDom = null
    this.commentListDom = null
  }

  pluginName = 'CommentPlugin'

  componentDidUpdate(prevProps: Readonly<CommentDesignProps>): void {
    const { moduleValue, replyUnderComment } = this.props.config;
    const { moduleValue: prevModule, replyUnderComment: preReplyUnderComment } = prevProps.config;
    if (moduleValue !== prevModule) {
      // this.handleRefresh();
    }
    // 组件渲染导致dom丢失，需要重新获取
    if (replyUnderComment !== preReplyUnderComment) {
      this.resetPageContainer()
    }
  }

  componentDidMount() {
    // 禁用design内的评论点击事件
    // this.wrapRef.current?.addEventListener("click", this.handleDisableClick, true)

    // 监听评论组件容器的点击事件
    this.addPageContainerClickListener()

    const { events, id, pluginCenter, config, onConfigChange } = this.props as any
    const { setMobxState } = pluginStore
    runInAction(() => {
      // 存储pluginCenter
      setMobxState({ pluginCenter: pluginCenter })
    })

    //@ts-ignore
    events?.on("rightMenu.scroll", id, (active: string) => {
      this.setState({ activeArea: active }, () => this.toggleAreaStyle())
    });

    const commentPlugin = createPlugin(this.pluginName)

    pluginCenter.registerPlugin(commentPlugin)

    const plugin = pluginCenter.originalEnabledPlugins.find((plugin: any) => plugin.name === this.pluginName)
    plugin.eventNames = [`${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`]

    eventEmitter.on(appName, `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`, this.updateSlots)

    // 处理评论id
    handleCommentIdInfo(config, onConfigChange)
  }

  componentWillUnmount() {
    const { id, pluginCenter } = this.props as any
    this.desotryPageContainerListener()

    eventEmitter.off(appName, `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`, this.updateSlots)
    pluginCenter?.enabledPluginRecord?.[this.pluginName]?.clearSlotRefs?.()
  }

  updateSlots = (slots: any) => {
    //console.log('updateSlots design========')
    this.setState({ slots: { ...slots } })
  }

  desotryPageContainerListener = () => {
    const { page } = this.props
    const pageContainer = document.querySelector(`#ebpage_${page?.id}` || '')

    pageContainer?.removeEventListener('click', this.onPageContainerClick, false);
  }

  resetPageContainer = () => {
    this.desotryPageContainerListener()
    this.commentInputDom = null
    this.commentListDom = null
    this.addPageContainerClickListener()
  }

  addPageContainerClickListener = () => {
    const { page, client } = this.props
    const pageContainer = client === 'PC' ?
      document.querySelector(`#ebpage_${page?.id}` || '') :
      document.querySelector(`.ebpage#ebpage_${page?.id}`)

    pageContainer?.addEventListener("click", this.onPageContainerClick, false)
  }

  onPageContainerClick = (e: any) => {
    const { activeArea: _activeArea } = this.state
    const { events, id, store } = this.props;

    // 监听是当前组件才执行动作
    if (store?.selectedCom?.id === id) {

      let activeArea: string = 'all_set'

      this.getAreaDom()

      if (isDescendantOrSelf(this.commentInputDom, e.target)) {
        activeArea = 'input_set'
      }

      if (isDescendantOrSelf(this.commentListDom, e.target)) {
        activeArea = 'list_set'
      }

      if (!_activeArea) activeArea = 'all_set'

      this.setState({ activeArea: activeArea }, () => {
        this.toggleAreaStyle()
        events?.emit("subcomponent.click", { tabId: activeArea, groupId: '' }, 'option');
      })
    }
  }

  toggleAreaStyle = () => {
    const { activeArea } = this.state

    this.getAreaDom()

    switch (activeArea) {
      case 'all_set':
        this.commentInputDom?.classList.remove('active_area')
        this.commentListDom?.classList.remove('active_area')
        break;
      case 'input_set':
        if (this.commentInputDom?.classList.contains('active_area')) return

        this.commentInputDom?.classList.add('active_area')
        this.commentListDom?.classList.remove('active_area')
        break;
      case 'list_set':
        if (this.commentListDom?.classList.contains('active_area')) return

        this.commentInputDom?.classList.remove('active_area')
        this.commentListDom?.classList.add('active_area')
        break;
    }
  }

  get isAdvanced() {
    const { config } = this.props;
    return config.displaySetting?.displayMode === 'advanced'
  }

  getAreaDom = () => {
    if (!(this.commentInputDom && this.commentListDom)) {
      const { page, id } = this.props;
      const commentContainer = document.querySelector(`#${id}` || '')
      if (page?.client === 'PC') {
        this.commentInputDom = commentContainer?.querySelector?.('.ui-comment-edit')
        this.commentListDom = commentContainer?.querySelector?.('.ui-comment-list')
      } else if (page?.client === 'MOBILE') {
        this.commentInputDom = commentContainer?.querySelector?.('.weapp-ui-props-design-comment-m-edit')
        this.commentListDom = commentContainer?.querySelector?.('.ui-m-comment-content')
      }
    }
  }

  handleDisableClick = (e: Event) => {
    e.stopPropagation();
    e.preventDefault();
  }

  handleRefresh = () => {
    this.commentRef.current?.refresh?.();
  }

  renderItemFooterInfo = () => {
    const { config, page } = this.props;
    const { showSource, showTime } = getDisplayConfig(config, page)
    return (
      <>
        {showSource && (
          <i className="ui-comment-item-from">
            {getLabel("208508", "评论来源")}
          </i>
        )}
        {showTime && getLabel("208509", "评论时间")}
      </>
    )
  }

  renderFloorHeaderInfo = (data: ReplyOption) => {
    const { page } = this.props;
    const { client } = page || {};
    const { commentor, parent } = data || {}
    const { name } = commentor || {}
    const { name: parentName } = parent?.commentor || {}

    return (
      <>
        <span className={`${prefixCls}-design-floor-item-name`}>{name}</span>
        <span style={{ padding: '5px' }}>{getLabel('207018', '回复')}</span>
        <span className={`${prefixCls}-design-floor-item-name`}>{parentName}</span>
        {client === "PC" && <span style={{ padding: '5px' }}>{getLabel('266805', '回复时间')}</span>}
      </>
    )
  }

  renderFloorFooterInfo = (data: ReplyOption) => {
    const { page } = this.props;
    const { client } = page || {};

    return (
      <>
        {client === "MOBILE" && <span>{getLabel('266805', '回复时间')}</span>}
      </>
    )
  }

  customRenderCommentItemContent = (rowData: ReplyOption, content: ReactNode) => {
    const { config } = this.props;
    const { displaySetting } = this.props.config
    const { displayContentAdvanced } = displaySetting
    const advancedData: any = displayContentAdvanced || {}
    let cardLayout: AnyObj = {}
    let data: any = {}
    if (rowData.mock) {
      data = this.advancedData.find(item => item.id === rowData.id)
      const grid = handleGridToText(advancedData.cardLayout?.grid || [])
      cardLayout = {
        ...advancedData.cardLayout,
        grid
      }
    } else { // 真实数据处理
      data = this.handleAdvancedRowData(rowData)
      cardLayout = handleCompeleteCardLayout(advancedData.cardLayout!, advancedData.listField)
    }
    return <div className={`${prefixCls}-list-card`}>
      <div style={{ display: 'none' }}>{content}</div>
      <CorsComponent weId={`${this.props.weId || ''}_t4d28u`}
        app="@weapp/ebdcontainercoms"
        compName="ListCard"
        key={rowData.id}
        data={data} // 行数据
        rowIndex={rowData.id} // 行下标
        cardLayout={cardLayout || { grid: [] }} // config中保存的cardLayout
        plugin={(this.props as any).pluginCenter} // 实例化后的插件中心
        // pageNo={pageNo} // 当前页码，配置序号字段时需要
        // pageSize={pageSize} // 页码大小，配置序号字段时需要
        isDesign={true} // 是否是设计器
        extraConfig={{
          ...advancedData,
          //listField: [],
          dataset: config.relatedDataSource || {}
        }} // config配置，要有dataset、ListField
      />
    </div>
  }

  /** 处理真实数据 */
  handleAdvancedRowData = (rowData: ReplyOption) => {
    const { id: comId, page } = this.props
    const { dataDetails = [] } = rowData.itemFormData || {}
    const { dataDetails: formDataDetails = [] } = rowData.formData || {}
    const { itemFormData = {} } = rowData.otherParam || {}
    const fieldsObj: AnyObj = {}
    Object.entries(itemFormData).forEach(([key, value]: [string, any]) => {
      fieldsObj[key] = value
    })
    dataDetails.forEach((d: any) => {
      const { content, formField, dataOptions } = d
      fieldsObj[formField.id] = content || handleDataOptions(dataOptions)
    })
    formDataDetails.forEach((d: any) => {
      const { content, formField, dataText, dataOptions } = d
      fieldsObj[formField.id] = content || dataText?.content || handleDataOptions(dataOptions)
    })
    const result = {
      ...rowData,
      comId,
      ...fieldsObj,
      isEbMobile: page?.client === 'MOBILE'
    }
    return result
  }

  customRenderCommentItem = (rowData: ReplyOption, content: ReactNode, index: number, _: any) => {
    return content
  }

  get advancedData() {
    const { id, config } = this.props
    const { displaySetting } = config
    const { displayContentAdvanced } = displaySetting || {}
    const { listField } = displayContentAdvanced || {}
    let data1: AnyObj = {
      id: '3575224169782458815',
    }
    let data2: AnyObj = {
      id: '3575224125243058810',
    }
    listField?.forEach((field: any) => {
      const { id, text, mainPrimaryKey } = field
      if (!CustomFields[id] && !mainPrimaryKey) {
        data1[id] = text
        data2[id] = text
      }
    })
    Object.assign(data1, { comId: id })
    Object.assign(data2, { comId: id })
    return [data1, data2]
  }

  renderPortals = (ele: any, data: any, type: string) => {
    const { slots } = this.state
    const { id } = data
    const itemSlots = slots[id]?.filter((slot: any) => slot.type === type)
    let portals = itemSlots?.map((slot: any) => {
      const { id, ref } = slot
      const container = ref?.current || document.getElementById(id)
      if (container) {
        return ReactDOM.createPortal(ele, container)
      }
      return null
    })
    if (type === 'operate') {
      if (portals?.every((portal: any) => !portal)) {
        return ele
      }
    }
    return portals
  }

  customAvatar = (ele: any, data: any) => {
    return this.renderPortals(ele, data, 'avatar')
  }

  renderUserContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'user')
  }

  renderItemContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'content')
  }

  renderSourceContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'source')
  }

  renderTimeContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'time')
  }

  renderFooterContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'operate')
  }

  renderFooterBottomContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'operateBottomArea')
  }

  render() {
    const { page, config } = this.props;
    const { client, id: pageId } = page || {};
    const { targetId = "", hasEdit, printHideInput } = config;

    if ((hasEdit === 'hidden' || printHideInput) && this.commentInputDom) {
      this.commentInputDom = null
    }

    const menu = classUseMemo("menu", this, () => [{
      id: `main`,
      content: getLabel('57083', '评论'),
      title: getLabel('57083', '评论'),
      sort: 99,
    }], [])

    const commentProps = {
      ...getCommentProps(this.props, true),
      isDesign: true,
      ebProps: this.props,
      showFloorUser: false,
      showFloorTime: false,
      renderFloorHeaderInfo: this.renderFloorHeaderInfo,
      renderFloorFooterInfo: this.renderFloorFooterInfo
    };

    if (this.isAdvanced) {
      Object.assign(commentProps, {
        renderUserContent: this.renderUserContent,
        renderItemContent: this.renderItemContent,
        renderSourceContent: this.renderSourceContent,
        renderTimeContent: this.renderTimeContent,
        renderFooterContent: this.renderFooterContent,
        renderFooterBottomContent: this.renderFooterBottomContent,
        customAvatar: this.customAvatar,
        customRenderCommentItem: this.customRenderCommentItem,
        customRenderCommentItemContent: this.customRenderCommentItemContent
      })
    }

    const cls = classnames(`${prefixCls}-design`);

    let content = (
      <Comment
        weId={`${this.props.weId || ''}_391rk9`}
        className={cls}
        menu={menu}
        targetId={pageId}
        disableOptionCheck
        ref={this.commentRef}
        {...commentProps}
      />
    )

    if (client === "MOBILE") {
      content = (
        <MComment
          weId={`${this.props.weId || ''}_zgddc8`}
          className={cls}
          targetId={pageId}
          disableOptionCheck
          ref={this.commentRef}
          {...commentProps}
        />
      )
    }

    return (
      <>
        <div
          className={`${prefixCls}-design`}
          id={`comment_${targetId}`}
        >
          {/*<div*/}
          {/*  className={`${prefixCls}-design-mask`}*/}
          {/*  onClick={this.handleMaskClick}*/}
          {/*/>*/}
          <div
            className={`${prefixCls}-design-container`}
            ref={this.wrapRef}
          >
            {content}
          </div>
        </div>
      </>
    )
  }
};

export default ebMiddleware('CommentDesign', 'Design', 'Comment', CommentDesign);