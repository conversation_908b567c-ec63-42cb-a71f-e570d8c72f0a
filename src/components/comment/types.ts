import { BaseProps, FormDatas, ReplyOption } from "@weapp/ui";
import { ConfigProps, ViewProps, DesignProps } from "@weapp/ebdcoms";
import { IComConfig } from "@weapp/designer";
import { AnyObj, IconNames } from "@weapp/ui";
import modules from "./constant/modules";
import React from "react";
import CommentStore from "./components/Store";
import { StoreType } from "../../store";
import { ReplySettingConfigData } from "./utils/initData";

export type I18nContentType = {
  nameAlias: string;
  nameAliasLabelId?: string;
  targetId?: string;
}

export type CommentStoreType = StoreType<CommentStore>;

export type CommonConfigData = {
  id: string;
  /** 操作名称 */
  name: string;
  /** 自定义名称 */
  customName?: I18nContentType;
  /** 自定义提示信息 */
  customMessage?: I18nContentType;
  /** 按钮名称 */
  iconName: IconNames;
  /** 是否启用 */
  enable: boolean;
  /** 是否显示名称 */
  showName: boolean;
  /** 详细设置 */
  settingData?: any;
}

export type CommentOptionObj = {
  id: string;
  /** 操作名称 */
  title?: string;
  /** 操作详情 */
  titleInfo?: string;
  /** 按钮名称 */
  iconName?: IconNames;
  /** 点击事件 */
  onClick?: (data: ReplyOption) => void;
  /** 隐藏title */
  hideTitle?: boolean;
  /** 相关设置 */
  settingData?: string | AnyObj;
}

export type HasEditType = "show" | "hidden";
export type PageSizeType = "5" | "10"| "20"| "50";
export type ModulesType = typeof modules;
export type ModuleKey = ModulesType[number]["id"];

export type CommentFilterType = "hasSort" | "hasSearch" | "hasEchart";

/** 评论排序 -- 倒序 | 正序 */
export type CommentSortBy = "desc" | "asc";

export type EnableModuleType = {
  id: string;
  name: string;
}

export interface CommentConfigType extends Partial<IComConfig> {
  /** 评论id */
  targetId?: string;
  /** 评论标题 */
  commentTitle?: string;
  /** 文本框底部配置 */
  actionConfig?: CommonConfigData[];
  /** 回复操作框配置 */
  optionConfig?: CommonConfigData[];
  /** 按楼层回复，回复操作框配置 */
  replySetting?: ReplySettingConfigData;
  /** 是否展示文本框 */
  hasEdit?: HasEditType;
  /** 输入框默认展开 */
  showText?: boolean;
  /** 显示评论时间 */
  showTime?: boolean;
  /** 默认展示空状态 */
  showEmpty?: boolean;
  /** 显示评论来源 */
  showSource?: boolean;
  /** 输入框显示头像 */
  showEditAvatar?: boolean;
  /** 列表显示头像 */
  showListAvatar?: boolean;
  /** 每页条数 */
  pageSize?: PageSizeType;
  /** 打印时水平展示 */
  printHorizontal?: boolean;
  /** 打印时隐藏评论输入框 */
  printHideInput?: boolean;
  /** 显示总数 */
  showTotal?: boolean;
  /** 选择模块 */
  moduleValue?: ModuleKey;
  /** 启用排序 */
  hasSort?: boolean;
  /** 启用高级搜索 */
  hasSearch?: boolean;
  /** 启用统计 */
  hasEchart?: boolean;
  /** 开启日志 */
  hasBlog?: boolean;
  /** 启用模块 */
  enableModule?: EnableModuleType[];
  /** 组件类型 */
  componentShowType?: string
  /** 过滤设置 */
  filterCondition?: any;
  /** 排序设置 */
  sortFields?: any;
};

export type CommentConfigKeys = keyof CommentConfigType;

export interface CommentConfigProps<T = any> extends ConfigProps {
  weId: string;
  className?: string;
  /** 数据config */
  config: CommentConfigType;
  value?: T;
  /** dialog模式 */
  inDialog?: boolean;
  /** 修改config数据（单条数据）  */
  onChange: (value: T, act?: any) => void;
  /** 修改config数据（全部数据） */
  onConfigChange: (value?: FormDatas, act?: any) => void;
  /** 回传表格宽度 */
  onTableWidthChange?: (width: number) => void;
  /** 配置表格数据改变事件 */
  onCustomChange?: (value: CommonConfigData[]) => void;
  /** 表格标题 */
  tableTitle?: string;
  showSplitLine?: boolean;
}

export interface CommentViewProps extends ViewProps<CommentConfigType> {
  formProps?: AnyObj;
  onConfigChange?: (value: CommentConfigType, act?: any) => void;
}

export interface CommentDesignProps extends DesignProps<CommentConfigType> {
  formProps?: AnyObj;
  onConfigChange?: (value: CommentConfigType, act?: any) => void;
}

export type Action = {

} & AnyObj;

export type ComBtnDataType = {
  id: CommentFilterType;
  /** 按钮展示的icon名称 */
  icon: IconNames;
  /** 按钮名称 */
  title: string;
  /** 选中时的按钮名称 */
  selectTitle?: string;
  /** 选中时的icon名称 */
  selectIcon?: IconNames;
  /** 是否选中 */
  isSelected?: boolean;
}


export interface FilterProps extends BaseProps {
  weId: string;
  /** 组件config */
  config: CommentConfigType;
  /** 组件module */
  module: string;
  objectModule: ModuleKey;
  /** 评论id */
  targetId: string;
  /** 是否是移动端页面 */
  isMobile?: boolean;
  /** 弹出框关闭 */
  onCancel?: () => void;
}

export interface FloorActionConfig {
  /** 使用常用批示语（仅富文本有效） */
  usePhrase?: boolean;
  /** 是否使用地图定位功能 */
  usePosition?: boolean;
  /** 启用电子签名 */
  useSignature?: boolean;
  /** 启用手写签批 */
  useSignApprove?: boolean;
  /** 是否允许私密评论 */
  usePrivated?: boolean;
  /** 是否使用富文本 */
  useRichText?: boolean;
  /** 启用模块 */
  associativeProps?: EnableModuleType[];
  /** 文本框底部配置 */
  actionConfig?: CommonConfigData[];
}

export type FilterType = React.ComponentType<FilterProps>;