export const commentConstantDefProps = {

}

export const commentDefProps = {
  ...commentConstantDefProps,
  showHeader: false,
  paginationType: "more",
}

export const mCommentDefProps = {
  ...commentConstantDefProps,
  hasHeader: false,
  paginationType: "scroll",
}

export enum CommentEvents {
  /** 触发slots更新渲染 */
  COMMENT_UPDATE_RENDER_SLOTS = 'Comment.Update.Render.Slots',
  /** 触发commentForm更新 */
  COMMENT_UPDATE_COMMENTFORM = 'Comment.Update.CommentForm',
  /** 触发CustomFields状态 */
  COMMENT_UPDATE_CUSTOMFIELDS = 'Comment.Update.CustomFields',
}

export const dataKeyConfig = {
  'comment_target_id': {
    // 事项ID
    title: ''
  },
  'comment_parent': {

  }
}

export const moduleData = {
  ebuilderformObjectModule: 'ebuilder/form',
  ebuilderformModule: 'ebuilderform',
  bcwModule: 'bcw'
}

/** 评论表单固定字段 */
export const commentCommonFields = [
  'comment_origin_content', // 评论内容
  'comment_plain_content', // 纯文本评论
  'comment_target_id', // 事项id
  'comment_parent', // 上级评论
  'comment_root_id', // 根评论
  'comment_privy', // 是否私评
  'comment_module', // 所属模块
  'comment_longitude', // 经度
  'comment_latitude', // 纬度
  'comment_address', // 评论位置
  'comment_client', // 客户端
  'create_time', // 创建时间
  'creator', // 评论创建人
  'comment_transmit_count', // 转任务数量
]

export const ebuilderformGroupId = 'weaver-ebuilder-form-service'