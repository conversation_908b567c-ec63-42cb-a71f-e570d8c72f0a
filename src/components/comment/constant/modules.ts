import { getLabel } from "@weapp/utils";

const modules = [{
  id: "bcw",
  module: "share",
  urlModule: "bcw",
  content: get<PERSON>abel("223937", "公共"),
}, {
  id: "task",
  module: "task",
  urlModule: "task",
  content: get<PERSON>abel("223938", "任务"),
}, {
  id: "blog",
  module: "blog",
  urlModule: "blog",
  content: getLabel("223939", "日报"),
}, {
  id: "workreport",
  module: "workreport",
  urlModule: "plan",
  content: getLabel("223940", "报告"),
}, {
  id: "goal",
  module: "goal",
  urlModule: "goal",
  content: "OKR",
}, {
  id: "project",
  module: "mainline",
  urlModule: "project",
  content: getLabel("223941", "项目"),
}, {
  id: "calendar",
  module: "calendar",
  urlModule: "calendar",
  content: get<PERSON>abel("223942", "日程"),
}, {
  id: "document",
  module: "document",
  urlModule: "doc",
  content: get<PERSON>abel("223943", "文档"),
}, {
  id: "performance",
  module: "performance",
  urlModule: "performance",
  content: getLabel("223944", "绩效"),
}, {
  id: "workflow",
  module: "workflow",
  urlModule: "workflow/core",
  content: getLabel("223945", "流程"),
}, {
  id: "odoc",
  module: "odoc",
  urlModule: "odoc",
  content: getLabel("223946", "公文"),
}, {
  id: "formdatareport",
  module: "formdatareport",
  urlModule: "formdatareport",
  content: getLabel("223947", "表单"),
}, {
  id: "fna",
  module: "fna",
  urlModule: "fna",
  content: getLabel("223948", "电子费控"),
}, {
  id: "meeting",
  module: "meeting",
  urlModule: "meeting",
  content: getLabel("223949", "会议"),
}, {
  id: "marketactivity",
  module: "marketactivity",
  urlModule: "crm",
  content: getLabel("223950", "市场活动"),
}, {
  id: "customer",
  module: "customer",
  urlModule: "crm",
  content: getLabel("223951", "客户"),
}, {
  id: "saleChance",
  module: "saleChance",
  urlModule: "crm",
  content: getLabel("223952", "商机"),
}, {
  id: "contract",
  module: "contract",
  urlModule: "crm",
  content: getLabel("223953", "合同"),
}, {
  id: "orderform",
  module: "orderform",
  urlModule: "crm",
  content: getLabel("223954", "订单"),
}, {
  id: "price",
  module: "price",
  urlModule: "crm",
  content: getLabel("223955", "价格"),
}, {
  id: "capital",
  module: "capital",
  urlModule: "crm",
  content: getLabel("223956", "资金"),
}, {
  id: "quote",
  module: "quote",
  urlModule: "crm",
  content: getLabel("223957", "报价"),
}, {
  id: "production",
  module: "production",
  urlModule: "crm",
  content: getLabel("223958", "产品"),
}, {
  id: "competitor",
  module: "competitor",
  urlModule: "crm",
  content: getLabel("223959", "竞争对手"),
}] as const;

export default modules;