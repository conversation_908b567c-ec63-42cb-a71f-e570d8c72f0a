import { getLabel } from "@weapp/utils";
import { CommentConfigType } from "../types";
import topOperatBtns from "../config/title-btns/topBtns";

export const commentDefConfig: Partial<CommentConfigType> = {
  commentTitle: getLabel("207022", "评论"),
  title: getLabel("207022", "评论"),
  showEmpty: true,
  hasEdit: "show",
  showEditAvatar: true,
  showListAvatar: true,
  pageSize: "10",
  showSource: true,
  showTime: true,
  moduleValue: "bcw",
  titleEnabled: true,
  // title按钮默认值
  topOperatBtns,
  componentShowType: 'all',
  showFloorExpand: true,
  replyFloorExpand: true,
  displaySetting: {
    displayMode: 'normal'
  }
}