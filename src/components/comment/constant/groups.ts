import { getLabel } from '@weapp/utils';

const groups = [
  // {
  //   id: 'data',
  //   title: get<PERSON>abel("224418", "评论数据设置"),
  //   visible: true,
  //   custom: false,
  // },
  {
    id: 'showType',
    title: get<PERSON>abel("244825", "展示样式"),
    visible: true,
    custom: false,
    tabId: 'all_set'
  },
  {
    id: 'dataSource',
    title: get<PERSON>abel("312140", "关联数据源"),
    visible: true,
    custom: false,
    tabId: 'all_set'
  },
  {
    id: 'dataFilter',
    title: getLabel("312141", "数据过滤"),
    visible: true,
    custom: false,
    tabId: 'all_set'
  },
  {
    id: 'dataSort',
    title: getLabel("312142", "排序设置"),
    visible: true,
    custom: false,
    tabId: 'all_set'
  },
  {
    id: 'input',
    title: get<PERSON>abel("206978", "评论输入设置"),
    visible: true,
    custom: false,
    tabId: 'input_set'
  },
  {
    id: 'inputBox',
    title: get<PERSON>abel("206979", "输入框操作"),
    visible: true,
    custom: false,
    tabId: 'input_set'
  },
  {
    id: 'display',
    title: getLabel("206980", "评论展示设置"),
    visible: true,
    custom: false,
    tabId: 'list_set'
  },
  {
    id: 'operation',
    title: getLabel("206981", "评论操作"),
    visible: true,
    custom: false,
    tabId: 'list_set'
  },
  {
    id: 'pagination',
    title: getLabel("206982", "分页设置"),
    visible: true,
    custom: false,
    tabId: 'list_set'
  },
  {
    id: 'otherSetting',
    title: getLabel("244070", "其他"),
    visible: true,
    custom: false,
    tabId: 'list_set'
  },
]

export default groups