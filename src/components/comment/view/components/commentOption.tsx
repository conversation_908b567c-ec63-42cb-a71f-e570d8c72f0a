import { PureComponent } from 'react'
import { ViewProps } from '@weapp/ebdcoms';
import { Empty } from '@weapp/ui';
import { eventEmitter } from '@weapp/utils';
import { appName } from '../../../../constants';

class CommentOption extends PureComponent<any> {

    state = {
        content: null
    }

    componentDidMount() {
        const { ebProps, opts } = this.props
        const { id, events } = ebProps
        console.log(id, events)
        //events?.on(`update_comment_option`, id, this.updateContent)
        eventEmitter.on(appName, `update_comment_option`+opts.data.id, this.updateContent)
    }

    updateContent = (content: any) => {
        this.setState({ content })
        console.log(content)
    }

    render() {
        const { content } = this.state
        console.log('render')
        return (
            content
        )
    }
};
export default CommentOption