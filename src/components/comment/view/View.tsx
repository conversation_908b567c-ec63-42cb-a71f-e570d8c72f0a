import { createRef, isValidElement, PureComponent, ReactNode } from 'react';
import ebMiddleware from '../../../eb-middleware';
import { classnames, classUseMemo, eventEmitter, getLabel } from '@weapp/utils';
import getCommentProps from '../props/commentProps';
import { CommentViewProps } from '../types';
import MComment from '../components/MComment';
import Comment from '../components/Comment';
import { appName, commentClsPrefix } from '../../../constants';
import CommentStore from '../components/Store';
import { CommentTitle } from '..';
import design from '../core/design';
import { DesignOptions } from "@weapp/designer";
import { customOperations } from '../config/title-btns/customOperations';
import ReactDOM from 'react-dom';
import { AnyObj, CorsComponent, ReplyOption } from '@weapp/ui';
import createPlugin from '../plugin/commentPlugin';
import { CommentEvents } from '../constant';
import { handleCompeleteCardLayout, handleDataOptions } from '../utils';
import React from 'react';
import { executeActions } from '../../../utils/transform';

const prefixCls = commentClsPrefix;

interface CommentViewState {
  slots: any;
}

@design
export class CommentView extends PureComponent<CommentViewProps, CommentViewState> {

  static defaultOpts?: DesignOptions = {
    // @ts-ignore
    customOperations
  }

  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: CommentViewProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {

    },
  };

  static renderTitle = (self: any, nextProps: CommentViewProps) => {
    return (
      <CommentTitle
        {...nextProps}
        weId={`${nextProps.weId || ''}_21fhk6`}
      />
    );
  }

  contextValue = {
    store: new CommentStore(),
  }

  commentRef = createRef();

  getScrollContainer = () => {
    const { page } = this.props;
    const { id } = page || {};

    const scrollContainer = document.querySelector(`#ebpage_${id}.weapp-ebpv-container`)

    return scrollContainer;
  }

  constructor(props: CommentViewProps) {
    super(props);
    this.state = {
      slots: {}
    }
  }

  pluginName = 'CommentPlugin'

  componentDidMount() {
    const { events, id, pluginCenter } = this.props as any
    const commentPlugin = createPlugin(this.pluginName)
    pluginCenter.registerPlugin(commentPlugin)

    const plugin = pluginCenter.originalEnabledPlugins.find((plugin: any) => plugin.name === this.pluginName)
    plugin.eventNames = [`${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`]

    eventEmitter.on(appName, `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`, this.updateSlots)

    events.on('filter', id, this.refreshCompFilter)
  }

  refreshCompFilter = (refreshInfo: any) => {
    const filter = refreshInfo[0]
    this.refreshComment(filter)
  }

  refreshComment = (filter: any) => {
    const refreshComment = (this.commentRef.current as any)?.refreshComment
    refreshComment?.(filter)
  }

  componentWillUnmount() {
    const { events, id, pluginCenter } = this.props as any

    eventEmitter.off(appName, `${CommentEvents.COMMENT_UPDATE_RENDER_SLOTS}_${id}`, this.updateSlots)
    pluginCenter?.enabledPluginRecord?.[this.pluginName]?.clearSlotRefs?.()

    // 实现外部筛选
    events.off('filter', id, this.refreshCompFilter)
  }

  updateSlots = (slots: any) => {
    //console.log('updateSlots design========')
    this.setState({ slots: { ...slots } })
  }

  get isAdvanced() {
    const { config } = this.props;
    return config.displaySetting?.displayMode === 'advanced'
  }

  renderPortals = (ele: any, data: any, type: string) => {
    const { slots } = this.state
    const { id } = data
    const itemSlots = slots[id]?.filter((slot: any) => slot.type === type)
    let portals = itemSlots?.map((slot: any) => {
      const { id, ref, eventGroup } = slot
      const container = ref?.current || document.getElementById(id)
      if (container) {
        // 手动触发container的点击事件，兼容高级显示事件动作
        if (isValidElement(ele)) {
          const wrappedEle = React.cloneElement(ele as any, {
            onClick: (clickEvent) => {
              eventGroup?.forEach((et: any[]) => {
                et?.forEach((e) => {
                  e?.events && executeActions(clickEvent?.target, e?.events, this.props);
                })
              })
            }
          })
          return ReactDOM.createPortal(wrappedEle, container)
        }
        return ReactDOM.createPortal(ele, container)
      }
      return null
    })
    if (type === 'operate') {
      if (portals?.every((portal: any) => !portal)) {
        return ele
      }
    }
    return portals
  }

  customAvatar = (ele: any, data: any) => {
    return this.renderPortals(ele, data, 'avatar')
  }

  renderUserContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'user')
  }

  renderItemContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'content')
  }

  renderSourceContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'source')
  }

  renderTimeContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'time')
  }

  renderFooterContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'operate')
  }

  renderFooterBottomContent = (data: any, ele: any) => {
    return this.renderPortals(ele, data, 'operateBottomArea')
  }

  handleAdvancedRowData = (rowData: ReplyOption) => {
    const { id: comId, page } = this.props
    const { dataDetails = [] } = rowData.itemFormData || {}
    const { dataDetails: formDataDetails = [] } = rowData.formData || {}
    const { itemFormData = {} } = rowData.otherParam || {}
    const fieldsObj: AnyObj = {}
    Object.entries(itemFormData).forEach(([key, value]: [string, any]) => {
      fieldsObj[key] = value
    })
    dataDetails.forEach((d: any) => {
      const { content, formField, dataOptions } = d
      fieldsObj[formField.id] = content || handleDataOptions(dataOptions)
    })
    formDataDetails.forEach((d: any) => {
      const { content, formField, dataText, dataOptions } = d
      fieldsObj[formField.id] = content || dataText?.content || handleDataOptions(dataOptions)
    })
    const result = {
      ...rowData,
      comId,
      ...fieldsObj,
      isEbMobile: page?.client === 'MOBILE'
    }
    return result
  }

  customRenderCommentItemContent = (rowData: ReplyOption, content: ReactNode) => {
    const { config, pluginCenter, page } = this.props as any
    const { client } = page || {};
    const { displaySetting } = this.props.config
    const { displayContentAdvanced } = displaySetting
    const advancedData: any = displayContentAdvanced || {}
    const data = this.handleAdvancedRowData(rowData)
    const cardLayout = handleCompeleteCardLayout(advancedData.cardLayout!, advancedData.listField)
    return <div className={classnames(`${prefixCls}-list-card`, {
      [`${prefixCls}-m-list-card`]: client === "MOBILE"
    })}>
      <CorsComponent weId={`${this.props.weId || ''}_fnibxi`}
        app="@weapp/ebdcontainercoms"
        compName="ListCard"
        key={rowData.id}
        data={data} // 行数据
        rowIndex={rowData.id} // 行下标
        cardLayout={cardLayout || { grid: [] }} // config中保存的cardLayout
        plugin={pluginCenter} // 实例化后的插件中心
        // pageNo={pageNo} // 当前页码，配置序号字段时需要
        // pageSize={pageSize} // 页码大小，配置序号字段时需要
        //isDesign={true} // 是否是设计器
        isMobile
        extraConfig={{
          ...advancedData,
          //listField: [],
          dataset: config.relatedDataSource || {}
        }} // config配置，要有dataset、ListField
      />
      <div style={{ display: 'none' }}>{content}</div>
    </div>
  }

  customRenderCommentItem = (rowData: ReplyOption, content: ReactNode, index: number, _: any) => {
    return content
  }

  render() {
    const { config, page } = this.props;

    const { client, id } = page || {};

    const menu = classUseMemo("menu", this, () => [{
      id: `main`,
      content: getLabel('57083', '评论'),
      title: getLabel('57083', '评论'),
      sort: 99,
    }], [])

    const commentProps = {
      ...getCommentProps(this.props),
      ebProps: this.props,
    };

    if (this.isAdvanced) {
      Object.assign(commentProps, {
        renderUserContent: this.renderUserContent,
        renderItemContent: this.renderItemContent,
        renderSourceContent: this.renderSourceContent,
        renderTimeContent: this.renderTimeContent,
        renderFooterContent: this.renderFooterContent,
        renderFooterBottomContent: this.renderFooterBottomContent,
        customAvatar: this.customAvatar,
        customRenderCommentItem: this.customRenderCommentItem,
        customRenderCommentItemContent: this.customRenderCommentItemContent
      })
    }

    const cls = classnames(`${prefixCls}-view`);

    let content = (
      <Comment
        weId={`${this.props.weId || ''}_391rk9`}
        className={cls}
        menu={menu}
        targetId={id}
        hasBcwDelete
        ref={this.commentRef}
        {...commentProps}
      />
    )
    if (client === "MOBILE") {
      content = (
        <MComment
          weId={`${this.props.weId || ''}_zgddc8`}
          className={cls}
          targetId={id}
          hasBcwDelete
          ref={this.commentRef}
          getScrollContainer={this.getScrollContainer}
          {...commentProps}
        />
      )
    }

    return content;

  }
};

export default ebMiddleware('CommentView', 'View', 'Comment', CommentView, { needRouter: true });