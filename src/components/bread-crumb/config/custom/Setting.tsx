import React, { memo, useMemo, useEffect, useState, useCallback } from "react";
import { Dialog, Icon, Form, FormStoreType, FormStore, Button, AnyObj, FormDatas } from "@weapp/ui";
import { breadCrumbClsPrefix, LABEL_SPAN } from "../../../../constants";
import { CommonConfigCustomProps } from "../../../../types/common";
import { classnames, getLabel } from "@weapp/utils";
interface SettingProps extends CommonConfigCustomProps {
  value?: FormDatas;
  onChange?: (value: FormDatas, customParams?: AnyObj) => void;
  customParams?: AnyObj;
}
const Setting = memo(React.forwardRef<{}, SettingProps>(
  (props) => {
    const { onChange, customParams, value } = props;
    const [visible, updateVisible] = useState<boolean>(false);
    const [formStore, updateFormStore] = useState<FormStoreType | null>(null);

    useEffect(() => {
      const formStore = new FormStore();
      updateFormStore(formStore);
      formStore.initForm({
        data: {
          // openway: 'newPage',
          url: '',
        }, groups: [],
        items: {
          // openway: {
          //   itemType: 'RADIO',
          //   required: true,
          //   data: [
          //     { id: 'newPage', content: '新窗口' },
          //     { id: 'currentPage', content: '当前页' },
          //   ]
          // },
          url: {
            itemType: 'INPUT',
            required: true,
          }
        },
        layout: [
          // [ { id: 'openway', label: '打开方式', labelSpan: LABEL_SPAN, items: ['openway'], hide: false } ],
          [ { id: 'url', label: getLabel('213270','网页地址'), labelSpan: LABEL_SPAN, items: ['url'], hide: false } ],
        ]
      })
    }, []);

    useEffect(() => {
      formStore?.updateDatas(value);
    }, [value, formStore]);

    const showDialog = useCallback(() => {
      updateVisible(true);
    }, []);

    const closeDialog = useCallback(() => {
      updateVisible(false);
      formStore?.resetForm();
    }, [formStore]);

    const save = useCallback(() => {
      formStore?.validate().then((errors: any) => {
        if (!(errors && errors.errors && Object.keys(errors.errors).length > 0)) {
          const datas = formStore?.getFormDatas();
          onChange?.(datas, customParams);
          closeDialog();
        }
      })
    }, [formStore, onChange, closeDialog, customParams]);

    const footer = useMemo(() => ([
      <Button weId={`${props.weId || ''}_poa1lh_save`} type="primary" onClick={save}>保存</Button>,
      <Button weId={`${props.weId || ''}_poa1lh_cancel`} onClick={closeDialog}>取消</Button>,
    ]), [props.weId, closeDialog, save]);

    const cls = classnames(`${breadCrumbClsPrefix}-config-setting-address`, {
      "active": value && value?.url,
    })

    return <div className={cls}>
      <Icon weId={`${props.weId || ''}_re5knp`} name="Icon-Basic-settings-o" onClick={showDialog} />
      <Dialog
        weId={`${props.weId || ''}_vfxxr0`}
        visible={visible}
        title={"设置网页地址"}
        width={500}
        height={230}
        closable
        onClose={closeDialog}
        footer={footer}
      >
        { formStore && <Form weId={`${props.weId || ''}_o41fhc`} store={formStore} /> }
      </Dialog>
    </div>
  }
))
export default Setting;