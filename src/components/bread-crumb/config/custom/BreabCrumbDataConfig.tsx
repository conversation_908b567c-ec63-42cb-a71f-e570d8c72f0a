import React, { memo, ReactElement, ReactNode, useCallback, useEffect, useMemo, useState } from "react";
import { AnyObj, EditableTable, FormDatas, FormItemProps, utils, Icon } from "@weapp/ui";
import { CommonConfigCustomProps } from "../../../../types/common";
import Setting from "./Setting";
import { breadCrumbClsPrefix } from "../../../../constants";
import IconSelectionConfig from "../../../../components-share/icon-selection-config";
import { weappSDK } from "@weapp/utils";

const { getRandom } = utils;

const prefixCls = `${breadCrumbClsPrefix}-config-data`;
const triggerProps = { popupPlacement: 'bottomRight', popupClassName: `popupClassName ${prefixCls}-icon` };
const iconSelectionConfigId = 'icon';

const transHTML = (dom: ReactElement) => {
  return {
    id: getRandom(),
    ...dom?.props?.data,
  }
}

interface SingleBreabCrumbProps {
  data: AnyObj;
}

const SingleBreabCrumb = memo(React.forwardRef<{}, SingleBreabCrumbProps>(
  (props) => {
    const { data } = props;
    const { icon, name, id, url } = data || {};

    const openUrl = useCallback(() => {
      weappSDK.webOpen(url);
    }, [url]);

    return (
      <>
        {icon && <Icon weId={`_9ilc13_${id}`} size={"md"} name={icon} />}
        {name && (<span onClick={openUrl}>{name}</span>)}
      </>
    )
  }
))

const BreabCrumbDataConfig = memo(React.forwardRef<{}, CommonConfigCustomProps>(
  (props) => {
    /* props */
    const { onFormChange, config } = props;
    /* data */
    const [data, updateData] = useState([]);
    useEffect(() => {
      const result = config?.data?.map((da: any) => transHTML(da)) || [];
      updateData(result);
    }, []);

    /* function */

    const onChange = useCallback((data: any) => {
      updateData(data);
      const result = data?.map((da: { id: any; }) => {
        return (
          <SingleBreabCrumb weId={`${props.weId || ''}_0n3igi@${da.id}`} data={da} />
        )
      });
      onFormChange?.({
        data: result
      })
    }, [onFormChange, props.weId]);

    const onIconChange = useCallback((formData: FormDatas, customParams?: AnyObj) => {
      const result = data?.map((da: any) => {
        if (da?.id === customParams?.id) {
          return {
            ...da,
            [iconSelectionConfigId]: formData[iconSelectionConfigId],
          }
        }
        return da;
      }) as any;
      onChange(result);
    }, [data, onChange]);

    const onChangeAddress = useCallback((formDatas: FormDatas, customParams?: AnyObj) => {
      const { openway, url } = formDatas || {};
      const result = data?.map((da: any) => {
        if (da?.id === customParams?.id) {
          return {
            ...da,
            openway,
            url,
          }
        }
        return da;
      }) as any;
      onChange(result);
    }, [data, onChange]);

    /* config */
    const columns = useMemo(() => ([
      {
        title: '名称',
        dataIndex: 'name',
        comKey: 'name',
        width: '40%',
      },
      {
        title: '图标',
        dataIndex: 'icon',
        comKey: 'icon',
        width: '30%',
      },
      {
        title: '操作',
        dataIndex: 'operate',
        comKey: 'operate',
        width: '30%',
      },
    ]), []);

    const comProps = useMemo(() => ({
      name: { itemType: 'INPUT' },
      icon: {
        itemType: 'CUSTOM',
        customRender: (formItemInstance: ReactNode, content: any, rowData: any, data: any, comProps: any, pos: any) => {
          return (
            <IconSelectionConfig
              weId={`${props.weId || ''}_c1vuwe_${rowData?.id}`}
              iconSelectionConfigId={iconSelectionConfigId}
              iconSelectionConfigType="name"
              canClear={false}
              triggerProps={triggerProps}
              onChange={onIconChange}
              config={rowData}
              customParams={rowData}
            />
          )
        }
      },
      operate: {
        itemType: 'CUSTOM',
        customRender: (formItemInstance: ReactNode, content: any, rowData: any, data: any, comProps: any, pos: any) => {
          return <Setting
            weId={`${props.weId || ''}_k0xxwa`}
            onChange={onChangeAddress}
            value={rowData}
            customParams={rowData}
          />
        }
      },
    }), [props.weId, onIconChange, onChangeAddress]) as FormItemProps;

    const topTools = useMemo(() => ([
      {
        type: 'add',
        icon: 'Icon-add-to03',
        title: '新增',
      },
      {
        type: 'batchDelete',
        icon: 'Icon-Batch-delete',
        title: '批量删除',
      },
    ]), []);

    return (
      <EditableTable
        weId={`${props.weId || ''}_1nz0t1`}
        className={prefixCls}
        columns={columns}
        comProps={comProps}
        data={data}
        deleteConfirm
        topTools={topTools}
        onChange={onChange}
        size="small"
      />
    )
  }
))

export default BreabCrumbDataConfig;