
import { ListData, BaseProps, DialogProps, FormDatas, FormStoreType } from "@weapp/ui";
import { CommonConfigCustomProps } from "../../../../../types/common";
export interface NavItemConfigProps extends CommonConfigCustomProps {
}

export type Action = 'delete' | 'edit' | 'change';

export interface NavItemSingleProps extends BaseProps {
  weId?: any;
  data: ListData;
  doAction?: (key: Action, id?: string, data?: ListData) => void;
}

export interface ConfigDialogProps extends CommonConfigCustomProps, DialogProps {
  weId?: any;
  type?: 'edit' | 'add';
  onSure?: (data: FormDatas) => void;
}

export interface ActionProps extends CommonConfigCustomProps {
  formStore: FormStoreType;
}