import React, { useCallback, useContext, useMemo } from "react";
import { CorsComponent } from "@weapp/ui";
import { ActionProps } from "./type";
import { CommonConfigCustomProps } from "../../../../../types/common";
import { ConfigContext } from "./utils";
import { observer } from "mobx-react";

const Action = observer(React.forwardRef<{}, ActionProps>(
  (props) => {
    const { prefixCls, appid, pageId, store, onDatasetChange, clientType, layoutInfo, pageScope } = useContext<CommonConfigCustomProps>(ConfigContext);
    const { formStore } = props;
    const value = formStore?.getFormDatas()?.action;
    const dftConfig = useMemo(() => ([
      {
        eventId: 'CLICK',
        actionIds: ['NewPage'],
        disableDelete: true,
      },
    ]), []);
    const page = useMemo(() => ({
      id: pageId,
      module: pageScope,
      appid,
      client: clientType,
      datasetVals: layoutInfo?.datasetVals,
    }), [appid, clientType, layoutInfo?.datasetVals, pageId, pageScope]);

    const onEventSure = useCallback((value: any[]) => {
      formStore.updateDatas({action: value});
    }, [formStore]);

    return (<CorsComponent weId={`${props.weId || ''}_y18jba`}
      app="@weapp/ebdcoms"
      compName="EventsActionBtn"
      className={`${prefixCls}-action-btn`} // 非必填
      eventGroup={value?.eventGroup} // 必填，从属性的配置里获取到对应的事件动作配置
      store={store} // 基础设计器store
      page={page} // page对象
      dftConfig={dftConfig} // 事件动作默认配置
      onSure={onEventSure} // 事件弹窗确认回调 (val: any[]) => void
      onDatasetChange={onDatasetChange} // 数据集回调，props取即可
    />)
  }
))
export default Action;