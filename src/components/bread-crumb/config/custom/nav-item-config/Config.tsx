import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { Dialog, Button, Form, FormStore, FormStoreType, FormSwitchProps, AnyObj } from "@weapp/ui";
import { ConfigDialogProps } from "./type";
// import { getLocaleValue } from "../../../../../utils/transform";
import { ConfigContext } from "./utils";
import { CommonConfigCustomProps } from "../../../../../types/common";
import Action from "./Action";
import ebdcoms from "../../../../../utils/ebdcoms";

const { getLocaleValue } = ebdcoms.get();
const ConfigDialog = React.memo(React.forwardRef<any, ConfigDialogProps>(
  (props) => {
    const [store] = useState<FormStoreType>(new FormStore());
    const { type, visible, onClose, onSure: sure, value } = props;
    const { page, pageId, prefixCls } = useContext<CommonConfigCustomProps>(ConfigContext);

    const title = type === 'add' ? '新增菜单' : '编辑菜单';

    useEffect(() => {
      visible ? store.initForm({
        groups: [],
        layout: [
          [
            { id: 'content', label: '菜单名称', items: ['content'], labelSpan: 6, hide: false }
          ],
          [
            { id: 'action', label: '动作', items: ['action'], labelSpan: 6, hide: false }
          ],
        ],
        items: {
          content: {
            itemType: 'PLUGIN',
            pluginParams: {
              packageName: '@weapp/ebdcoms',
              compName: 'LocaleEx',
              pageId: pageId || page?.id,
            },
            required: true,
          },
          action: {
            itemType: 'CUSTOM',
          }
        },
        data: value,
      }) : store.clear();
    }, [value, pageId, page, store, visible]);

    const onSure = useCallback(() => {
      store.validate().then((errors: any) => {
        if (!(errors && errors?.errors && Object.keys(errors?.errors).length > 0)) {
          const datas = store.getFormDatas()
          const resultTitle = getLocaleValue(datas?.content);
          sure?.({
            content: resultTitle,
            action: datas?.action,
          })
        }
      })
    }, [store, sure]);

    const footer = useMemo(() => ([
      <Button weId={`${props.weId || ''}_9c2mv9`} key="sure" type="primary" onClick={onSure}>确定</Button>,
      <Button weId={`${props.weId || ''}_39c01j`} key="cancel" onClick={onClose}>取消</Button>,
    ]), [onClose, onSure, props.weId]);

    const customRenderFormSwitch = useCallback((id: string, props: FormSwitchProps) => {
      if (id === 'action') return <Action weId={`${props.weId || ''}_cvtics`} {...props} formStore={store} />
      return null;
    }, [store]);

    return (
      <Dialog weId={`${props.weId || ''}_rxka3i`}
        className={`${prefixCls}-config`}
        visible={visible}
        title={title}
        closable
        onClose={onClose}
        footer={footer}
        icon="Icon-e-builder"
        width={500}
      >
        <Form weId={`${props.weId || ''}_tdepv4`}
          store={store}
          customRenderFormSwitch={customRenderFormSwitch}
        />
      </Dialog>
    )
  }
))

export default ConfigDialog;
