import React, { useCallback, useMemo, useState } from "react";
import { List, ListData, Button, Icon, AnyObj, FormDatas, utils, BrowserValueType, ColorObject, DataType, DatePickerDateType, OptionsType, ScopeValue, SelectOptionData, UploadData } from "@weapp/ui";
import { breadCrumbClsPrefix } from "../../../../../constants";
import { Action, NavItemConfigProps } from "./type";
import NavItemSingle from "./Single";
import ConfigDialog from "./Config";
import { ConfigContext } from "./utils";
import { DataValueType } from "@weapp/ui/lib/components/checkbox/types";
import { Dayjs } from "dayjs";

const { getRandom } = utils;
/* 单独开发NavItem配置组件，适配BreabCrumb */
/**
  @weapp/ebdcoms  NavItemSetting 功能类似
  1.多了权限、子级；少了图标隐藏功能
  2.数据格式兼容复杂(NavItem 是树形数据) 
  3.NavItemSetting 内部结构相对复杂, 业务比较固定(适配导航栏)，强行开个钩子，维护复杂，代价相对较大
*/


const prefixCls = `${breadCrumbClsPrefix}-config-nav-item`;

const NavItemConfig = React.memo(React.forwardRef<any, NavItemConfigProps>(
  (props) => {
    const { value, onChange, page, appid, pageId, store, onDatasetChange, clientType, layoutInfo, pageScope, config } = props;

    // contextValue EB Config props集成下来的属性
    const contextValue = useMemo(() => (
      { pageId, page, prefixCls, appid, store, onDatasetChange, clientType, layoutInfo, pageScope, config }), 
    [appid, clientType, layoutInfo, onDatasetChange, page, pageId, pageScope, store, config]);

    const [dialogConfig, updateDialogConfig] = useState<AnyObj>({});

    const doAction = useCallback((key: Action, id?: string, data?: ListData) => {
      if (key === 'delete') onChange?.(value?.filter((da: { id: string | undefined; }) => da.id !== id));
      else if (key === 'edit') updateDialogConfig({ visible: true, type: 'edit', value: data });
      else if (key === 'change') onChange?.(value?.map((da: { id: string | undefined; }) => da.id === id ? { ...da, ...data } : da))
    }, [onChange, value]);

    const onAdd = useCallback(() => updateDialogConfig({ visible: true, type: 'add', value: {} }), []);
    const onClose = useCallback(() => updateDialogConfig({ visible: false }), []);
    const onSure = useCallback((data: FormDatas) => {
      const { type } = dialogConfig;
      if (type === 'add') {
        onChange?.([...value, {
          id: getRandom(),
          ...data,
        }])
      } else {
        const result = value?.map((val: any) => val.id === dialogConfig?.value?.id ? { ...val, ...data } : val)
        onChange?.(result)
      }
      updateDialogConfig({ visible: false });
    }, [dialogConfig, value, onChange]);

    const customRenderContent = useCallback((rowData: ListData, rowID: number) => {
      return (
        <NavItemSingle weId={`${props.weId || ''}_1vw2em`} key={rowID} data={rowData} doAction={doAction} prefixCls={prefixCls} />
      );
    }, [doAction, props.weId]);

    const onSortEnd = useCallback((data: ListData[]) => {
      onChange?.(data);
    }, [onChange]);

    return (
      <div className={prefixCls}>
        <ConfigContext.Provider value={contextValue} weId={`${props.weId || ''}_8xcj1i`}>
          <List weId={`${props.weId || ''}_m646rn`}
            data={value?.slice()}
            sortable
            direction="column"
            customRenderContent={customRenderContent}
            onSortEnd={onSortEnd}
            className={`${prefixCls}-list`}
          />
          <Button weId={`${props.weId || ''}_k76bjm`} className={`${prefixCls}-add-btn`} onClick={onAdd}>
            <Icon weId={`${props.weId || ''}_2sdepx`} name="Icon-add-to01" />
            <span>添加导航项</span>
          </Button>
          <ConfigDialog weId={`${props.weId || ''}_hxhse9`} prefixCls={prefixCls} {...dialogConfig} onClose={onClose} onSure={onSure} />
        </ConfigContext.Provider>
      </div>)
  }
))

export default NavItemConfig;