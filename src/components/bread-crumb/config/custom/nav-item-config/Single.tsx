import { AnyObj, Help, Icon } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import React, { useCallback, useContext } from "react";
import IconConfig from "../../../../../components-share/icon-config";
import { CommonConfigCustomProps } from "../../../../../types/common";
import { NavItemSingleProps } from "./type";
import { ConfigContext } from "./utils";

const NavItemSingle = React.memo(React.forwardRef<any, NavItemSingleProps>(
  (props) => {
    const { prefixCls, config } = useContext<CommonConfigCustomProps>(ConfigContext);
    const { data, doAction } = props;
    const onDelete = useCallback(() => {
      doAction?.('delete', data.id, data);
    }, [data, doAction]);

    const onEdit = useCallback(() => {
      doAction?.('edit', data.id, data);
    }, [data, doAction]);

    const onIconChange = useCallback((value: AnyObj) => {
      doAction?.('change', data.id, { ...data, ...value })
    }, [data, doAction]);

    return (
      <div key={data.id} className={`${prefixCls}-list-single`}>
        <div className={`${prefixCls}-list-single-content`}>
          {
            config?.showIcon && (
              <IconConfig
                weId={`${props.weId || ''}_oaitsi`}
                config={data}
                onFormChange={onIconChange}
                dataKey="icon"
              />
            )
          }
          <span>{data?.content}</span>
        </div>
        <div className={`${prefixCls}-list-single-action`}>
          <Help weId={`${props.weId || ''}_h59wx7`} placement="top" title={getLabel('207020','编辑')}>
            <span><Icon weId={`${props.weId || ''}_b55wuq`} name="Icon-rename" onClick={onEdit} /></span>
          </Help>
          <Help weId={`${props.weId || ''}_sguisn`} placement="top" title={getLabel('207017','删除')}>
            <span><Icon weId={`${props.weId || ''}_fejdai`} name="Icon-error01" onClick={onDelete} /></span>
          </Help>
        </div>
      </div>
    )
  }
))

export default NavItemSingle;
