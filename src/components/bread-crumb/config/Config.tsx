import { LABEL_SPAN, LABEL_SPAN_LG, uiPropsDesignClsPrefix } from "../../../constants";
import NavItemConfig from "./custom/nav-item-config";
// import BreabCrumbDataConfig from "./custom/BreabCrumbDataConfig"; // 旧版本设计

const config = {
  /** 是否使用标题配置 */
  title: true,
  /** 是否使用底部区域配置 */
  footer: false,
  /** 是否使用固定区域配置 */
  fixedArea: false,
  groups: [
    {
      id: 'basic',
      title: '基础设置',
      visible: true,
      custom: false,
    },
    {
      id: 'config',
      title: '导航项',
      visible: true,
      custom: false,
    },
  ],
  items: {
    showIcon: {
      itemType: 'SWITCH',
      size: 'sm',
    },
    separator: {
      itemType: 'INPUT',
    },
    data: {
      itemType: 'CUSTOM',
      customRender: NavItemConfig,
    }
  },
  references: {
    showIcon: ['data'],
  },
  layout: [
    [
      {
        id: 'showIcon',
        items: ['showIcon'],
        groupId: 'basic',
        label: '显示图标',
        labelSpan: LABEL_SPAN_LG,
        wrapClassName: `${uiPropsDesignClsPrefix}-is-hold-right`,
      },
    ],
    [
      {
        id: 'separator',
        items: ['separator'],
        groupId: 'basic',
        label: '分隔符',
        labelSpan: LABEL_SPAN,
      },
    ],
    [
      {
        id: 'data',
        items: ['data'],
        groupId: 'config',
        label: '',
        labelSpan: 0,
      },
    ]
  ]
};

export default config;