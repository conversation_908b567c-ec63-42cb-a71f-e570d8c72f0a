.@{breadCrumbClsPrefix}-config {
  &-setting-address {
    .ui-icon {
      cursor: pointer;
      &:hover, &.active {
        color: var(--primary);
      }
    }
    &.active .ui-icon {
      color: var(--primary);
    }
  }

  &-data {
    &-icon {
      .ui-icon-selection-panel-line {
        left: 0;
      }
    }
    .ui-icon-selection-selected {
      padding: 0 var(--h-spacing-xs);
    }
    .ui-icon-selection-custom-icon {
      margin-right: var(--h-spacing-sm);
    }
  }

  &-nav-item {
    &-add-btn {
      width: 100%;
    }
    &-list-single {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      color: var(--regular-fc);
      &-action {
        visibility: hidden;
        .ui-icon {
          margin: 0 var(--h-spacing-md);
          cursor: pointer;
        }
      }
      &:hover &-action {
          visibility: visible;
      }
      &-content {
        width: 100%;
        display: flex;
        align-items: center;
        .ebcoms-assets-icon-editor {
          margin-right: var(--h-spacing-md);
        }
      }
    }
    &-action-btn.ui-btn.ebcoms-event-action-btn.ebcoms-event-action-btn.ebcoms-event-action-btn {
      width: calc(160 * var(--hd));
    }
    &-list-single {
      .ebcoms-assets-icon-editor {
        width: calc(30 * var(--hd));
        height: calc(30 * var(--hd));
      }
    }
  }
}
