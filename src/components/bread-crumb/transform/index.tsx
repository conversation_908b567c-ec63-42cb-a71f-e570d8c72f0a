import React, { memo, useCallback } from "react";
import { AnyObj } from "@weapp/ui";
import IconConfig from "../../../components-share/icon-config";
import { executeActions } from "../../../utils/transform";

const SingleBreadCrumb = memo(React.forwardRef<any, any>(
  (props) => {
    const { data, showIcon, allProps } = props;

    const onClick = useCallback((clickEvent: any) => {
      data?.action?.eventGroup?.forEach((et: any[]) => {
        et?.forEach((e) => {
          e?.events && executeActions(clickEvent?.target, e?.events, allProps);
        })
      })
    }, [allProps, data?.action?.eventGroup]);

    return (
      <span>
        {showIcon && data?.icon && IconConfig.getIcon(data?.icon)}
        <span onClick={onClick}>{data.content}</span>
      </span>
    )
  }
))

export const bread_crumb_trans_func = (props: any, allProps: any) => {
  const { data, ...resProps } = props;
  const result: JSX.Element[] = [];
  data?.forEach((da: AnyObj) => {
    result.push(<SingleBreadCrumb weId={`${props.weId || ''}_h3y9at@${da.id}`} {...resProps} data={da} allProps={allProps} />)
  });

  return {
    ...resProps,
    data: result,
  };
}