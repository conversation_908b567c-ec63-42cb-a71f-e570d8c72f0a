import { PureComponent } from 'react'
import { ViewProps } from '@weapp/ebdcoms';
import { BreadCrumb } from '@weapp/ui';
import ebMiddleware from '../../../eb-middleware';

class BreadCrumbView extends PureComponent<ViewProps> {
  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: ViewProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {
    },
  };

  render() {
    const { className, config } = this.props;
    return (<BreadCrumb weId={`${this.props.weId || ''}_391rk9`}
      {...config}
    />)
  }
};
export default ebMiddleware('BreadCrumbView', 'View', 'BreadCrumb', BreadCrumbView, { needRouter: true });