import { PureComponent } from 'react'
import { DesignProps } from '@weapp/ebdcoms';
import { AnyObj, BreadCrumb, BreadCrumbProps } from '@weapp/ui';
import ebMiddleware from '../../../eb-middleware';

class BreadCrumbDesign extends PureComponent<DesignProps<BreadCrumbProps>> {
  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: DesignProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {
      title: '面包屑',
      data: [
        {
          id: 'nav1',
          content: '导航1',
        },
        {
          id: 'nav2',
          content: '导航2',
        },
        {
          id: 'nav3',
          content: '导航3',
        }
      ],
      separator: '/',
      showIcon: false,
    } as AnyObj,
  };


  render() {
    const { className, config } = this.props;
    return (<BreadCrumb weId={`${this.props.weId || ''}_391rk9`}
      // className={className}
      {...config}
    />)
  }
};
export default ebMiddleware('BreadCrumbDesign', 'Design', 'BreadCrumb', BreadCrumbDesign, { needRouter: true });