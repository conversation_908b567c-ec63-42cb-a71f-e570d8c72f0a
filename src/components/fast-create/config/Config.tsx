import { getLabel } from "@weapp/utils";
import { fastCreateClsPrefix } from '../../../constants';
import TimeFieldSelect from '../common/TimeFieldSelect';
import ModuleType from '../common/ModuleType';

const cls = `${fastCreateClsPrefix}-config`;

const config = (com?: any, store?: any) => {
  return {
    /** 是否使用标题配置 */
    title: true,
    /** 是否使用底部区域配置 */
    footer: false,
    /** 是否使用固定区域配置 */
    fixedArea: false,
    /** 表单配置项，同公共组件Form，扩展属性参考下面定义的FormItemProps */
    items: {
      moduleType: {
        label: getLabel('252421','新建数据'),
        itemType: 'CUSTOM',
        customRender: ModuleType,
      },
      showTimeField: {
        label: getLabel('252422','显示时间字段'),
        labelSpan: 10,
        itemType: 'SWITCH',
        wrapClassName: `${cls}-isHoldRight`
      },
      chooseField: {
        label: getLabel('252423','选择字段') ,
        itemType: 'CUSTOM',
        customRender: TimeFieldSelect,
      },
      moveInTip: {
        label: getLabel('252424','移入提示') ,
        labelSpan: 10,
        itemType: "LOCALE",
        wrapClassName: `${cls}-isHoldRight`
      },
      manualInput: {
        label: getLabel('252425','手动输入时间') ,
        labelSpan: 10,
        itemType: 'SWITCH',
        wrapClassName: `${cls}-isHoldRight`
      },
      relatedPersonnel: {
        label: getLabel('252426','@相关人员') ,
        labelSpan: 10,
        itemType: 'SWITCH',
        wrapClassName: `${cls}-isHoldRight`
      },
      setPersonnel: {
        label: getLabel('252427','人员赋值') ,
        labelSpan: 10,
        itemType: "SELECT",
        data: [
          { id: 'manager', content: getLabel('252428','负责人')  },
        ],
        wrapClassName: `${cls}-select`
      },
      enterAdd: {
        label: getLabel('252429','enter键新建') ,
        labelSpan: 10,
        itemType: 'SWITCH',
        wrapClassName: `${cls}-isHoldRight`
      },
      searchTip: {
        label: getLabel('252430','搜索框提示') ,
        labelSpan: 10,
        itemType: "LOCALE",
        wrapClassName: `${cls}-isHoldRight`
      },
    },
    layout: [
      [
        { id: 'moduleType', label: getLabel('252421','新建数据'), labelSpan: 10, items: ['moduleType'], hide: false },
      ],
      [
        {
          id: 'showTimeField', label: getLabel('252422','显示时间字段'), labelSpan: 10, items: ['showTimeField'], hide: false,
          cascadeRulesOuter: {
            'showTimeField': {
              true: {
                show: ['chooseField', 'moveInTip', 'manualInput'],
              },
              false: {
                hide: ['chooseField', 'moveInTip', 'manualInput'],
              },
            },
          }
        }
      ],
      [
        { id: 'chooseField', label: getLabel('252423','选择字段'), labelSpan: 10, items: ['chooseField'], hide: true },
      ],
      [
        { id: 'moveInTip', label: getLabel('252424','移入提示'), labelSpan: 10, items: ['moveInTip'], hide: true },
      ],
      [
        { id: 'manualInput', label: getLabel('252425','手动输入时间'), labelSpan: 10, items: ['manualInput'], hide: true },
      ],
      [
        { id: 'relatedPersonnel', label: getLabel('252426','@相关人员'), labelSpan: 10, items: ['relatedPersonnel'], hide: false,
          cascadeRulesOuter: {
            'relatedPersonnel': {
              true: {
                show: ['setPersonnel'],
              },
              false: {
                hide: ['setPersonnel'],
              },
            },
          }
        },
      ],
      [
        { id: 'setPersonnel', label: getLabel('252427','人员赋值'), labelSpan: 10, items: ['setPersonnel'], hide: true },
      ],
      [
        { id: 'enterAdd', label: getLabel('252429','enter键新建'), labelSpan: 10, items: ['enterAdd'], hide: false },
      ],
      [
        { id: 'searchTip', label: getLabel('252430','搜索框提示'), labelSpan: 10, items: ['searchTip'], hide: false },
      ],
    ],
    references: {
      moduleType: ['chooseField', 'relatedPersonnel'],
    },
    customHide: function (col: any) {
      const _this = this as any;
      let hide = false;
      let { id, label } = col;
      const { client, data = {} } = _this?.props || {};
      const { moduleType, relatedPersonnel, showTimeField } = data;
      if (id === "relatedPersonnel") {
        hide = moduleType === "goal";
      }
      if (id === "setPersonnel") {
        hide = moduleType === "goal" || !relatedPersonnel;
      }
      if (id === "chooseField") {
        hide = !showTimeField;
      }
      if (id === "moveInTip") {
        hide = !showTimeField;
      }
      if (id === "manualInput") {
        hide = !showTimeField;
      }
      return { ...col, hide, label };
    },
  }
};

export default config;