.@{fastCreateClsPrefix}-config {
  &-isHoldRight {
    text-align: right;
  }

  &-select {
    .ui-select {
      width: 100%;
    }
  }
}

.@{fastCreateClsPrefix}-view {
  margin: var(--v-spacing-lg) 14px var(--v-spacing-md) 14px;
  position: relative;

  .ui-input-group.ui-input-group-append {
    cursor: pointer;

    .ui-input-wrap {
      border-right: 0;
    }

    .ui-input-append {
      display: -webkit-flex;
      display: flex;
      width: calc(var(--hd) * 35);
      height: calc(var(--hd) * 35);
      border-top-right-radius: var(--input-border-radius);
      border-bottom-right-radius: var(--input-border-radius);
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-align-items: center;
      align-items: center;
      background-color: #eee;
      transition: border-color var(--transition-du) var(--transition-tf);
      border-left: none;
      cursor: pointer;
    }
    .icon-wrapper {
      display: flex;
      justify-content: space-between;
      .date-popup{
        top: 41px !important;
        left: 77% !important;
        background: white !important;
        z-index: 999;
      }
      svg{
        color: #999999;
      }
    }
  }

  .icon-icon-date {
    position: absolute;
    top: calc(1 * var(--hd));
    border: 0;
    right: calc(42 * var(--hd));
    height: calc(33 * var(--hd));
    width: calc(130 * var(--hd));
    .ui-date-picker-wrap-input {
      border: 0;
      height: calc(33 * var(--hd));
      line-height: calc(33 * var(--hd));
    }
  }

  &-custom-datePicker {
    height: 100%;
    .custom-render-com {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px;

      &-value {
        color: var(--main-fc);
        height: 28px;
        line-height: 28px;

        &-clear {
          visibility: hidden;
          margin-right: 5px;
          cursor: pointer;
          color: var(--invalid-fc);

          &:hover {
            color: var(--secondary-fc);
          }
        }
      }

      &:hover {
        .custom-render-com-value {
          &-clear {
            visibility: visible;
          }
        }
      }

      .ui-icon {
        color: var(--secondary-fc);
      }
    }
  }

  &-focused {
    .icon-icon-add,.ui-input-append {
      color: #fff !important;
      background-color: var(--primary) !important;
      box-shadow: var(--input-focus-shadow) !important;
      border-color: var(--primary) !important;
    }

    .fast-input-main .ui-input-wrap {
      border-color: var(--input-focus-border-color);
      box-shadow: var(--input-focus-shadow);
      outline: 0;
    }

    .icon-icon-add svg {
      color: #fff !important;
    }

    .icon-icon-date {
      .ui-input-wrap {
        box-shadow: none !important;
        border-color: transparent !important;
      }
    }
  }

  &:hover {
    .ui-input-append,.ui-input-wrap {
      border-color: var(--input-hover-border-color);
    }
  }
}