import { PureComponent } from 'react';
import { DesignProps } from '@weapp/ebdcoms';
import { AnyObj } from '@weapp/ui';
import { getLabel } from "@weapp/utils";
import ebMiddleware from '../../../eb-middleware';
import FastInput from '../common'

class FastCreateDesign extends PureComponent<DesignProps> {
  // 可选，组件在设计区域内的一些配置选项
  static defaultOpts: AnyObj = {
    // 可选，是否有遮罩，用于控制组件设计模式下，功能的禁用，默认为false
    mask: true,
    layoutSizes: {
      // pc端自由布局
      gl: {
        w: 8,
        h: 18,
      },
    },
  };
  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  // 公共属性有title, titleEnabled, footerEnabled
  static defaultProps: DesignProps = {
    type: '',
    config: {
      title: getLabel('241237','快速新建'),
      labelPosition: {
        pc: 'lr',
        m: 'tb',
      },
      moduleType: 'goal',
      showTimeField: true,
      chooseField: 'periodStart',
      manualInput: false,
      relatedPersonnel: false,
      setPersonnel: 'manager',
      enterAdd: true,
    } as any,
  };

  render() {
    return <FastInput weId={`${this.props.weId || ''}_9dx80d`} {...this.props} />;
  }
};

export default ebMiddleware('FastCreateDesign', 'Design', 'FastCreate', FastCreateDesign)