import { PureComponent } from 'react';
import ebMiddleware from '../../../eb-middleware';
import { FastCreateViewProps } from '../types';
import FastInput from '../common';

class FastCreateView extends PureComponent<FastCreateViewProps> {
  componentDidMount() {

  }
  componentWillUnmount() {
  }
  render() {
    return (
      <FastInput weId={`${this.props.weId || ''}_a4v3ej`}  {...this.props} />
    )
  }
}

export default ebMiddleware('FastCreateView', 'View', 'FastCreate', FastCreateView);
