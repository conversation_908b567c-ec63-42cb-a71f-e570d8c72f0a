import { FC, useCallback, useMemo } from 'react';
import { FormDatas, Select, SelectValueType } from '@weapp/ui';
import { getLabel } from "@weapp/utils";
import { ConfigProps } from '@weapp/ebdcoms';

export interface TimeFieldSelectProps<T = any> extends ConfigProps  {
  value?: string,
  weId?: string,
  config: T;
  /** 修改config数据（单条数据）  */
  onChange: (value: string, act?: any) => void;
  /** 修改config数据（全部数据） */
  onConfigChange: (value?: FormDatas, act?: any) => void;
}

const TimeFieldSelect: FC<TimeFieldSelectProps> = (props) => {
  const { value, onConfigChange, weId, config } = props;

  const handleChange = useCallback((value: SelectValueType) => {
    onConfigChange?.({
      chooseField: value
    })
  }, [value])

  const selectData = useMemo(() => {
    const { moduleType } = config

    if (moduleType === 'goal') {
      return [
        {
          id: 'periodStart',
          content: getLabel('252416','目标开始时间')
        },
        {
          id: 'periodEnd',
          content: getLabel('252417','目标结束时间')
        }
      ]
    } else if (moduleType === 'task') {
      return [
        {
          id: 'beginDate',
          content: getLabel('252418','起始日')
        },
        {
          id: 'dueDate',
          content: getLabel('252419','到期日')
        }
      ]
    }

  }, [config.moduleType])

  return (
    <Select
      weId={`${weId || ''}_3r49zp`}
      value={value}
      style={{ width: '100%' }}
      data={selectData}
      onChange={handleChange}
    />
  )
}

export default TimeFieldSelect;