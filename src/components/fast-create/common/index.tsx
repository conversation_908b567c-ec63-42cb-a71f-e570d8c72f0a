import React, { ReactText } from 'react';
import { Input, Icon, DatePicker, DatePickerDateType, AnyObj, DatePickerProps, Dialog } from '@weapp/ui';
import { getLabel, request, RequestOptionConfig } from '@weapp/utils';
import { fastCreateClsPrefix } from '../../../constants';
import { RequestHeaderParams } from '../types'

const { message } = Dialog;
// @ts-ignore
const { InputAt } = Input

interface FastInputProps extends AnyObj {
  callBack?: (name:string, date?:string) => void,
  /** 模块接口路由标识 */
  objectModule?: string;
  /** 接口头部设置 */
  requestHeaderParams?: RequestHeaderParams;
}

export default class FastInput extends React.Component<FastInputProps, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      visible: false,
      name: '',
      date: '',
      quickAddInputFocused: false,
      atItemData: []
    }
  }

  public inputRef = React.createRef<any>();

  onDateChange = (value: DatePickerDateType | DatePickerDateType[]) => {
    this.setState({ date: value })
    this.setState({ visible: !this.state.visible })
  }

  visibleChange = () => {
    this.setState({ visible: !this.state.visible })
  }

  onInputChange = (v: ReactText) => {
    this.setState({ name: v })
  }

  onInputAtChange = (value: ReactText, itemData?: any) => {
    this.setState({ name: value, atItemData: itemData })
  }

  onPressEnter = () => {
    const { config } = this.props
    if (!config.enterAdd) return;

    this.createFastData()
  }
  
  createFastData = async () => {
    const { config } = this.props
    const { name, date, atItemData } = this.state
    const { moduleType, chooseField, relatedPersonnel, setPersonnel } = config

    if (!name) {
      message({
        type: 'error',
        content: getLabel('252420','请填写名称') ,
      })
      return false
    }

    let paramsData: AnyObj = {}
    if (moduleType === 'goal') {
      paramsData = {
        name,
        module: moduleType,
        [chooseField]: date
      }
    } else if (moduleType === 'task') {
      paramsData = {
        name: name?.match(/@.*?\s(\d+)/)?.[1] || name,
        [chooseField]: date,
        manager: atItemData?.[0]?.userId || ''
      }
    }

    try {
      let colConfig: RequestOptionConfig = {
        url: `/api/${moduleType}/component/quickNew/addNew`,
        method: 'post',
        data: paramsData,
      }

      const colRes = await request(colConfig)

      if (colRes.status) {
        this.props.callBack?.(name,date);
        this.setState({ visible: false, name: "", date: "" }, () => {
          message({ type: 'success', content: getLabel('65443', '新增成功')})
          if (relatedPersonnel && setPersonnel) {
            this.inputRef?.current?.setState({ value: '' })
          }
        })
        return { ...colRes }
      }
    }

    catch (e) {
      console.error(e)
    }
  }

  renderAppend = () => {
    return <div className="icon-wrapper" onClick={this.createFastData}>
      <div className={"icon-icon-add"} title={getLabel('241237','快速新建') }>
        <Icon weId={`${this.props.weId || ''}_gvn8x5`} name="Icon-add-to01" size="xs"></Icon>
      </div>
    </div>
  }

  setInputFocusedTrue = () => {
    this.setState({ quickAddInputFocused: true })
  }

  setInputFocusedFalse = () => {
    this.setState({ quickAddInputFocused: false })
  }

  onClick = () => this.setState({ popupVisible: !this.state.popupVisible})

  onDateValueClear = (e: any) => {
    e.stopPropagation()
    e.preventDefault()

    this.setState({ date: '' })
  }

  customRender = () => {
    const { config } = this.props;
    const { moveInTip } = config
    const { date } = this.state;
    return <div className={'custom-render-com'}>
      <span className={'custom-render-com-value'}>{date}</span>
      <div>
        {
          date ? (
            <Icon
              weId={`${this.props.weId || ''}_6kedit`}
              name="Icon-cancel"
              size={'s'}
              onClick={this.onDateValueClear}
              className={'custom-render-com-value-clear'}
            />
          ) : null
        }
        <Icon weId={`${this.props.weId || ''}_vs1ael`} name="Icon-schedule-o" size={'s'} title={moveInTip} />
      </div>
    </div>
  }

  render() {
    const { config } = this.props;
    const { showTimeField, searchTip, moveInTip, manualInput, relatedPersonnel, setPersonnel, moduleType } = config
    const { quickAddInputFocused, date } = this.state;
    let _className = quickAddInputFocused ? `${fastCreateClsPrefix}-view-focused` : "";

    let datePickerProps: DatePickerProps = {
      value: date,
      onChange: this.onDateChange,
      onPanelOpen: this.setInputFocusedTrue,
      onPanelClose: this.setInputFocusedFalse,
    }

    if (!manualInput) {
      datePickerProps = {
        ...datePickerProps,
        customRender: this.customRender,
        className: `${fastCreateClsPrefix}-view-custom-datePicker`
      }
    }

    return <div className={`${fastCreateClsPrefix}-view ${_className}`}>
      {
        (relatedPersonnel && setPersonnel) ? (
          <InputAt weId={`${this.props.weId || ''}_zq9rm7`}
            ref={this.inputRef}
            style={{ width: "100%" }}
            append={this.renderAppend()}
            placeholder={searchTip}
            allowClear
            onPressEnter={this.onPressEnter}
            className="fast-input-main"
            onFocus={this.setInputFocusedTrue}
            onBlur={this.setInputFocusedFalse}
            onChange={this.onInputAtChange}
            isMultiple={moduleType != 'task'}
          />
        ) : (
          <Input weId={`${this.props.weId || ''}_duomly`}
                 ref={this.inputRef}
                 style={{ width: "100%" }}
                 append={this.renderAppend()}
                 value={this.state.name}
                 placeholder={searchTip}
                 allowClear
                 onChange={this.onInputChange}
                 onPressEnter={this.onPressEnter}
                 className="fast-input-main"
                 onFocus={this.setInputFocusedTrue}
                 onBlur={this.setInputFocusedFalse}
          />
        )
      }
      {
        showTimeField ? (
          <div className="icon-icon-date" title={moveInTip}>
            <DatePicker
              weId={`${this.props.weId || ''}_rj1geg`}
              {...datePickerProps}
            />
          </div>
        ) : null
      }
    </div>
  }
}
