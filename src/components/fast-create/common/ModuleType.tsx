import { FC, useCallback, useMemo } from 'react';
import { getLabel } from "@weapp/utils";
import { FormDatas, Select, SelectValueType } from '@weapp/ui';
import { ConfigProps } from '@weapp/ebdcoms';

export interface ModuleTypeProps<T = any> extends ConfigProps {
  value?: string,
  weId?: string,
  config: T;
  /** 修改config数据（单条数据）  */
  onChange: (value: string, act?: any) => void;
  /** 修改config数据（全部数据） */
  onConfigChange: (value?: FormDatas, act?: any) => void;
}

const selectData = [
  { id: 'goal', content: getLabel('252415','目标')  },
  { id: 'task', content: getLabel('223938','任务')  },
]

const ModuleType: FC<ModuleTypeProps> = (props) => {
  const { value, onConfigChange } = props;

  const handleChange = useCallback((value: SelectValueType) => {

    onConfigChange?.({
      moduleType: value,
      chooseField: value === 'goal' ? 'periodStart' : 'beginDate'
    })
  }, [onConfigChange])

  return (
    <Select weId={`${props.weId || ''}_fvz4sp`}
      value={value}
      style={{ width: '100%' }}
      data={selectData}
      onChange={handleChange}
    />
  )
}

export default ModuleType;