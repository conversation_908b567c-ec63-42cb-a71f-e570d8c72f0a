import React from "react";
import { AnyObj, Dialog, FormLayoutType } from "@weapp/ui";
import { CommonContextProps, ModeType } from "../types";
import { commonRelationsRules } from "./constants";
import { getLabel, request } from "@weapp/utils";

const { message } = Dialog;
export const CommonContext = React.createContext<CommonContextProps>({});

/* mode = form  */

// 转换Browser的数据至事项关联数据格式，保存
/**
 * 
 * @param datas 转换前data
 * @param otherParams 
 * @param remainOriginData 保留原数据 
 * @returns 
 */
export const transBrowserData = (datas: any[], otherParams?: AnyObj, remainOriginData?: boolean) => {
	let result: AnyObj = {};
	datas?.forEach((data) => {
		const resKey = data?._entityType;
		if (!result[resKey]) result[resKey] = [];
		if (remainOriginData) {
			// 不可关联自身
			const res = judgeAssociateSelf(otherParams?.objectId, [data]);
			res[0] && result[resKey].push(res[0]);
		} else {
			const targetModule = commonRelationsRules[data._entityType] || data._entityType;
			let tempData = {
				targetId: data.id,
				targetName: data.targetName || data.content || data.name,
				targetModule,
				sort: data.sort,
				...otherParams,
			} as AnyObj;
			if (targetModule === 'biaoge') { // 关联表单浏览框单独处理(筛选条件)
				tempData = {
					...tempData,
					type: data.detailType,
					detailId: data.detailId,
					detailName: data.detailContent,
					sort: data.sort,
				}
			}
			result[resKey].push(tempData);
		}
	})
	return result;
}
// 转换事项关联数据格式至Browser的数据，回显
export const transMatterData = (datas: AnyObj) => {
	let result: any[] = [];
	Object.keys(datas)?.forEach((k) => {
		result = result.concat(datas[k]?.map((da: any) => ({
			...da,
			_entityType: k,
			id: da.targetId || da.id,
			content: da.content || da.targetName,
			name: da.name || da.targetName,
		})));
	})
	return result;
}

// 获取objectModule标记
export const getObjectModule = (mode?: ModeType, module?: string, otherParams?: AnyObj) => {
	// module: 关联表单数据对应表单设计器搭建的表单
	// ebpage：页面设计器搭建的页面数据（事项关联组件上还没有对应的浏览框, 后续新增, 目前是单向）
	const _module = mode === 'normal' ? 'ebpage' : module;
	const { formProps, formStore } = otherParams || {};
	return _module || formProps?.cardDetailDialogParams?.objectModule || formStore?.formProps?.cardDetailDialogParams?.objectModule;
}
/**
 * 获取objectId标记
 * id 规则：
 * 默认绑定页面：
 * 后续支持开关(绑定页面id)，支持生成动态id，支持一个页面放多个事项关联
 */
export const getObjectId = (mode?: ModeType, otherParams?: AnyObj) => {
	const { page, module, customParam, formData, formProps, formStore } = otherParams || {};
	// 规则：页面/表单数据id --- 一个页面仅先支持一个组件
	if (mode === 'normal') return page?.id || '';
	// 表单设计器搭建页面获取id
	switch(module) {
		case 'workflow': return customParam?.requestId || ''; // 审批
		case 'formdatareport': return formProps?.cardDetailDialogParams?.objectId || formStore?.formProps?.cardDetailDialogParams?.objectId || ''; // 业务表单
		case 'ebuilderform': return formProps?.cardDetailDialogParams?.objectId || formStore?.formProps?.cardDetailDialogParams?.objectId || ''; // 业务表单
		default: return formData?.id || formProps?.cardDetailDialogParams?.objectId || formStore?.formProps?.cardDetailDialogParams?.objectId || ''; // 表单数据
	}
	// const _id = mode === 'normal' ? `${page?.id}_${id}` : `${formData?.id}_${config?.fieldId}`; // 规则：页面/表单id+组件id
}
/**
 * 排序逻辑，同CardDetailForm，后续ui组件抛出，替换成ui库内逻辑
 */
export const isIdEqual = (a: AnyObj, b: AnyObj) => (a?.id || a?.targetId) === (b?.id || b?.targetId);

export const updateSortArr = (originArr: AnyObj[], nowArr: AnyObj[]) => {
	if (!originArr || originArr?.length === 0) {
		const nowValue = nowArr?.map((v, index) => ({ ...v, sort: 65536 * (index + 1) }))
		return {
			deleteList: [], updateList: [], createList: nowValue, datas: nowValue,
		}
	}

	// 排除已删除的数据, originArr.length <= nowArr.length
	const delArr = originArr.filter((a) => !nowArr?.find((b) => isIdEqual(a, b)));
	const sortBeforeArr = originArr.filter((a) => nowArr?.find((b) => isIdEqual(a, b)));
	let updateArr: AnyObj[] = [];
	const lengthNow = nowArr.length;
	let beforeSort = 0, pass = true;
	for (let i = 0, j = 0; j < lengthNow; j++) {
		const originData = sortBeforeArr[i];
		const nowData = nowArr[j];
		if (originData) {
			if (isIdEqual(originData, nowData)) {
				// 数据相同，无需调整
				beforeSort = originData.sort;
				i++;
			} else {
				const sort = (originData.sort - beforeSort) / 2 + beforeSort;
				if (sort >= 1 && sort > beforeSort && sort < originData.sort) {
					updateArr.push({
						...nowData,
						sort,
					});
					beforeSort = sort;
				} else {
					pass = false;
					break;
				}
			}
		} else {
			// 超出部分为在末尾新增的数据
			updateArr.push({
				...nowData,
				sort: beforeSort + 65536,
			});
			beforeSort += 65536;
		}
	}
	if (!pass) {
		// 达到设定安全值，全部重新排序
		updateArr = nowArr.map((data, index) => ({
			...data,
			sort: 65536 * (index + 1),
		}))
	}
	const addArr = updateArr?.filter((a) => !originArr?.find((b) => isIdEqual(a, b)));
	const updateResult = updateArr?.filter((a) => originArr?.find((b) => isIdEqual(a, b)));
	return {
		deleteList: delArr, updateList: updateResult, createList: addArr, datas: nowArr?.map((da) => {
			const nowSort = updateArr?.find((a) => a.id === da.id)?.sort;
			return {
				...da,
				sort: nowSort,
			}
		})
	}
}

export const judgeAssociateSelf = (objectId: string, result: Array<any>) => {
	if (!objectId) {
		console.error('未设置saveDataParams或saveDataParams未设置objectId');
		return result;
	}
	const resIds = new Set(result.map((data) => data.targetId || data.id));
	if (resIds.has(objectId)) {
		message({
			type: 'info',
			content: getLabel('214697','无法关联自己'),
		});
		return result.filter((res) => !(res.targetId === objectId || res.id === objectId)); // 过滤掉关联自身的数据
	}
	return result;
}

/* -------------------------------------------------------- */
/**
 * 获取分类设置唯一id
 */
export const getUUID = () => {
	return request({
		url: '/api/bcw/util/generateIds',
		params: { size: 1 },
	}).then((res) => {
		return res?.data?.[0] || ''
	})
}
export const getBusinessId = async (mode?: ModeType, otherParams?: AnyObj) => {
	if (otherParams?.id) return otherParams?.id;
	const result = await getUUID();
	return result;
}

// 通过layout id 获取layout配置
export const getLayoutById = (id: string, layout: FormLayoutType[]) => {
	let col = undefined;
	for(let i = 0; i < layout.length; i++) {
		if (col) break;
		col = layout[i]?.find((c) => c.id === id);
	}
	return col;
}