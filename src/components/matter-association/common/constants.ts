interface CommonRelationsRules {
  [key: string]: string | boolean,
}

/* 公共关联浏览框字段用于保存数据的key {browserBean.type: key} 兼容et老数据， */
/**
 * key与与@weapp/ui CardDetailForm 规则一致
 * browserBean.type层数据，按照实际Browser.AssociativeBrowser给到的 _entityType为准
 */
export const commonRelationsRules: CommonRelationsRules = {
  'production': 'production', // 关联产品
  'marketactivity': 'marketactivity', // 关联活动
  'document': 'document', // 关联文档
  'competitor': 'competitor', // 关联对手
  'contact': 'contact', // 关联联系人
  'clue': 'clue', // 关联线索
  'workreport': 'workreport', // 关联报告
  'odoc': 'odoc', // 关联公文
  'email': 'email', // 关联邮件
  'calendar': 'calendar', // 关联日程
  'orderform': 'orderform', // 关联订单
  'biaoge': 'biaoge', // 关联表单
  'workflow': 'workflow', // 关联流程
  'saleChance': 'saleChance', // 关联商机
  'mainline': 'mainline', // 关联项目
  'contract': 'contract', // 关联合同
  'kpiFlow': 'kpiFlow', // 关联绩效
  'task': 'task', // 关联任务
  'formdatareport': 'formdatareport', // 关联数据
  'cowork': 'cowork',// 关联协作
  'customer': 'customer', // 关联客户
}
