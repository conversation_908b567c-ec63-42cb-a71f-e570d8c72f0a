import { Browser, TypesBrowserValueType, MBrowser } from "@weapp/ui";
import { memo, forwardRef, useState, useCallback, useContext, useMemo, useEffect } from "react";
import { CommonContextProps, FormConfigProps } from "../types";
import { matterAssociationClsPrefix } from "../../../constants";
import { CommonContext, transMatterData } from "./utils";
// import { getLocaleValue } from "../../../utils/transform";
import { RequestOptionConfig, classnames, request } from "@weapp/utils";
import ebdcoms from "../../../utils/ebdcoms";

const { getLocaleValue } = ebdcoms.get();
const FormConfig = memo(forwardRef<any, FormConfigProps>(
  (props) => {
    const [value, updateValue] = useState<TypesBrowserValueType>([]);
    const { mode, className, config, save, relevance, deleteData, isMobile, preView } = useContext<CommonContextProps>(CommonContext);
    const { description, labelPosition, hideLabel, matterConfig, classificationSetting } = config || {};
    //matterConfig 设置的事项关联选项
    const _description = getLocaleValue(description);
    let position = isMobile ? labelPosition?.m : labelPosition?.pc;
    // 页面设计器，移动端上下布局
    if (isMobile && mode === 'normal') position = 'tb';

    const title = getLocaleValue(config?.title || {});
    const clsPrefix = classnames(`${matterAssociationClsPrefix}-form-config`, position, mode, className, {
      'mobile': isMobile,
      'hideLabel': hideLabel,
    });

    useEffect(() => {
      if (relevance) {
        updateValue(transMatterData(relevance));
      }
    }, [relevance, isMobile]);

    const onChange = useCallback((nowVal: TypesBrowserValueType) => {
      if (preView) {
        updateValue(nowVal);
        return;
      }
      save?.(nowVal, value)?.then(({ status }) => {
        status && updateValue(nowVal);
      });
    }, [preView, save, value]);

    const combinationParams = useMemo(() => ({
      optionIds: matterConfig?.map((m: { id: any; }) => m?.id)?.join() || '',
      businessId: classificationSetting?.id,
      isGroup: classificationSetting?.enableClassification,
    }), [classificationSetting?.enableClassification, classificationSetting?.id, matterConfig]);

    const browserAssociativeProps = useMemo(() => ({ onBeforeDeselect: deleteData }), [deleteData]);
    const fetchData = useCallback((options: RequestOptionConfig) => {
      const url = options.url as string;
      if (/\/common\/browser\/combination\/relevanceTypeBrowser/.test(url)) {
        return request(options)?.then((result) => {
          if (result.status) {
            const { data } = result;
            const { options } = data || {};
            const newOptions: never[] = options?.map((opt: any) => {
              return {
                ...opt,
                children: opt?.children?.filter((child: { id: any; }) => matterConfig?.find((a: { id: any; }) => a.id === child.id)),
              };
            });
            return {
              ...result,
              data: {
                ...data,
                options: newOptions
              }
            }
          }
          return result;
        })
      }
      return request(options);
    }, [matterConfig]);
    return <div className={clsPrefix}>
      {!hideLabel && <div className={`${matterAssociationClsPrefix}-form-config-label`}>{title || ''}</div>}
      <div className={`${matterAssociationClsPrefix}-form-config-wrapper`}>
        {_description && <div className={`${matterAssociationClsPrefix}-form-config-description`}>{_description}</div>}
        {
          isMobile ? (
            <MBrowser.MAssociativeBrowser weId={`${props.weId || ''}_vrx6sb`}
              key={combinationParams?.optionIds}
              type="relevanceTypeBrowser"
              module="relevance"
              combinationParams={combinationParams}
              //@ts-ignore
              enableAssociativeSelected={true}
              value={value}
              onChange={onChange}
              browserAssociativeProps={browserAssociativeProps}
              fetchData={fetchData}
            />
          ) : (
            <Browser.AssociativeBrowser weId={`${props.weId || ''}_rf92b1`}
              key={combinationParams?.optionIds}
              type="relevanceTypeBrowser"
              module="relevance"
              combinationParams={combinationParams}
              //@ts-ignore
              enableAssociativeSelected={true}
              value={value}
              onChange={onChange}
              browserAssociativeProps={browserAssociativeProps}
              fetchData={fetchData}
            />
          )
        }
      </div>
    </div>
  }
));

export default FormConfig;