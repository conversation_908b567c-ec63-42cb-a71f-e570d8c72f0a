import { memo, forwardRef, useContext, useState, useMemo, use<PERSON><PERSON>back, ReactNode, useImperativeHandle } from "react";
import { Method } from "axios";
import { MCardDetailForm, CardDetailForm, CardDetailFormStore, CardDetailFormStoreType, Icon, CardDetailFormAllDatas, FormLayoutProps, AnyObj, constantsM } from "@weapp/ui";
import { CommonContextProps, PageConfigProps, PageConfigRef } from "../types";
import { CommonContext, getLayoutById } from "./utils";
import { matterAssociationClsPrefix } from "../../../constants";
import { classnames, getLabel } from "@weapp/utils";
import { DesignDrag } from "../design";
import { DesignDrag as DesignDragM } from "../design-m";
import DesignDragContainer from "../design/drag/DesignDragContainer";

const { mCardDetailFormClsPrefix } = constantsM;
const noop = () => {};
const PageConfig = memo(forwardRef<PageConfigRef, PageConfigProps>(
  (props, ref) => {
    const [store] = useState<CardDetailFormStoreType>(new CardDetailFormStore());
    const [allLayout, updateAlllayout] = useState<any[]>([]);
    const { config, isMobile, commonApiParams, isDesign, onConfigChange, preView } = useContext<CommonContextProps>(CommonContext);
    const { defaultShow, classificationSetting } = config || {};
    const enableClassification = classificationSetting?.enableClassification;

    useImperativeHandle(ref, () => {
      return {
        doSave: store?.doSave,
      }
    });

    const beforeInit = useCallback((datas: CardDetailFormAllDatas) => {
      defaultShow && store.setState({ visible: true });
      let layoutIds: string[] = [];
      //@ts-ignore
      if (enableClassification && datas?.groupList) {
        if (isDesign) {
          // 初始化，classificationSetting需同步接口数据
          onConfigChange?.({ classificationSetting: {
            ...config?.classificationSetting,
            //@ts-ignore
            classificationSetting: datas?.groupList,
          }})
        }
        //@ts-ignore
        datas.groupList = datas?.groupList?.filter((group: any) => !group.isHidden)?.map((group) => {
          const lids = group.layoutIds?.filter((ids: any) => config?.matterConfig?.find((c: { id: any; }) => c.id === ids));
          layoutIds = layoutIds.concat(lids);
          return {
            ...group,
            layoutIds: lids,
          }
        })

      }
      if (datas?.layout) {
        let ls: any[] = [];
        [...datas?.layout]?.forEach((row) => {
          ls = ls.concat(row.slice(0));
        });
        updateAlllayout(ls);
        datas.layout = datas?.layout?.map((row) => row?.filter((col) => {
          const hasConfig = config?.matterConfig?.find((d: any) => d.id === col.id);
          const hasClass = !(enableClassification && !layoutIds?.find((a) => a === col.id));
          return hasConfig && hasClass;
        }));
        // 排序
        let layouts: FormLayoutProps[][] = [];
        config?.matterConfig?.forEach((a: { id: string; }, i: number) => {
          const l = getLayoutById(a.id, datas.layout);
          if (l) {
            if (isMobile) {
              layouts.push([l]);
            } else {
              if (!layouts[0]) layouts[0] = [];
              layouts[0].push(l);
            }
          }
        })
        return {
          ...datas,
          layout: layouts,
        }
      }
      return datas;
    }, [config?.classificationSetting, config?.matterConfig, defaultShow, enableClassification, isDesign, isMobile, onConfigChange, store]);

    const afterHideTag = useCallback((id: string) => {
      //@ts-ignore
      const { groupList, setState } = store;
      const result: any = groupList?.map((group: { layoutIds: any[]; }) => ({
        ...group,
        layoutIds: group?.layoutIds?.filter((l) => l !== id),
      }))?.filter((group: { layoutIds: string | any[]; }) => group.layoutIds?.length > 0);
      //@ts-ignore
      setState({ groupList: result })
    }, [store]);

    const commonProps = useMemo(() => ({
      url: isMobile ?  "/api/app/relevance/newForm/cardDetailForm/base" : "/api/relevance/newForm/cardDetailForm/base",
      method: 'POST' as Method,
      params: { businessId: config?.classificationSetting?.id || commonApiParams?.objectId },
      mode: true,
      store,
      saveDataParams: commonApiParams,
      dataParams: commonApiParams,
      saveParams: commonApiParams,
      openGroupList: enableClassification,
      // disableSubBtn: config?.classificationSetting?.enableClassification && isMobile,
      beforeInit,
      afterHideTag,
      tagNumber: parseInt(config?.lineNums || 5),
      enableSearch: config?.enableSearch,
      searchProps: {
        placeholder: getLabel('245667','请输入事项名称'),
      },
      className: classnames({
        isDesign: isDesign,
        defaultShow: defaultShow,
      }),
      prohibitRealTimeSaving: config?.saveMethod === 'oneTime' || preView,
    }), [isMobile, config?.classificationSetting?.id, config?.lineNums, config?.enableSearch, config?.saveMethod, commonApiParams, store, enableClassification, beforeInit, afterHideTag, isDesign, defaultShow, preView]);
    
    const customRenderTag = useCallback((props: AnyObj, com: ReactNode, datas: AnyObj[], activeClassKey: string, groupList: AnyObj[], searchValue?: any) => {
      if (defaultShow && isDesign) {
        return <DesignDrag weId={`${props.weId || ''}_39cml0`}
          layout={allLayout}
          lineNum={parseInt(config?.lineNums || 5)}
          enableClassification={enableClassification}
          activeClassKey={activeClassKey}
          groupList={groupList}
          searchValue={searchValue}
          onChangeActiveClassKey={noop}
        />
      }
      return com;
    }, [allLayout, config?.lineNums, defaultShow, enableClassification, isDesign]);
    
    const customRenderMCardDetailForm = useCallback((_this: any, com: ReactNode) => {
      if (isDesign && defaultShow) {
        return <DesignDragM weId={`${props.weId || ''}_s3rji6`}
          layout={allLayout}
          store={store}
        />
      }
      return com;
    }, [allLayout, defaultShow, isDesign, props.weId, store]);

    const customRenderCardDetailForm = useCallback((_this: any, com: ReactNode) => {
      if (isDesign && defaultShow && enableClassification) {
        return <DesignDragContainer weId={`${props.weId || ''}_s3rji6`}
          layout={allLayout}
          store={store}
        >
        </DesignDragContainer>
      }
      return com;
    }, [allLayout, defaultShow, enableClassification, isDesign, props.weId, store]);

    if (isDesign && !defaultShow) {
      if (isMobile) return (
        <div className={`${mCardDetailFormClsPrefix}-tags-footer ${matterAssociationClsPrefix}-design-drag-m-btn`}>
          <span>{getLabel('233611','更多及事项关联')}</span>
          <Icon weId={`${props.weId || ''}_i4c2ry`} name={"Icon-Down-arrow03"} />
        </div>
      )
      return ( <div className={`${matterAssociationClsPrefix}-page-design`}>
        <span>{getLabel('233611','更多及事项关联')}</span>
        <Icon weId={`${props.weId || ''}_p043o8`} name="Icon-Down-arrow01" />
      </div>)
    }
    
    return (
      <>
        {
          isMobile ? <MCardDetailForm
            weId={`${props.weId || ''}_85bfj6`}
            {...commonProps}
            //@ts-ignore
            hideSubBtn
            //@ts-ignore
            forbiddenGetData={defaultShow && isDesign}
            customRender={customRenderMCardDetailForm}
          /> : <CardDetailForm
            weId={`${props.weId || ''}_1ysxkp`}
            {...commonProps}
            //@ts-ignore
            forbiddenGetData={defaultShow && isDesign}
            //@ts-ignore
            customRenderTag={customRenderTag}
            customRender={customRenderCardDetailForm}
          />
        }
      </>
    )
  }
))

export default PageConfig;