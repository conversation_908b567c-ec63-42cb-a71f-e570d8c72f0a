.@{matterAssociationClsPrefix}-form-config {
  &-label {
    color: var(--regular-fc);
    word-break: break-word;
    font-size: var(--font-size-12);
  }
  &-description {
    word-break: break-word;
    color: var(--secondary-fc);
    font-size: var(--font-size-12);
    word-break: break-word;
    padding: calc(9 * var(--hd)) 0 calc(5 * var(--hd)) 0;
  }
  &.lr {
    display: flex;
    align-items: flex-start;
    .@{matterAssociationClsPrefix}-form-config-label {
      width: calc(80 * var(--hd));
      text-align: right;
      padding-left: 0;
      padding-right: calc(5 * var(--hd));
      flex-shrink: 0;
      line-height: calc(30 * var(--hd));
    }
    .@{matterAssociationClsPrefix}-form-config-wrapper {
      padding: 0 calc(5 * var(--hd));
      flex: 1 1 auto;
    }
    .@{matterAssociationClsPrefix}-form-config-description {
      border-bottom: calc(1 * var(--hd)) dashed var(--border-color);
      margin-bottom: calc(8 * var(--hd));
    }
    &.mobile {
      .@{matterAssociationClsPrefix}-form-config-label {
        line-height: calc(44 * var(--hd));
      }
    }
    // 按照表单设计器，左右布局样式处理
    &.form.mobile.view {
      .@{matterAssociationClsPrefix}-form-config-label {
        font-size: var(--font-size-14);
        width: 30%;
        padding-left: calc(13 * var(--hd));
        text-align: left;
      }
      .@{matterAssociationClsPrefix}-form-config-wrapper {
        width: 70%;
      }
    }
    &.form.design {
      padding: calc(5 * var(--hd)) calc(16 * var(--hd));
    }
  }
  &.tb {
    .@{matterAssociationClsPrefix}-form-config-label {
      line-height: calc(30 * var(--hd));
    }
    .@{matterAssociationClsPrefix}-form-config-label, .@{matterAssociationClsPrefix}-form-config-wrapper {
      padding: 0 calc(5 * var(--hd));
    }
    .@{matterAssociationClsPrefix}-form-config-description {
      border-top: calc(1 * var(--hd)) dashed var(--border-color);
    }
    // 表单设计模式边距
    &.design.form {
      padding: calc(5 * var(--hd)) calc(16 * var(--hd));
    }
    &.view.form.mobile:not(.hideLabel) {
      .@{matterAssociationClsPrefix}-form-config-label {
        background: var(--bg-base);
        padding: calc(20 * var(--hd)) calc(15 * var(--hd)) calc(5 * var(--hd));
        font-size: var(--font-size-14);
        max-width: unset;
        padding-left: var(--h-spacing-md);
      }
      .ui-formItem-item.ui-m-browser-associative-inner.is-placeholder .ui-formItem-item-placeholder {
        justify-content: start;
        padding-left: var(--h-spacing-md);
      }
    }
  }
}
.@{matterAssociationClsPrefix}-normal {
  .@{matterAssociationClsPrefix}-form-config{ 
    &-label {
      width: auto;
      max-width: calc(200 * var(--hd));
      font-size: var(--font-size-12);
      text-align: left;
      padding-right: calc(10 * var(--hd));
    }
    &.mobile {
      background: var(--base-white);
    }
  }
  // 表单设计器 接入 平铺模式样式
  &.form {
    .ui-form-module .ui-form-row .ui-form-col,
    .ui-form-module .ui-form-row .ui-form-col:first-child,
    .ui-form-module .ui-form-row .ui-form-col:last-child,
    .ui-card-detail-form-tags-group-container,
    .ui-card-detail-form-tags-footer {
      border-right: 0;
      border-left: 0;
    }
    .ui-card-detail-form-tags-single:not(.weapp-ui-props-design-matter-association-design-drag-single):first-child {
      border-left: 0;
    }
    .ui-card-detail-form-tags-row,
    .ui-card-detail-form-tags-row .ui-card-detail-form-tags-single:last-child {
      border-right: 0;
    }
  }
  .ui-card-detail-form-tags-empty {
    // display: none;
  }

  .ui-m-card-detail-form-search {
    border-top: var(--border-solid);
    margin-top: var(--hd);
  }
}

.@{matterAssociationClsPrefix}-view-container {
  height: auto;
}

// 移动端
.ebcom.ebcom-matterassociation.@{matterAssociationClsPrefix}-container-m {
  &:not(.titleEnabled) {
    background-color: transparent!important;
    border-bottom: 0;
  }
  .content {
    padding: 0;
  }
  &.titleEnabled {
    .content .ui-m-card-detail-form{
      margin-top: calc(-15 * var(--hd));
    }
  }
  .@{matterAssociationClsPrefix}-form-config-label {
    font-size: var(--font-size-14);
  }
  .@{matterAssociationClsPrefix}-form-config-wrapper {
    padding: 0;
  }
  .ui-formItem-item.ui-m-browser-associative-inner.is-placeholder .ui-formItem-item-placeholder {
    justify-content: start;
    padding-left: var(--h-spacing-md);
  }
}

.ebcom.ebcom-matterassociation.ebcom-m.ebcom-flow.ebcom-view.has-no-header {
  background: transparent;
  .content {
    padding: 0;
  }
}
.ebcom.ebcom-matterassociation.ebcom-flow.ebcom-design.has-no-header.@{matterAssociationClsPrefix}-container {
  .content {
    padding: 0;
  }
}

// 平铺模式 整体框架样式
.ebcom.ebcom-matterassociation.@{matterAssociationClsPrefix}-container-page {
  &.ebcom-view:not(.titleEnabled) {
    background: transparent;
    border: 0;
  }
  & > div.content {
    padding: 0;
  }
  &.titleEnabled:not(.@{matterAssociationClsPrefix}-container-m) .content {
    overflow: hidden;
    padding: 0 calc(15 * var(--hd));
  }
  .@{matterAssociationClsPrefix}-normal.form {
    &:not(.isDesign) {
      margin-top: calc(-16 * var(--hd));  
    }
    .ui-card-detail-form-tags-footer-hideTagFooter {
      border: 0;
    }
    .ui-card-detail-form {
      .ui-formItem-label-fixedWidth {
        width: calc(80 * var(--hd));
        padding-right: calc(4 * var(--hd));
        .ui-formItem-label {
          justify-content: flex-end;
        }
      }
      .ui-formItem-wrapper-fixedWidth {
        width: calc(100% - 80 * var(--hd));
        padding-left: calc(5 * var(--hd));
      }
    }
  }
}

.@{matterAssociationClsPrefix}-page-design {
  padding: var(--v-spacing-sm) 0;
  border: var(--form-item-border-module);
  background-color: var(--base-white);
  text-align: center;
  font-size: var(--font-size-12);
  color: #888;
  margin: var(--v-spacing-sm) 0;
}
.@{matterAssociationClsPrefix}-empty {
  background-color: var(--base-white);
  color: var(--regular-fc);
  height: 100%;
  max-height: calc(40 * var(--hd));
  min-height: calc(34 * var(--hd));
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
}