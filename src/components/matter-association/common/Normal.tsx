import { memo, forwardRef, useRef, useEffect, useCallback, useState, useContext, useImperativeHandle } from "react";
import { CommonContextProps, NormalProps, NormalRef, PageConfigRef } from "../types";
import FormConfig from "./FormConfig";
import { matterAssociationClsPrefix } from "../../../constants";
import { CommonContext } from "./utils";
import ResizeObserver from 'resize-observer-polyfill';
import PageConfig from "./PageConfig";
import { classnames } from "@weapp/utils";

const NormalConfig = memo(forwardRef<NormalRef, NormalProps>(
  (props, ref) => {
    const { className } = props;
    const { id, page, isMobile, isDesign, coms, resetLayoutByUserConfig, cardTB, config } = useContext<CommonContextProps>(CommonContext);
    const { titleEnabled, mode } = config || {};
    const containerRef = useRef<HTMLDivElement>(null);
    const [height, updateHeight] = useState<number>(0);
    const changeLayoutHeight = useCallback(() => {
      if (containerRef.current) {
        let clientHeight = containerRef.current.clientHeight;
        if (titleEnabled) {
          clientHeight += (mode ? 45 : 40);
        }
        if (height !== clientHeight) {
          updateHeight(clientHeight);
          const nowHeight = clientHeight + 15;
          const layouts = coms?.map((com: any) => {
            if (com.id === id) {
              const cardTBNum = Number(cardTB);
              const h = !height || !cardTB ? com.config.layout?.h : Math.round((Number(nowHeight) + cardTBNum) / (cardTBNum + 1)) + 1;
              return {
                ...com,
                customConfig: {
                  layout: {
                    ...com.config.layout,
                    h,
                  },
                },
              }
            }
            return com;
          })
          resetLayoutByUserConfig?.(layouts, id);
        }
      }
    }, [cardTB, coms, height, id, mode, resetLayoutByUserConfig, titleEnabled]);

    const pageRef = useRef<PageConfigRef>(null);
    useImperativeHandle(ref, () => ({
      pageRef: pageRef?.current,
    }))

    useEffect(() => {
      let observe: ResizeObserver | null = null;
      if (!isMobile && !isDesign && page?.layoutType === "GRID") {
        observe = new ResizeObserver(changeLayoutHeight)
        if (containerRef?.current && containerRef?.current) {
          observe.observe(containerRef?.current);
        }
      }
      return () => {
        observe?.disconnect();
      }
    }, [changeLayoutHeight, page?.layoutType, isMobile, isDesign]);

    const containerCls = classnames(`${matterAssociationClsPrefix}-normal`, className, {
      'isDesign': isDesign,
      'enableSearch-m': config?.enableSearch && isMobile,
    });
    return <div className={containerCls} ref={containerRef}>
      {
        config?.mode ? (
          <PageConfig weId={`${props.weId || ''}_e5t8pr`} ref={pageRef} />
        ) : (
          <FormConfig weId={`${props.weId || ''}_j7g8zn`} {...props} />
          )
      }
    </div>
  }
));

export default NormalConfig;