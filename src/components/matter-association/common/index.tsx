import { memo, forwardRef, useState, useEffect, useMemo, useCallback, useImperativeHandle, useRef } from "react";
import { CommonProps, CommonRef, ModeType, NormalRef } from "../types";
import Normal from "./Normal";
import { CommonContext, getObjectId, getObjectModule, transBrowserData, transMatterData, updateSortArr } from "./utils";
import { AnyObj, BrowserValueType, Dialog, SingleType } from "@weapp/ui";
// import { getLocaleValue } from "../../../utils/transform";
import { classnames, getLabel, request } from "@weapp/utils";
import { getMode } from "../utils";
import { matterAssociationClsPrefix } from "../../../constants";
import ebdcoms from "../../../utils/ebdcoms";

const { getLocaleValue } = ebdcoms.get();
const { message } = Dialog;
const Main = memo(forwardRef<CommonRef, CommonProps>(
  (props, ref) => {
    const { id, cardTB, config, module, customParam, formProps, formStore, page, form, formData, isMobile, className, isDesign, containerRef, coms, resetLayoutByUserConfig, onConfigChange } = props;
    const [relevance, updateRelevance] = useState<AnyObj>();
    const [initRelevance, updateInitRelevance] = useState<AnyObj>();
    const [lockSave, updateLockSave] = useState<boolean>(false);
    const normalRef = useRef<NormalRef>(null);
    const mode: ModeType = getMode(page?.module);
    const oneTimeSave = config?.saveMethod === 'oneTime';
    const matterConfig = config?.matterConfig;
    const _id = getObjectId(mode, { page, module, customParam, formData, formStore, formProps });
    const objectModule = getObjectModule(mode, module);
    // 首次加载即可，回显数据
    useEffect(() => {
      // 非平铺模式需单独走接口
      !isDesign && !config?.mode && _id && request({
        url: '/api/bcw/newRelevance/loadRelevanceList',
        method: 'post',
        data: {
          module: objectModule,
          objectId: _id
        }
      }).then((result) => {
        if (result.status) {
          // 过滤多余数据(未设置的类型不可展示)
          let res = {};
          Object.keys(result?.data || {}).forEach((k) => {
            if (matterConfig?.find((m: { id: string; }) => m.id === k)) {
              res = {
                ...res,
                [k]: result?.data?.[k],
              }
            }
          })
          updateRelevance(res);
          updateInitRelevance(res);
        }
      })
    }, []);

    useEffect(() => {
      return () => {
        updateRelevance({});
        updateInitRelevance({});
      }
    }, []);
    /**
     * 布局样式兼容代码
     */
    useEffect(() => {
      let dom = containerRef?.current?.querySelector('.content')?.parentElement;
      if (!dom && id) {
        dom = document.getElementById(id);
      }
      const pageCls = `${matterAssociationClsPrefix}-container-page`;
      dom?.classList?.add(`${matterAssociationClsPrefix}-container${isMobile ? '-m' : ''}`);
      if (config?.mode) {
        // 平铺模式
        dom?.classList?.add(pageCls);
      } else if (dom?.classList?.contains(pageCls)) {
        dom?.classList?.remove(pageCls)
      }
      const titleEnableCls = 'titleEnabled';
      if (config?.titleEnabled) {
        // 平铺模式
        dom?.classList?.add(titleEnableCls);
      } else if (dom?.classList?.contains(titleEnableCls)) {
        dom?.classList?.remove(titleEnableCls)
      }

      if (isDesign) {
        if (!(config?.mode && config?.defaultShow)) {
          dom?.classList?.add('mask');
        } else {
          dom?.classList?.remove('mask');
        }
      }
    }, [isDesign, isMobile, containerRef, mode, config?.mode, config?.defaultShow, config?.titleEnabled, id]);

    /* ------------------------------------------------------------------------------------ */
    const saveDataParams = useMemo(() => ({
      objectId: _id,
      objectModule,
      objectName: getLocaleValue(config?.title || {}),
      module: objectModule,
    }), [_id, config?.title, objectModule]);
    const doSave = useCallback((data: any[], preValue: any[], otherParams?: AnyObj) => {
      let params: AnyObj = { ...saveDataParams, createList: [], deleteList: [], updateList: [] };
      // 按照entityType划分数据 + 判断是否关联自身
      const relevanceDatas = transBrowserData(data, saveDataParams, true);
      const preRelevanceDatas = transBrowserData(preValue, saveDataParams, true);
      // 设置sort + 获取createList、deleteList、updateList数据
      const relevanceDatasKeys = Object.keys(relevanceDatas);
      relevanceDatasKeys.forEach((key) => {
        const { createList: cl, deleteList: dl, updateList: ul } = updateSortArr(preRelevanceDatas?.[key] as AnyObj[], relevanceDatas?.[key] as AnyObj[]);
        const cls = transBrowserData(cl, saveDataParams)?.[key];
        const dls = transBrowserData(dl, saveDataParams)?.[key];
        const uls = transBrowserData(ul, saveDataParams)?.[key];
        cls && (params.createList = params.createList.concat(cls));
        dls && (params.deleteList = params.deleteList.concat(dls));
        uls && (params.updateList = params.updateList.concat(uls));
      });
      if (relevanceDatasKeys?.length === 0) {
        Object.keys(preRelevanceDatas).forEach((key) => {
          const { deleteList: dl } = updateSortArr(preRelevanceDatas?.[key] as AnyObj[], []);
          const dls = transBrowserData(dl, saveDataParams)?.[key];
          dls && (params.deleteList = params.deleteList.concat(dls));
        })
      }
      // 执行保存接口，为空不可保存
      const isEmpty = !(params?.createList?.length > 0 || params?.deleteList?.length > 0 || params?.updateList?.length > 0);
      if (isEmpty) return new Promise((resolve) => resolve({ status: true })); // 为空不需要走接口

      return request({
        url: "/api/bcw/newRelevance/batchSave",
        method: 'post',
        data: params
      }).then((result: { status: any; msg: any; }) => {
        if (!result.status) {
          message({
            type: 'error',
            content: result.msg || getLabel('214692','关联失败'),
          });
        } else {
          message({
            type: 'success',
            content: getLabel('214693','关联成功'),
          });
          updateRelevance(relevanceDatas);
        }
        return result;
      });
    }, [saveDataParams]);
    const save = useCallback((data: any[], preValue: any[], otherParams?: AnyObj) => {
      // 设计视图不需要真实保存数据保存
      if (!isDesign && !lockSave) {
        if (oneTimeSave) {
          // 一次性保存
          const relevanceDatas = transBrowserData(data, saveDataParams, true);
          updateRelevance(relevanceDatas);
          return new Promise((res) => res({ status: true }))
        }
        return doSave(data, preValue, otherParams);
      } else if (lockSave) {
        const relevanceDatas = transBrowserData(data, saveDataParams, true);
        updateRelevance(relevanceDatas);
        updateLockSave(false);
      }
      return new Promise((res) => res({ status: true }))
    }, [isDesign, oneTimeSave, doSave, saveDataParams, lockSave]);

    /** 一次性保存 */
    const doOneTimeSave = useCallback(() => {
      if (config?.mode) {
        // 平铺模式
        normalRef?.current?.pageRef?.doSave?.();
      } else {
        let preValue: any[] = initRelevance ? transMatterData(initRelevance) : [], value: any[] = [];
        relevance && Object.values(relevance)?.forEach((v) => {
          value = value.concat(v);
        })
        doSave(value, preValue);
      }
    }, [config?.mode, doSave, initRelevance, relevance]);

    useImperativeHandle(ref, () => ({
      save: doOneTimeSave,
    }), [doOneTimeSave]);

    const deleteData = useCallback((data: SingleType<BrowserValueType>) => {
      return new Promise((resolve, reject) => {
        Dialog.confirm({
          content: getLabel('214694','确定要删除吗？'),
          onOk: () => {
            if (oneTimeSave) {
              const result = { ...relevance };
              const type = data?._entityType;
              result[type] = result[type]?.filter((da: { id: string | undefined; }) => da.id !== data.id);
              updateRelevance(result);
              resolve(true);
            } else {
              updateLockSave(true);
              const dl = transBrowserData([data], saveDataParams);
              let deleteList: any[] = [];
              Object.values(dl).forEach((v) => {
                deleteList = deleteList.concat(v);
              })
              const params: AnyObj = { ...saveDataParams, createList: [], deleteList, updateList: [] };
              request({
                url: "/api/bcw/newRelevance/batchSave",
                method: 'post',
                data: params
              }).then((result: { status: any; msg: any; }) => {
                if (!result.status) {
                  message({
                    type: 'error',
                    content: result.msg || getLabel('214695','删除失败'),
                  });
                } else {
                  message({
                    type: 'success',
                    content: getLabel('214696','删除成功'),
                  });
                }
                resolve(true);
              });
            }
          },
          onCancel: () => {
            reject();
          }
        })
      });
    }, [oneTimeSave, relevance, saveDataParams]);

    // 业务表单、EB表单预览地址不做拦截
    const isFormPreview = window.location.pathname.indexOf('/sp/formreport/preview/business') >= 0;
    const isFormPreviewM = window.location.pathname.indexOf('/sp/formbuilder/mpreview/biaoge') >= 0;
    const isEBFormPreview = (
      window.location.pathname.indexOf('/sp/ebdform/formbuilder') >= 0
      ||
      window.location.pathname.indexOf('/sp/formbuilder/mpreview/ebuilderform') >= 0
    ) && window.location.search.split('&').indexOf('type=preview') >= 0;
    const isWfForm= module === 'workflow'; // 流程表单无需拦截
    const preView = isFormPreview || isEBFormPreview || isFormPreviewM;
    const commonValue = useMemo(() => ({ id, config, page, form, isMobile, mode, className, save, preView,
      relevance, deleteData, isDesign, coms, resetLayoutByUserConfig, cardTB, commonApiParams: saveDataParams, onConfigChange }),
     [id, config, page, form, isMobile, mode, className, save, preView, relevance, deleteData, isDesign, coms, resetLayoutByUserConfig, cardTB, saveDataParams, onConfigChange]);

    if ((config?.matterConfig?.length || 0) <= 0) {
      const emptyCls = classnames(`${matterAssociationClsPrefix}-empty`, {
        'isDesign': isDesign,
      });
      return (
        <div className={emptyCls}>
          {getLabel('238211','请设置事项关联范围')}
        </div>
      )
    }

    if ((!_id && !isDesign) && !(isWfForm || preView)) return <div/>;
    return <CommonContext.Provider weId={`${props.weId || ''}_12ggdp`} value={commonValue}>
      {/* {
        mode === 'normal' ? <Normal weId={`${props.weId || ''}_7mxu3s`} /> :
          mode === 'form' ? <FormConfig weId={`${props.weId || ''}_ua1lh2`} /> : null
      } */}
      <Normal weId={`${props.weId || ''}_7mxu3s`} className={mode} ref={normalRef} />
    </CommonContext.Provider>
  }
));

export default Main;