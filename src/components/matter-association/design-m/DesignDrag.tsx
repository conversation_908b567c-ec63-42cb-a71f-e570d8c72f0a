import React, { ReactText, memo, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { CommonContext } from "../common/utils";
import { CommonContextProps } from "../types";
import { AnyObj, FormLayoutType, AnimateHeight, Icon, List, constants, constantsM, ListData, MSearchbar, CardDetailFormStoreType, utils } from "@weapp/ui";
import { observer } from "mobx-react";
import { matterAssociationClsPrefix } from "../../../constants";
import { classnames, getLabel } from "@weapp/utils";

interface DesignDragProps extends React.Attributes {
  store: CardDetailFormStoreType;
  layout: FormLayoutType;
}

type Type = 'single' | 'list';

interface ListSingleProps extends React.Attributes {
  title?: string;
  datas: ListData[];
  id: string;
  onSortEnd?: (datas: ListData[], id: string, type?: Type, oldIndex?: number, newIndex?: number) => void;
  type?: Type;
  sortable?: boolean;
}

const { isNil, moveItem } = utils;
const { EASE_IN_OUT } = constants;
const { mCardDetailFormClsPrefix } = constantsM;

const ListSingle = memo(React.forwardRef<any, ListSingleProps>(
  (props) => {
    const { title, datas, id, type, onSortEnd: _onSortEnd, sortable } = props;

    const customRenderContent = useCallback((rowData: AnyObj) => {
      return <div key={rowData.id} className={`${matterAssociationClsPrefix}-design-drag-m-list`}>
        <div className={'left'}>
          {
            sortable ? (
              <>
                <Icon weId={`${props.weId || ''}_u5t58o`} name="Icon-move" className="icon" />
                <span className="text">{rowData?.name}</span>
              </>
            ) : rowData?.name
          }          
        </div>
        <div className={'right'}>
          <span className="text">{getLabel('245657','未关联')}</span>
          <Icon weId={`${props.weId || ''}_fkpak2`} name="Icon-Right-arrow01" className="icon" />
        </div>
      </div>
    }, [props.weId, sortable]);

    const onSortEnd = useCallback((data: ListData[], dataGroupTitle?: any[], oldIndex?: number, newIndex?: number) => {
      _onSortEnd?.(data, id, type, oldIndex, newIndex);
    }, [_onSortEnd, id, type]);

    return (
      <>
        { title && <div className={`${matterAssociationClsPrefix}-design-drag-m-list-title`}>{title}</div> }
        {datas?.length > 0 && <List weId={`${props.weId || ''}_at110f_${id}`} data={datas} sortable={sortable} customRenderContent={customRenderContent} onSortEnd={onSortEnd}/> }
      </>
    ) 
  }
))

const DesignDrag = observer(React.forwardRef<any, DesignDragProps>(
  (props) => {
    const { onConfigChange, config } = useContext<CommonContextProps>(CommonContext);
    const { layout, store } = props;
    const [visible, updateVisible] = useState<boolean>(true);
    const [searchValue, updateSearchValue] = useState<ReactText>('');
    const { classificationSetting, matterConfig } = config || {};
    const { enableClassification, classificationSetting: _groupList1 } = classificationSetting;
    const [matterConfigs, updateMatterConfigs] = useState<any[]>([...matterConfig]);
    useEffect(() => {
      if (enableClassification) {
        const res = _groupList1?.filter((g: AnyObj) => {
          if (g.layoutIds?.length > 0 && !g.isHidden) {
            const ls = g.layoutIds?.find((id: string) => matterConfig?.find((c: any) => c.id === id ))
            return ls ? true : false;
          }
          return false;
        });
        //@ts-ignore
        store.setState({ groupList: res });
      }
    }, [_groupList1, enableClassification, store, matterConfig])

    const datas = useMemo(() => {
      return matterConfigs?.map((data: any, index: number) => {
        let l = layout?.find((r) => r.id === data?.id);
        return l ? {
          ...data,
          index,
          name: l?.carddetailform_label || l?.label || data?.name, // 多语言处理
        } :{
          ...data,
          index,
        }
      })?.filter((data: any) => !(searchValue && data?.name?.indexOf(searchValue) < 0));
    }, [layout, matterConfigs, searchValue]);

    const prevent = useCallback((e: any) => {
      e?.preventDefault?.();
    }, []);

    const onSortEnd = useCallback((data: ListData[], id: string, type?: Type, oldIndex?: number, newIndex?: number) => {
      if (type === 'list') {
        if (!(isNil(newIndex) || isNil(oldIndex))) {
          const oldData = [...data];
          moveItem(oldData, newIndex, oldIndex);
          const fromId = oldData?.[oldIndex]?.id;
          const toId = oldData?.[newIndex]?.id;
          const dataRes = matterConfigs;
          let fromIndex, toIndex;
          for (let i = 0; i < dataRes?.length; i++) {
            if (dataRes[i].id === fromId) fromIndex = i;
            if (dataRes[i].id === toId) toIndex = i;
            if (!isNil(fromIndex) && !isNil(toIndex)) break;
          }
          if(!isNil(fromIndex) && !isNil(toIndex)) {
            moveItem(dataRes, fromIndex, toIndex);
            updateMatterConfigs([...dataRes]);
            onConfigChange?.({ matterConfig: [...dataRes] });
          }
        }
      } else {
        onConfigChange?.({ matterConfig: data });
      }
    }, [matterConfigs, onConfigChange]);

    const onClick = useCallback(() => {
      updateVisible(!visible);
    }, [visible]);

    const cls = classnames(`${matterAssociationClsPrefix}-design-drag-m`, { "visible": visible });

    const onChangeSearchValue = useCallback((val: ReactText) => {
      updateSearchValue(val);
    }, []);

    return <div className={cls}>
        <AnimateHeight
          weId={`${props.weId || ''}_mt8pjt`}
          height={visible ? 'auto' : 0}
          duration={160}
          appear={visible}
          easing={EASE_IN_OUT}
          overflow={"hidden"}
        >
          <>
            {
              config?.enableSearch && (
                <div key="search" className={`${mCardDetailFormClsPrefix}-search`}>
                  <MSearchbar weId={`${props.weId || ''}_bbidlc`} 
                    onChange={onChangeSearchValue}
                    value={searchValue}
                    placeholder={getLabel('245667','请输入事项名称')}
                  />
                </div>
              )
            }
            <div onMouseDown={prevent}>
              {
                
                enableClassification ? (
                  <>
                    {
                      //@ts-ignore
                      store?.groupList?.map((group) => {
                        const gdatas = datas?.filter((da: any) => group?.layoutIds?.indexOf(da?.id) > -1);
                        return <ListSingle weId={`${props.weId || ''}_7jw5u3`}
                          datas={gdatas}
                          onSortEnd={onSortEnd}
                          title={group?.name}
                          id={group?.id}
                          type="list"
                          key={group?.id}
                          sortable={!searchValue}
                        />
                      })
                    }
                  </>
                ) : (
                  <ListSingle weId={`${props.weId || ''}_7jw5u3`}
                    datas={datas}
                    onSortEnd={onSortEnd}
                    id="_all"
                    type="single"
                    sortable={!searchValue}
                  />
                )
              }
            </div>
          </>
        </AnimateHeight>
        <div className={`${mCardDetailFormClsPrefix}-tags-footer ${matterAssociationClsPrefix}-design-drag-m-btn`} onClick={onClick}>
          <span>{visible ? getLabel('245656','收起') : getLabel('233611','更多及事项关联')}</span>
          <Icon weId={`${props.weId || ''}_i4c2ry`} name={visible ? "Icon-up-arrow03" : "Icon-Down-arrow03"} />
        </div>
    </div>
  }
));

export default DesignDrag;