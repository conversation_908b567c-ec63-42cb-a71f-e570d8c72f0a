.@{matterAssociationClsPrefix}-design-drag-m {
  padding: var(--v-spacing-md) 0;
  &-list {
    display: flex;
    justify-content: space-between;
    height: 100%;
    align-items: center;
    font-size: var(--font-size-14);
    .left, .right {
      display: flex;
      align-items: center;
      .text {
        max-width: calc(100% - var(--icon-size-xs) - calc(6 * var(--hd)));
        text-overflow: ellipsis;
        white-space: wrap;
        word-wrap: break-word;
      }
      .icon {
        padding: 0 calc(3 * var(--hd));
      }
    }
    .left {
      max-width: 60%;
      color: var(--regular-fc);
    }
    .right {
      max-width: 40%;
      color: var(--secondary-fc);
    }
    &-title {
      color: var(--regular-fc);
      padding: calc(var(--hd)*21) 0 calc(var(--hd)*9) var(--h-spacing-lg);
      background: var(--bg-base);
    }
  }
  .ui-list-content {
    display: block;
  }
  &-btn {
    color: var(--primary);
    cursor: pointer;
    position: relative;
    border-top: calc(var(--hd)*1) solid var(--diviling-line-color);
    border-bottom: calc(var(--hd)*1) solid var(--diviling-line-color);
    &:after {
      content: unset;
    }
  }
  &.visible .ui-list-item:first-child .@{matterAssociationClsPrefix}-design-drag-m-list {
    border-top: calc(var(--hd)*1) solid var(--diviling-line-color);
  }
}