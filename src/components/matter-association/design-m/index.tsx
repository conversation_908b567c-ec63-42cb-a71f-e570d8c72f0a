import Loadable from "../../../react-loadable";
import ebdcoms from "../../../utils/ebdcoms";

const MMatterAssociationDesign = async () => {
  await ebdcoms.load();
  return import(
    /* webpackChunkName: "m_matter_association_design" */
    "./Design"
  );
}

export default MMatterAssociationDesign;

export const DesignDrag = Loadable({
  name: 'DesignDragM',
  loader: () => import(
    /* webpackChunkName: "m_matter_association_design" */
    './DesignDrag'
  )
}) as any;
