import { PureComponent, createRef } from 'react';
import ebMiddleware from '../../../eb-middleware';
import Common from '../common';
import { MATTER_ASSOCIATION_EVENT_NAME } from '../constants';
import { CommonRef, MatterAssociationViewProps } from '../types';

class MatterAssociationView extends PureComponent<MatterAssociationViewProps> {
  commonRef = createRef<CommonRef>();
  componentDidMount() {
    const { id, events, onMount } = this.props;
    id && events?.on(MATTER_ASSOCIATION_EVENT_NAME.save, id, this.save);
    onMount?.({
      save: this.save,
    })
  }
  componentWillUnmount() {
    const { id, events } = this.props;
    id && events?.off(MATTER_ASSOCIATION_EVENT_NAME.save, id, this.save);
  }
  save = () => {
    const { config: { saveMethod } } = this.props;
    if (saveMethod === 'oneTime') {
      this.commonRef?.current?.save?.();
    } else {
      console.error('事项关联组件设置为实时保存,无需单独触发保存逻辑');
    }
  }
  render() {
    return (
      <Common weId={`${this.props.weId || ''}_1jatth`} {...this.props} className="view" ref={this.commonRef} />
    )
  }
}

export default ebMiddleware('MatterAssociationView', 'View', 'MatterAssociation', MatterAssociationView);
