.@{matterAssociationClsPrefix}-config {
  &-matterConfig {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: flex-end;
    color: var(--secondary-fc);
    .ui-browser .ui-icon {
      margin-left: calc(5 * var(--hd));
      cursor: pointer;
      .Icon-correct01 {
        color: var(--primary);
      }
    }
    .active {
      color: var(--primary);
    }
  }
  &-isHoldRight {
    text-align: right;
  }
  &-labelPosition {
    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: var(--font-size-12);
      color: var(--regular-fc);
      padding-bottom: calc(10 * var(--hd));
      .ui-icon {
        cursor: pointer;
      }
      .active {
        transform: rotate(180deg);
        transition: all .4s;
        line-height: 100%;
      }
    }
    &-content {
      display: none;
      &.active {
        display: block;
      }
      &-single {
        font-size: var(--font-size-12);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 calc(6 * var(--hd)) 0 calc(15 * var(--hd));
      }
    }
  }
  &-saveMethod {
    .ui-radio-group {
      display: block;
      label {
        margin-right: 0;
        line-height: calc(16 * var(--hd));
      }
    }
    &-single {
      display: flex;
      padding-bottom: var(--v-spacing-md);
    }
    &-showCode {
      color: var(--primary);
      cursor: pointer;
      font-size: var(--font-size-12);
      display: inline-flex;
      align-items: end;
      padding-left: calc(5 * var(--hd));
    }
  }
  &-classificationSetting {
    display: flex;
    justify-content: end;
    .ui-icon {
      margin-left: var(--h-spacing-md);
      cursor: pointer;
      color: var(--secondary-fc);
      &.active {
        color: var(--primary);
      }
    }
    &-menu {
      width: 70%;
      &-whole {
        width: 100%;
        background: #fff;
        &+div.@{matterAssociationClsPrefix}-config-classificationSetting-table {
          border-top: 0;
        }
        border: var(--border-solid);
        border-bottom: 0;
        .ui-menu-tab-top {
          border-bottom-color: #eeeff2;
        }
      }
    }
    &-table {
      border: var(--border-solid);
      &.hide {
        display: none;
      }
      .ui-editable-table-top {
        background: #fff;
        line-height: calc(35 * var(--hd));
        height: calc(35 * var(--hd));
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: calc(1 * var(--hd));
        padding-right: var(--h-spacing-lg);
        .ui-editable-table-top-title {
          width: 80%;
        }
        .ui-menu-tab-top {
          border-bottom: 0;
        }
      }
      .ui-table-grid {
        border: 0;
      }
    }
    &-dialog {
      .ui-dialog-body {
        padding-bottom: 0;
      }
    }
    &-error {
      position: absolute;
      padding: var(--v-spacing-sm) var(--h-spacing-sm);
      background: var(--form-item-error-background);
      border: var(--form-item-error-border);
      border-radius: var(--border-radius-xs);
      margin-top: var(--popover-arrow-width);
      line-height: normal;
      font-size: var(--font-size-12);
      white-space: nowrap;
      z-index: var(--form-item-zIndex);
      &-container {
        position: relative;
      }
      &-arrow-before {
        border-left: var(--popover-arrow-width) solid transparent;
        border-right: var(--popover-arrow-width) solid transparent;
        border-bottom: var(--popover-arrow-width) solid var(--form-item-required);
        width: 0;
        height: 0;
        margin-left: var(--h-spacing-md);
        position: absolute;
        top: calc(-1 * var(--popover-arrow-width));
      }
      &-arrow {
        border-left: var(--popover-arrow-width) solid transparent;
        border-right: var(--popover-arrow-width) solid transparent;
        border-bottom: var(--popover-arrow-width) solid
          var(--form-item-error-background);
        width: 0;
        height: 0;
        margin-left: var(--h-spacing-md);
        position: absolute;
        top: calc(calc(-1 * var(--popover-arrow-width)) + calc(1 * var(--hd)));
      }
    }
    &-table tr:last-child {
      .@{matterAssociationClsPrefix}-config-classificationSetting-error {
        position: relative;
        display: inline-block;
      }
      .ui-table-grid-td .ui-formItem-wrapper .ui-formSwitch.ui-formSwitch-error-bottom {
        display: block;
        .ui-formSwitch-showError {
          display: inline-block;
          position: relative;
          left: unset;
          top: unset;
        }
      }
      .ui-table-grid-td .ui-formItem-wrapper .ui-formSwitch.ui-formSwitch-error-right {
        .ui-formSwitch-showError {
          top: 0;
          left: calc(100 * var(--hd));
        }
      }
    }
    &-table tr:last-child:only-child {

    }
    &-locale {
      display: flex;
      align-items: center;
    }
  }
}

@import '../common/index.less';
@import '../design/index.less';
@import '../design-m/index.less';