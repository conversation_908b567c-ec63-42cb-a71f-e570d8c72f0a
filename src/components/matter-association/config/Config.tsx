import { AnyObj, Icon } from "@weapp/ui";
import { getLabel,handleSiteDomain } from "@weapp/utils";
import { matterAssociationClsPrefix } from '../../../constants';
import Alert from "../../../components-share/alert";
import LabelPosition from "./LabelPosition";
import SaveMethod from "./SaveMethod";
import ClassificationSetting from "./classification-setting/ClassificationSetting";

const cls = `${matterAssociationClsPrefix}-config`;
const getMatterConfig = () => {
  return {
    label: getLabel('214719', '事项关联范围'),
    labelSpan: 16,
    itemType: 'BROWSER',
    wrapClassName: `${cls}-matterConfig`,
    browserBean: {
      type: 'scopeItem',
      module: 'relevance',
      multiple: true,
      disabledSelectedSort: false,
      commonParams: {
        formType: "demo"
      },
      browserAssociativeProps: {
        customRenderSelectedList: () => <></>,
        customRenderSearch: (props: AnyObj) => {
          const { handleInputClick } = props;
          const active = props?.props?.value?.length > 0;
          const iconName = active ? "Icon-Setup-complete" : "Icon-set-up-o";
          return <div onClick={handleInputClick}>
            <Icon weId={`${props.weId || ''}_o4ftye`} name={iconName} size="sm" className={active ? 'active' : ''} />
          </div>
        }
      }
    }
  }
}
const getClassificationSettingConfig = () => ({
  classificationSetting: {
    label: getLabel('233624','分类显示'),
    labelSpan: 16,
    itemType: 'CUSTOM',
    groupId: 'showSetting',
    customRender: ClassificationSetting,
  },
  mode: {
    label: getLabel('233626','平铺模式'),
    labelSpan: 16,
    itemType: 'SWITCH',
    groupId: 'showSetting',
    size: 'sm',
    helpTip: getLabel('233628','若开启，则将已选择范围内的关联事项平铺展示') ,
    wrapClassName: `${matterAssociationClsPrefix}-config-isHoldRight`
  },
  defaultShow: {
    label: getLabel('247630','默认展开'),
    labelSpan: 16,
    itemType: 'SWITCH',
    groupId: 'showSetting',
    size: 'sm',
    wrapClassName: `${matterAssociationClsPrefix}-config-isHoldRight`,
    hide: true,
  },
  lineNums: {
    label: getLabel('247631','每行列数'),
    labelSpan: 12,
    itemType: 'INPUTNUMBER',
    groupId: 'showSetting',
    size: 'sm',
    wrapClassName: `${matterAssociationClsPrefix}-config-isHoldRight`,
    hide: true,
    precision: 0,
    step: 1,
    suffix: getLabel('247633','列'),
  },
  enableSearch: {
    label: getLabel('247632','启用搜索'),
    labelSpan: 16,
    itemType: 'SWITCH',
    groupId: 'showSetting',
    size: 'sm',
    wrapClassName: `${matterAssociationClsPrefix}-config-isHoldRight`,
    hide: true,
  },
})
const saveMethod = {
  label: '',
  labelSpan: 0,
  itemType: 'CUSTOM',
  wrapClassName: `${cls}-saveMethod`,
  customRender: SaveMethod,
}
const getFormConfig = (commonParams: AnyObj) => {
  return {
    items: {
      alertTip: {
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'tip',
        customRender: () => <Alert weId={`_1znj9e`}
          title={getLabel('214698', '【事项关联】组件使用说明')}
          helpUrl={handleSiteDomain("https://site-admin.eteams.cn/help/1968120461067785061")}
          content={
            <>
              <div>1.{getLabel('214699', '此控件可以用于选择事项关联数据')}</div>
              <div>2.{getLabel('214700', '可配置事项关联前台需要展示的模块数据页签')}</div>
            </>
          }
        />,
      },
      title: {
        label: getLabel('213305', '字段名称'),
        labelSpan: 24,
        itemType: 'LOCALEEX',
        groupId: 'fieldConfig',
        placeholder: getLabel('214701', '请输入'),
      },
      description: {
        label: '描述',
        labelSpan: 24,
        itemType: 'LOCALEEX',
        groupId: 'fieldConfig',
        placeholder: getLabel('214701', '请输入'),
      },
      matterConfig: {
        ...commonParams?.matterConfig,
        groupId: 'fieldConfig',
      },
      ...commonParams?.classificationSetting,
      saveMethod: {
        ...saveMethod,
        groupId: 'saveMethod',
      },
      hideLabel: {
        label: getLabel('214702', '隐藏标题'),
        labelSpan: 16,
        itemType: 'SWITCH',
        groupId: 'layoutConfig',
        size: 'sm',
        wrapClassName: `${matterAssociationClsPrefix}-config-isHoldRight`
      },
      labelPosition: {
        label: getLabel('214703', '标题位置'),
        labelSpan: 0,
        itemType: 'CUSTOM',
        groupId: 'layoutConfig',
        customRender: LabelPosition,
      },
    },
    groups: [
      { id: 'tip', title: '', visible: true, custom: false },
      { id: 'fieldConfig', title: getLabel('214704', '字段属性'), visible: true, custom: false },
      { id: 'showSetting', title: getLabel('233629','显示设置'), visible: true, custom: false }, 
      { id: 'saveMethod', title: getLabel('214705', '保存方式'), visible: true, custom: false },
      { id: 'layoutConfig', title: getLabel('214706', '布局属性'), visible: true, custom: false },
    ],
    ...commonParams?.linkConfig
  };
}

const config = (com?: any, store?: any) => {
  const inForm = com?.formId; // formId存在则为表单设计器
  const matterConfig = getMatterConfig();
  const classificationSetting = getClassificationSettingConfig();
  const linkConfig = {
    references: {
      matterConfig: ['classificationSetting'],
      mode: ['defaultShow', 'lineNums', 'enableSearch'],
    },
    customHide: function(col: any) {
      if (col.id === 'defaultShow' || col.id === 'lineNums' || col.id === 'enableSearch') {
        const _this = this as any;
        const data = _this.props?.data;
        const isPC = _this.props?.client?.toUpperCase?.() === "PC";
        if (data?.mode && !(!isPC && col.id === 'lineNums')) {
          return { ...col, hide: false };
        }
      }
      return col;
    }
  }
  if (inForm) return getFormConfig({
    matterConfig,
    classificationSetting,
    linkConfig
  });
  return {
    /** 是否使用标题配置 */
    title: true,
    /** 是否使用底部区域配置 */
    footer: false,
    /** 是否使用固定区域配置 */
    fixedArea: false,
    /** 表单配置项，同公共组件Form，扩展属性参考下面定义的FormItemProps */
    items: {
      matterConfig,
      saveMethod: {
        ...saveMethod,
        groupId: 'saveMethod',
      },
      ...classificationSetting,
    },
    groups: [
      { id: 'showSetting', title: getLabel('233629','显示设置'), visible: true, custom: false }, 
      { id: 'saveMethod', title: getLabel('214705', '保存方式'), visible: true, custom: false },
    ],
    ...linkConfig
  }
};

export default config;