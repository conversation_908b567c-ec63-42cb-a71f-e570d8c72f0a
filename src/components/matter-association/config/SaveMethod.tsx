import { forwardRef, memo, useCallback, useMemo, useState } from "react";
import { Radio, RadioOptionProps, Help, Dialog, Button, CodeMirror } from '@weapp/ui';
import copy from 'copy-to-clipboard';
import { ModeType, SaveMethodProps } from "../types";
import { matterAssociationClsPrefix } from "../../../constants";
import { getMode } from "../utils";
import { getLabel } from "@weapp/utils";

const { message } = Dialog;
const SaveMethod = memo(forwardRef<any, SaveMethodProps>(
  (props) => {
    const [visible, updateVisible] = useState<boolean>(false);
    const { value, onChange, page } = props;
    const mode: ModeType = getMode(page?.module);
    const content = mode === 'form' ? `
    window.ebuilderSDK.getPageSDK().on('formReady',(args)=>{
      // 获取表单实例
       const weFormSdk = window.WeFormSDK.getWeFormInstance();
       // 注册保存事件
       weFormSdk.registerAction(window.WeFormSDK.ACTION_FORM_SAVE, (successFn, failFn)=>{
           const pageSDK = window.ebuilderSDK.getPageSDK();
           if (pageSDK) {
             const allComs = pageSDK.coms || [];
             const matterInstanceIds = allComs.filter((com) => com.type === "MatterAssociation");
             matterInstanceIds.forEach((com) => {
               const dom = pageSDK.getCompInstance(com.id);
               dom.save();
             })
           }
           successFn();
       });
   });
   ` : `
    const pageSDK = window.ebuilderSDK.getPageSDK();
    if (pageSDK) {
      const allComs = pageSDK.coms || [];
      const matterInstanceIds = allComs.filter((com) => com.type === "MatterAssociation");
      matterInstanceIds.forEach((com) => {
        const dom = pageSDK.getCompInstance(com.id);
        dom.save();
      })
    }
  `;
    const data = useMemo(() => ([
      { id: 'realTime', content: getLabel('214709','实时保存') },
      { id: 'oneTime', content: getLabel('214710','统一保存') },
    ]), []);
    const showDialog = useCallback(() => updateVisible(true), []);
    const closeDialog = useCallback(() => updateVisible(false), []);
    const doCopy = useCallback(() => {
      const res = copy(content);
      res && message({
        type: 'success',
        content: getLabel('214711','复制成功'),
      });
    }, [content]);
    const buttons = useMemo(() => ([
      <Button weId={`${props.weId || ''}_mbebjt`} onClick={doCopy}>{getLabel('214712','复制')}</Button>
    ]), [doCopy, props.weId]);

    const customOptionRender = useCallback((_props: RadioOptionProps, ele: React.ReactNode) => {
      const tip = _props?.option?.id === "realTime" ?
        getLabel('214713','当添加/删除数据后，操作后立刻生效。无需点击按钮，进行统一保存。') :
        getLabel('214714','需要在表单设计器-源码或按钮动作事件中插入代码才可生效。生效后，点击“保存/提交”按钮或已绑定事件的按钮后，才对该字段的已选数据进行保存。');
      return <div className={`${matterAssociationClsPrefix}-config-saveMethod-single`}>
        {ele}
        <Help weId={`${props.weId || ''}_7grt2k`} title={tip} placement="bottom" />
        {_props?.option?.id === "oneTime" && (
          <div className={`${matterAssociationClsPrefix}-config-saveMethod-showCode`} onClick={showDialog}>{getLabel('214715','代码示例')}</div>
        )}
      </div>
    }, [props.weId, showDialog]);

    return <>
      <Radio weId={`${props.weId || ''}_wmt22k`} data={data} value={value} onChange={onChange} customOptionRender={customOptionRender} />
      <Dialog weId={`${props.weId || ''}_4rq71g`}
        title={getLabel('214715','代码示例')}
        icon="Icon-eb"
        visible={visible}
        onClose={closeDialog}
        closable
        buttons={buttons}
      >
        <CodeMirror weId={`${props.weId || ''}_2a9adj`} value={content} readOnly />
      </Dialog>
    </>
  }
))

export default SaveMethod;