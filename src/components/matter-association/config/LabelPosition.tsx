import React, { memo, useCallback, useMemo, useState } from "react";
import { Icon, Radio, RadioValueType } from "@weapp/ui";
import { LabelPositionProps, SingleLabelPositionProps } from "../types";
import { matterAssociationClsPrefix } from "../../../constants";
import { getLabel } from "@weapp/utils";

const SingleLabelPosition = memo(React.forwardRef<{}, SingleLabelPositionProps>(
  (props) => {
    const { prefixCls, data, type, value, onChange } = props;
    const doChange = useCallback((value: RadioValueType) => { onChange?.(value, type); }, [onChange, type]);
    return (<div className={`${prefixCls}-content-single`}>
      <span>{type}</span>
      <Radio weId={`${props.weId || ''}_xvalq1`} data={data} value={value} onChange={doChange} />
    </div>)
  }
))

const LabelPosition = memo(React.forwardRef<{}, LabelPositionProps>(
  (props) => {
    const { value: labelPosition, onChange } = props;
    const [visible, updateVisible] = useState<boolean>(true);
    const prefixCls = `${matterAssociationClsPrefix}-config-labelPosition`;
    const options = useMemo(() => ([
      { id: 'tb', title: getLabel('214707','上下'), content: getLabel('214707','上下') },
      { id: 'lr', title: getLabel('214708','左右'), content: getLabel('214708','左右') },
    ]), []);

    const onClick = useCallback(() => { updateVisible(!visible) }, [visible]);
    const activeCls = visible ? 'active' : '';

    const onChangeLabelPosition = useCallback((value: RadioValueType, type: string) => {
      if (type === 'PC') {
        onChange?.({
          ...labelPosition,
          pc: value,
        })
      } else {
        onChange?.({
          ...labelPosition,
          m: value,
        })
      }
    }, [labelPosition, onChange]);

    return <div className={prefixCls}>
      <div className={`${prefixCls}-title`}>
        <span>{getLabel('214703','标题位置')}</span>
        <Icon weId={`${props.weId || ''}_p7ovd3`} name="Icon-up-arrow03" className={activeCls} onClick={onClick} />
      </div>
      <div className={`${prefixCls}-content ${activeCls}`}>
        <SingleLabelPosition weId={`${props.weId || ''}_vwr43t`}
          prefixCls={prefixCls}
          data={options}
          value={labelPosition?.pc}
          onChange={onChangeLabelPosition}
          type="PC"
        />
        <SingleLabelPosition weId={`${props.weId || ''}_vwr43t`}
          prefixCls={prefixCls}
          data={options}
          value={labelPosition?.m}
          onChange={onChangeLabelPosition}
          type="H5"
        />
      </div>
    </div>
  }
))

export default LabelPosition;