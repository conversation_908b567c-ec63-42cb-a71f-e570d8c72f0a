import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { memo } from "react";
import { ClassificationSettingProps, ModeType } from "../../types";
import { AnyObj, Button, Dialog, EditableTable, FormItemProps, utils, Icon,  Menu, Switch } from "@weapp/ui";
import { matterAssociationClsPrefix } from "../../../../constants";
import { classnames, getLabel, request } from "@weapp/utils";
import { getBusinessId } from "../../common/utils";
import { getMode } from "../../utils";
import LocaleCom from "./LocaleCom";
import { getLocaleContent } from "./util";
import { toJS } from "mobx";

const { isEmpty, isNil } = utils;
const { message } = Dialog;

const ClassificationSetting = memo(React.forwardRef<{}, ClassificationSettingProps>(
  (props) => {
    // props
    const { value, onChange, page, config: { matterConfig } } = props;
    // state
    const [menuValue, updateMenuValue] = useState<string>('matterSetting');
    const [visible, updateVisible] = useState<boolean>(false);
    const [classificationSetting, updateClassificationSetting] = useState<AnyObj[]>([]); // 分类数据
    const [matterSetting, updateMatterSetting] = useState<AnyObj[]>([]); // 事项关联数据
    const [loading, updateLoading] = useState<boolean>(false);
    const [initClassificationSetting, updateInitClassificationSetting] = useState<AnyObj[]>([]); // 分类数据
    const [initMatterSetting, updateInitMatterSetting] = useState<AnyObj[]>([]); // 事项关联数据

    const [defaultGroupLabelIdList, updateDefaultGroupLabelIdList] = useState<string[]>([]); // 默认分组多语言id
    const [mappingAll, updateMappingAll] = useState<AnyObj>({}); // 映射关系(全数据)
    const [lockSave, updateLockSave] = useState<boolean>(false);
    const [hasInit, updateHasInit] = useState<boolean>(false);

    const enableClassification = value?.enableClassification || false;
    const changeEnableClassification = useCallback((enableClassification: boolean) => {
      onChange?.({ ...toJS(value), enableClassification })
    }, [onChange, value]);

    const active = value?.hasInit && matterConfig?.length > 0;
    const iconName = active ? "Icon-Setup-complete" : "Icon-set-up-o";
    const isClassificationSetting = menuValue === 'classificationSetting';
    const edittableRef = useRef<any>(null);
    const mode: ModeType = getMode(page?.module);

    // init id
    useEffect(() => {
      getBusinessId(mode, { page, id: value?.id })?.then((id) => {
        value?.id !== id && onChange?.({ ...toJS(value), id});
      })
    }, [mode, onChange, page, value]);
    
    // validate
    const [emptyClassificationSetting, updateEmptyClassificationSetting] = useState<string[]>([]);
    const validate = useCallback(() => {
      const result = classificationSetting?.filter((da) => !isEmpty(da.groupName));
      const res: string[] = classificationSetting?.filter((da) => isEmpty(da.groupName))?.map((da) => da.dataId);
      const rename: string[] = result?.filter((r) => result?.find((l) => l.dataId !== r.dataId && l.groupName?.trim() === r.groupName?.trim() ))?.map((l) => l.groupName);
      updateEmptyClassificationSetting(res);
      const timer = setTimeout(() => {
        updateEmptyClassificationSetting([]);
        timer && clearTimeout(timer);
      }, 3000);
      return new Promise((resolve, reject) => {
        edittableRef?.current?.doValidate?.(
          (result: any) => {
            const pass = !result?.find((da: { errors: { [s: string]: unknown; } | ArrayLike<unknown>; }) => da.errors && Object.values(da.errors)?.length > 0);
            if (!pass && res?.length <= 0) {
              updateMenuValue('matterSetting')  
            } else if (pass && rename?.length > 0) {
              updateMenuValue('classificationSetting');
              const tips =  Array.from(new Set(rename));
              message({
                type: 'info',
                content: getLabel('237810','分类名称重复：$0')?.replace('$0', tips?.join()),
              })
            }
            resolve(pass && rename?.length <= 0 && res?.length <= 0 ? true : false);
          }
        );
      })
    }, [classificationSetting]);

    // menu configuration
    const menuData = useMemo(() => ([
      { id: 'matterSetting', content: getLabel('233612','事项关联设置') },
      { id: 'classificationSetting', content: getLabel('233613','分类设置') },
    ]), []);

    const onMenuChange = useCallback((val) => {
      validate?.()?.then((pass) => {
        if (pass) updateMenuValue(val);
      });
    }, [validate]);

    // data init

    const init = useCallback((_id: string) => {
      if (!hasInit) {
        updateLoading(true);
        const getClass = request({
          url: '/api/relevance/config/group/queryByList',
          method: 'POST',
          data: { "businessId": _id }
        });
        
        const getMatterMapping = request(
          {
            url: '/api/relevance/config/item/queryByList',
            method: 'POST',
            data: { "businessId": _id }
          }
        ).then((result) => {
          const mappingAll: any = {};
          result?.data?.forEach((r: { relevanceGroupId: string | number; relevanceType: any; }) => {
            mappingAll[r.relevanceType] = r;
          });
          return { data: result, mappingAll }
        });
        return Promise.all([getClass, getMatterMapping]).then((result) => {
          const { data: resultData } = result[0] || {};
          const { list, defaultGroupLabelIdList } = resultData || {};
          const records = list?.map((da: { isDisable: number; }) => ({ ...da, isDisable: da.isDisable === 1 ? true : false }))
          const { mappingAll } = result[1] || {};
          updateMappingAll(mappingAll);
          updateClassificationSetting(records);
          updateInitClassificationSetting(records);
          const matterSetting = matterConfig?.map((da: any) => ({ ...da, group: mappingAll?.[da.id]?.relevanceGroupId, name: mappingAll?.[da.id]?.relevanceName || da.name }));
          updateMatterSetting(matterSetting);
          updateInitMatterSetting(matterSetting);
          // updateHasInit(true);
          updateLoading(false);
          updateDefaultGroupLabelIdList(defaultGroupLabelIdList);
        })
      } else {
        updateClassificationSetting(initClassificationSetting);
        const matterSetting = matterConfig?.map((da: any) => ({ ...da, group: mappingAll?.[da.id]?.relevanceGroupId, name: mappingAll?.[da.id]?.relevanceName || da.name }));
        updateInitMatterSetting(matterSetting);
        updateMatterSetting(matterSetting);
      }
    }, [hasInit, initClassificationSetting, mappingAll, matterConfig]);
    
    // dialog configuration
    const close = useCallback(() => {
      updateVisible(false);
      updateMenuValue('matterSetting');
    }, []);
    const open = useCallback(() => {
      updateVisible(true);
      getBusinessId(mode, { page, id: value?.id })?.then((id) => {
        init(id);
      })
    }, [init, mode, page, value]);

    const save = useCallback(() => {
      validate?.()?.then((pass) => {
        if (pass) {
          updateInitClassificationSetting(classificationSetting);
          updateInitMatterSetting(matterSetting);
          close();
          const res: any[] = [];
          // 存储入库
          let deleteClassifications: AnyObj[] = [];
          if (initClassificationSetting?.length > classificationSetting?.length) {
            deleteClassifications = initClassificationSetting?.filter((cl) => !classificationSetting?.find((cn) => cn.dataId === cl.dataId))?.map((cl) => ({ ...cl, deleteType: 1 }));
          }
          const itemConfigList = matterSetting?.map((da) => ({
            relevanceType: da.id,
            relevanceGroupId: da.group,
          })) || [];
          const groupConfigList = [...classificationSetting, ...deleteClassifications]?.map((da) => {
            res.push({
              id: da.dataId,
              name: da.groupName,
              isHidden: da.isDisable,
              layoutIds: itemConfigList?.filter((i) => i.relevanceGroupId === da.dataId)?.map((i) => i.relevanceType),
            })
            if (isNil(da.isDisable)) da = { ...da, isDisable: 0 };
            if (typeof da.isDisable === "boolean") da = {...da, isDisable: da.isDisable ? 1 : 0 }
            return {
              groupName: da.groupName,
              labelId: da.labelId,
              isDisable: da.isDisable,
              dataId: da.dataId,
              deleteType: da.deleteType,
            };
          });
          onChange?.({
            id: value.id,
            enableClassification,
            hasInit: true,
            classificationSetting: res
          })
          request({
            url: '/api/relevance/config/batchSave',
            method: 'POST',
            data: {
              "businessId": value?.id,
              "itemConfigList": itemConfigList,
              "groupConfigList": groupConfigList,
            }
          }).then((result) => {
            if (result?.status) {
              message({
                type: 'success',
                content: getLabel('233614','保存成功'),
              })
            } else {
              message({
                type: 'error',
                content: getLabel('233615','保存失败'),
              })
            }
          })
        }
      })
     }, [validate, onChange, enableClassification, classificationSetting, matterSetting, close, initClassificationSetting, value?.id]);

    const doSave = useCallback(() => {
      if (lockSave) {
        message({
          type: 'info',
          content: getLabel('233616','正在更新多语言, 请稍后保存。'),
        });

        return;
      }
      save();
     }, [lockSave, save]);

    const footer = useMemo(() => (
      <>
        <Button weId={`${props.weId || ''}_kqzgwa`} type="primary" onClick={doSave}>{getLabel('223931','保存')}</Button>
        <Button weId={`${props.weId || ''}_4461dy`} onClick={close}>{getLabel('223917','取消')}</Button>
      </>
    ), [props.weId, doSave, close]);

    // table configuration
    const onLocaleChange = useCallback((value: AnyObj) => {
      const result = classificationSetting?.map((cls) => {
        if (cls.dataId === value.dataId) {
          return value;
        }
        return cls;
      })
      updateClassificationSetting(result)
    }, [classificationSetting]);

    const onLocaleBlur = useCallback((value: string, data: AnyObj) => {
      updateLockSave(true);
      getLocaleContent(value, data, defaultGroupLabelIdList, (result: any) => {
        if (result?.nameAliasLabelId && result?.nameAliasLabelId !== result?.labelId) {
          onLocaleChange?.({...data, 'name': value, labelId: result?.nameAliasLabelId });
        }
        updateLockSave(false);
      });
    }, [defaultGroupLabelIdList, onLocaleChange])

    const bodyRender = useCallback((data: any, pos: any, name: any) => {
      let pass = emptyClassificationSetting?.length <= 0;
      if (!pass) {
        pass = !emptyClassificationSetting?.find((da) => da === data.dataId);
      }
      return <LocaleCom 
        weId={`${props.weId || ''}_4v3d10`}
        value={data[name]}
        data={data}
        name={name}  
        onChange={onLocaleChange}
        pass={pass}
        onBlur={onLocaleBlur}
      />
    }, [emptyClassificationSetting, onLocaleBlur, onLocaleChange, props.weId]);

    const matterColumns = useMemo(() => [
      { title: getLabel('233617','关联事项'), dataIndex: 'name', comKey: 'name', width: '40%' },
      { title: getLabel('233618','分类'), dataIndex: 'group', comKey: 'group', showRequired: true, width: '60%' },
    ], []);
    
    const classificationColumns = useMemo(() => [
      { title: getLabel('233619','名称'), dataIndex: 'groupName', comKey: 'groupName', showRequired: true, bodyRender },
      { title: getLabel('233621','禁用'), dataIndex: 'isDisable', comKey: 'isDisable', showCheckAll: true, checkAllProps: { itemType: 'CHECKBOX' }, },
    ], [bodyRender]);

    const groupOptions = useMemo(() => classificationSetting?.map((g) => ({ id: g.dataId, content: g.groupName })), [classificationSetting]);

    const matterComProps: FormItemProps = useMemo(() => ({
      'name': { itemType: 'INPUT', readOnly: true },
      'group': { itemType: 'SELECT', data: groupOptions, required: true, errorTipPosition: matterConfig?.length !== 1 ? 'bottom' : 'right', needAutoHideTip: false },
    }), [groupOptions, matterConfig?.length]);

    const classificationComProps: FormItemProps = useMemo(() => ({
      'isDisable': { itemType: 'CHECKBOX' }
    }), []);

    const title = useMemo(() => {
      const cls = classnames(`${matterAssociationClsPrefix}-config-classificationSetting-menu`, {
        [`${matterAssociationClsPrefix}-config-classificationSetting-menu-whole`]: !isClassificationSetting,
      });
      return (
        <div className={cls}>
          <Menu
            weId={`${props.weId || ''}_8n8jt6`}
            data={menuData}
            value={menuValue}
            onChange={onMenuChange}
          />
        </div>
      )
    }, [isClassificationSetting, props.weId, menuData, menuValue, onMenuChange]);

    const topTools = useMemo(() => (
      [
        { type: 'batchDelete', icon: 'Icon-Batch-delete', title: getLabel('213309','批量删除') },
        { type: 'add', icon: 'Icon-add-to03', title: getLabel('213310','新增') }
      ]
    ), []);
    const tableProps = useMemo(() => {
      const sortProps = isClassificationSetting ? {
        sortable: true,
        sortableType: 'icon' as any,
      } : {};
      return {
        selection: isClassificationSetting ? {
          getSelectionProps: (data: AnyObj) => {
            console.log("data", data);
            return {
              disabled: data.isDefault || data?.dataId < 1000, // 默认配置不可更改
            }
          }
        } : {},
        scroll: { y: 400 },
        ...sortProps,
      }
    }, [isClassificationSetting]);
    const onTableChange = useCallback((data: any) => {
      updateClassificationSetting(data)
    }, []);
    const onAddBefore = useCallback((data: any) => {
      (data[data.length - 1] = { ...data?.[data?.length - 1], isDefault: false });
      return data;
    }, []);
    const onDeleteBefore = useCallback((data: any, ids: string[]) => {
      const result = matterSetting?.map((da) => ({
        ...da,
        group: ids?.indexOf(da.group) >= 0 ? '' : da.group,
      }));
      updateMatterSetting(result);
      return data;
    }, [matterSetting]);

    //table classname
    const matterSettingCls = classnames(`${matterAssociationClsPrefix}-config-classificationSetting-table`, {
      hide: menuValue === 'classificationSetting',
    })
    const classificationSettingCls = classnames(`${matterAssociationClsPrefix}-config-classificationSetting-table`, {
      hide: menuValue === 'matterSetting',
    })
    return (
      <div className={`${matterAssociationClsPrefix}-config-classificationSetting`}>
        <Switch weId={`${props.weId || ''}_rrsmm6`} size="sm" value={enableClassification} onChange={changeEnableClassification} />
        {
          enableClassification && (
            <Icon weId={`${props.weId || ''}_mzixna`} name={iconName} size="sm" className={active ? 'active' : ''} onClick={open} />
          )
        }
        <Dialog
          weId={`${props.weId || ''}_adtuhz`}
          visible={visible}
          onClose={close}
          closable
          icon="Icon-eb"
          title={getLabel('233613','分类设置')}
          height={600}
          width={600}
          footer={footer}
          draggable
          wrapClassName={`${matterAssociationClsPrefix}-config-classificationSetting-dialog`}
          destroyOnClose
        >
          {!isClassificationSetting && title}
          <EditableTable
            weId={`${props.weId || ''}_ni72l7`}
            columns={matterColumns}
            comProps={matterComProps}
            data={matterSetting}
            title={title}
            onChange={updateMatterSetting}
            className={matterSettingCls}
            showSelection={false}
            isShowIndexInCheck={false}
            ref={edittableRef}
            loading={loading}
            validateDelay={3000}
            {...tableProps}
          />
          <EditableTable
            weId={`${props.weId || ''}_0kcl93`}
            columns={classificationColumns}
            comProps={classificationComProps}
            data={classificationSetting}
            title={title}
            deleteConfirm
            topTools={topTools}
            onChange={onTableChange}
            onAddBefore={onAddBefore}
            onDeleteBefore={onDeleteBefore}
            className={classificationSettingCls}
            showSelection
            isShowIndexInCheck={false}
            rowKey="dataId"
            loading={loading}
            validateDelay={3000}
            {...tableProps}
          />
        </Dialog>
      </div>
    )
  }
));

export default ClassificationSetting;