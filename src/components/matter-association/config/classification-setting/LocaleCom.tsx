import React, { useCallback, useMemo, memo, Attributes, useState } from "react";
import { AnyObj, Locale, FormItem, utils } from "@weapp/ui";
import { matterAssociationClsPrefix } from "../../../../constants";
import { getLabel } from "@weapp/utils";

const { RequiredMark } = FormItem;
const { isEmpty } = utils;

interface LocaleComProps extends Attributes {
  value: string;
  data: any;
  name: any;
  onChange?: (value: AnyObj) => void;
  onBlur?: (value: string, data: AnyObj) => void;
  pass?: boolean;
  hasRepeatName?: boolean;
}
const LocaleCom = memo(React.forwardRef<{}, LocaleComProps>(
  (props) => {
    const { value: _value, data, name, onChange, pass, onBlur, hasRepeatName } = props;
    const [value, updateValue] = useState<string>(_value);
    const doChange = useCallback((val: string, otherParams?: AnyObj) => {
      updateValue(val);
      onChange?.({...data, [name]: val });
    }, [data, name, onChange]);

    const onSuccessSave = useCallback((value:any, responseData?: any) => {
      if (responseData?.labelid) {
        onChange?.({...data, [name]: value, labelId: responseData?.labelid });
      }
    }, [data, name, onChange]);

    const markPosition =  window?.TEAMS?.globalConfig?.markPosition || (window?.TEAMS?.privateDeploy ? 'afterFields' : 'afterLabel');
    const hideRequiredMarkAfterEdit = window?.TEAMS?.globalConfig?.hiddenMarker ? Boolean(Number(window?.TEAMS?.globalConfig?.hiddenMarker)) : window?.TEAMS?.privateDeploy;
    let showError = markPosition === 'afterFields';
    if (showError) {
      showError = !(hideRequiredMarkAfterEdit && !isEmpty(value));
    }
    const saveUrlData = useMemo(() => ({ labelid: parseInt(data.labelId) > 0 ? '' : data.labelId }), [data.labelId]);
    const getUrlData = useMemo(() => ({ labelid: data.labelId }), [data.labelId]);
    const doBlur = useCallback((value: string) => {
      onBlur?.(value, data);
    }, [data, onBlur]);
    return <div className={`${matterAssociationClsPrefix}-config-classificationSetting-error-container`}>
      <div className={`${matterAssociationClsPrefix}-config-classificationSetting-locale`}>
        <Locale 
          weId={`${props.weId || ''}_4v3d10`}
          saveUrlData={saveUrlData}
          getUrlData={getUrlData}
          getUrl={"/api/bs/i18n/multilangset/getdata"}
          saveUrl={"/api/bs/i18n/multilangLabel/saveLabel"}
          useDefaultLangConfig
          value={value}
          onChange={doChange}
          onSuccessSave={onSuccessSave}
          isSetLocale={data?.labelId}
          onBlur={doBlur}
        />
        {
          showError && (
            <RequiredMark weId={`${props.weId || ''}_06onp8`} />
          )
        }
      </div>
      {
        (!pass || hasRepeatName) && (
          <div className={`${matterAssociationClsPrefix}-config-classificationSetting-error`}>
            <span className={`${matterAssociationClsPrefix}-config-classificationSetting-error-arrow-before`}/>
            <span className={`${matterAssociationClsPrefix}-config-classificationSetting-error-arrow`}/>
            <span>{
              !pass ? getLabel('233622','此项必填') : getLabel('35501','名称重复')
            }</span>
          </div>
        )
      }
    </div>
  }
));
export default LocaleCom;