import { AnyObj } from "@weapp/ui";
import { request } from "@weapp/utils";

const createI18N = (targetId: string, tableData: any[], defaultGroupLabelIdList: string[]) => {
  const _tableData = tableData.map((item) => ({
    ...item,
    value: item.value,
  }));
  return request({
    url: '/api/bs/bcw/area/multilangsetMultiline/savedata',
    method: 'POST',
    data: {
      labelid: "-1",
      module: "ebuilder",
      tableData: _tableData,
      targetId: defaultGroupLabelIdList?.indexOf(targetId) > 0 ? undefined : targetId, // 新增
    }
  });
}

const getI18N = (labelid: string) => {
  return request({
    url: '/api/bcw/administrativeArea/getData',
    method: 'POST',
    data: {
      labelid,
      targetId: labelid,
      module: "ebuilder",
    },
    headers: {
      Ebbusinessid: labelid,
    },
  })
}


export const getLocaleContent = async (value: any, data: AnyObj, defaultGroupLabelIdList: string[], successCallback: Function) => {
  const { labelId } = data || {};
  if (labelId) {
    const getLabelInfo: any = await getI18N(labelId);
    const createLabelInfo: any = await createI18N(labelId, getLabelInfo?.data?.usernamePerLangs, defaultGroupLabelIdList);
    successCallback({ value, labelId, nameAliasLabelId: createLabelInfo?.data?.new_targetId })
  } else {
    successCallback(value);
  };
};

