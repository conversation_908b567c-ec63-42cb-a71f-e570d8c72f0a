.@{matterAssociationClsPrefix}-design-drag {
  display: inline-flex;
  flex-wrap: wrap;
  width: 100%;
  border-right: var(--border-solid);
  border-left: var(--border-solid);
  &-single-outer {
    background-color: #f2f3d5;
  }
  &-single.ui-card-detail-form-tags-single {
    flex: 20% 1 0%;
    background-color: var(--base-white);
    height: 100%;
    border: var(--border-solid);
    border-color: transparent;
    border-left: var(--form-item-border-module);
    position: relative;
    &-first {
      border-left: 0;
    }
    &.needBorderBottom {
      border-bottom: var(--form-item-border-module);
    }
    &.needBorderRight {
      border-right: var(--form-item-border-module);
    }
    &.needLeftBorder {
      border-left: var(--form-item-border-module);
    }
    &.move:hover {
      background-color: var(--base-white);
      border: var(--form-item-border-module);
      box-shadow: var(--box-shadow);
    }
    .content {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-left: calc(8 * var(--hd));
    }
    .moveIcon {
      visibility: hidden;
      position: absolute;
      right: 16px;
    }
    &:not(.hasSearchValue):hover .moveIcon{
      visibility: visible;
    }
  }
  &-container {
    padding-top: var(--v-spacing-lg);
  }
}