import { PureComponent } from 'react';
import { DesignProps } from '@weapp/ebdcoms';
import ebMiddleware from '../../../eb-middleware';
import Common from '../common';
import { AnyObj } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { customHide } from '../utils';

class MatterAssociationDesign extends PureComponent<DesignProps> {
  // 可选，组件在设计区域内的一些配置选项
  static defaultOpts: AnyObj = {
    // 可选，是否有遮罩，用于控制组件设计模式下，功能的禁用，默认为false
    mask: true,
    layoutSizes: {
      // pc端自由布局
      gl: {
        w: 6,
        h: 6,
      },
    },
    compList: {
      customHide,
    }
  };

  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  // 公共属性有title, titleEnabled, footerEnabled
  static defaultProps: DesignProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {
      title: getLabel('207011','事项关联'),
      labelPosition: {
        pc: 'lr',
        m: 'tb',
      },
      saveMethod: 'realTime',
      lineNums: 5,
    } as AnyObj,
  };

  render() {
    return <Common weId={`${this.props.weId || ''}_1wqnmx`} {...this.props} className="design" isDesign />;
  }
};
export default ebMiddleware('MatterAssociationDesign', 'Design', 'MatterAssociation', MatterAssociationDesign);
