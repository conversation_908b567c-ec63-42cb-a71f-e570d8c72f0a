import Loadable from "../../../react-loadable";
import ebdcoms from "../../../utils/ebdcoms";

const MatterAssociationDesign = async () => {
  await ebdcoms.load();
  return import(
    /* webpackChunkName: "matter_association_design" */
    "./Design"
  );
}

export default MatterAssociationDesign;

export const DesignDrag = Loadable({
  name: 'DesignDrag',
  loader: () => import(
    /* webpackChunkName: "matter_association_design" */
    './drag/DesignDrag'
  )
}) as any;
