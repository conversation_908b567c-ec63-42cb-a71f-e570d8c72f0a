import React, { memo, useMemo } from "react";
import { getLabel } from "@weapp/utils";
import { AnyObj, BaseProps, Menu } from "@weapp/ui";

interface TagGroupProps extends BaseProps {
  datas?: AnyObj[];
  weId?: any;
  value: string;
  onChange: (value: string) => void;
}

const TagGroup = memo(React.forwardRef<{}, TagGroupProps>(
  (props) => {
    const allKey = '_all';
    const { prefixCls, datas, value, onChange } = props;

    const menuData = useMemo(() => {
      const result = [
        { id: allKey, content: getLabel("66835", "全部")},
      ];
      datas?.forEach((da) => {
        result.push({ id: da.id, content: da.name })
      })
      return result;
    }, [allKey, datas]);

    return menuData && menuData?.length > 0 ? (
      <div className={`${prefixCls}-group-container`}>
        <div className={`${prefixCls}-group`}>
          <Menu
            weId={`${props.weId || ''}_hjg3vl`}
            data={menuData}
            type="secondtab"
            value={value}
            onChange={onChange}
          />
        </div>
      </div>
    ) : null;
  }
));

export default TagGroup;