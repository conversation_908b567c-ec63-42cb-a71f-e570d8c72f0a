import React, { use<PERSON><PERSON>back, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react";
import { memo } from "react";
import { CommonContextProps, CommonProps } from "../../types";
import { AnyObj, Icon, constants } from "@weapp/ui";
import { matterAssociationClsPrefix } from "../../../../constants";
import { classnames, getLabel } from "@weapp/utils";
import { CommonContext } from "../../common/utils";
import { observer } from "mobx-react";
import TagGroup from "./TagGroup";
import { toJS } from "mobx";

const { cardDetailFormClsPrefix } = constants;

interface DesignDragProps extends CommonProps {
  lineNum: number;
  enableClassification: boolean;
  activeClassKey: string;
  groupList: AnyObj[];
  layout: AnyObj[];
  searchValue?: string;
  showGroup?: boolean;
  onChangeActiveClassKey: (activeClassKey: string) => void;
  menuGroup?: AnyObj[];
}

export interface DesignDragRef {
  showDatas: any[];
}

interface DesignDragSingleProps extends CommonProps {
  onMouseDown?: (event: MouseEvent, id: string, moveIndex: number) => void;
  data: AnyObj;
  moveId: string;
  moveIndex: number;
  searchValue?: string;
}

const Single = memo(React.forwardRef<any, DesignDragSingleProps>(
  (props) => {
    const { style: _style, className, data, onMouseDown: _onMouseDown, moveId, moveIndex, searchValue } = props;

    const onMouseDown = useCallback((e: any) => {
      _onMouseDown?.(e, data?.id, moveIndex);
    }, [_onMouseDown, data?.id, moveIndex]);

    const style = useMemo(() => data?.id === moveId ? _style : {}, [moveId, _style, data?.id]);

    const cls = classnames(className, {
      'move': moveId === data?.id,
      'hasSearchValue': searchValue,
    })

    return (
      <div style={style} onMouseDown={onMouseDown} className={cls} id={data?.id}>
        {
          data.icon && (
            <Icon weId={`${props.weId || ''}_ttx6ev@${data?.id}`} name={data.icon} className={'icon'} />
          )
        }
        <span className={'content'}>
          {data.label}
        </span>
        <Icon weId={`${props.weId || ''}_lhfva2`} name="Icon-move" className="moveIcon" />
      </div>
    )
  }
))

const calculate = (moveProps: AnyObj, moveState: AnyObj, draggingState: AnyObj, moveIndex: number, datasLength: number, _lineNum?: number) => {
  const moveDomWidth = moveProps?.width;
  const moveDomHeight = moveProps?.height;
  const x = moveState.x - draggingState.x;
  const lineNum = _lineNum || 5;
  const y = moveState.y - draggingState.y;
  let offestX = x / moveDomWidth;
  if (Math.abs(offestX) < 0.5) offestX = 0;

  const offestY = y / moveDomHeight;

  const lastColNum = datasLength % lineNum;
  const rowsLength = Math.ceil(datasLength / lineNum);
  
  const offestXPrecent = Math.floor(Math.ceil(offestX * 2) / 2);
  const offestYPrecent = Math.floor(Math.ceil(offestY * 2) / 2);

  const fromIndex = moveIndex;

  let toIndex = fromIndex, offsetPositionX = 0, offsetPositionY = 0;
  if (offestXPrecent && !offestYPrecent) {
    toIndex = fromIndex + offestXPrecent;
    // 临界点
    const limitMin = Math.floor(fromIndex / lineNum) * lineNum, limitMax = (Math.floor(fromIndex / lineNum) + 1)  * lineNum - 1;
    if (toIndex >= limitMax) toIndex = limitMax;
    else if (toIndex < limitMin) toIndex = limitMin;

    // 偏移位置
    offsetPositionX = moveDomWidth * (toIndex - fromIndex);
  } else if (!offestXPrecent && offestYPrecent) {
    // 纵向偏移超过一半
    toIndex = fromIndex + lineNum * offestYPrecent;
    let limitMax = datasLength - 1, limitMin = fromIndex % lineNum;
      if (fromIndex % lineNum < lastColNum) {
        limitMax = (rowsLength - 1) * lineNum + lastColNum - 1;
      } else {
        limitMax = (rowsLength - 1) * lineNum + limitMin;
        if (lastColNum) {
          limitMax = (rowsLength - 2) * lineNum + limitMin;
        }
      }
      if (toIndex > limitMax) toIndex = limitMax;
      else if (toIndex < limitMin) toIndex = limitMin;

      offsetPositionY = moveDomHeight * ((toIndex - fromIndex) / lineNum);
    } else if (offestXPrecent && offestYPrecent) {
      // 横、纵向偏移超过一半
      toIndex = fromIndex + lineNum * offestYPrecent + offestXPrecent;

      const limitMax = datasLength - 1, limitMin = 0;
      if (toIndex > limitMax) toIndex = limitMax;
      else if (toIndex < limitMin) toIndex = limitMin;

      offsetPositionY = moveDomHeight * offestYPrecent;
      offsetPositionX = moveDomWidth * offestXPrecent;

    }
    return { toIndex, offsetPositionY, offsetPositionX };
}

const DesignDrag = observer(React.forwardRef<DesignDragRef, DesignDragProps>(
  (props, ref) => {
    const { lineNum = 5, layout, searchValue, enableClassification, activeClassKey, groupList,
      showGroup, onChangeActiveClassKey, menuGroup
    } = props;
    const containerRef = useRef<HTMLDivElement>(null);
    const [dragging, updateDragging] = useState<boolean>(false);
    const [draggingState, updateDraggingState] = useState<AnyObj>({});
    const [moveState, updateMoveState] = useState<AnyObj>({});
    const [moveId, updateMoveId] = useState<string>('');
    const [moveIndex, updateMoveIndex] = useState<number>(0);
    const [datas, updateDatas] = useState<AnyObj[]>([]);

    const { onConfigChange, config } = useContext<CommonContextProps>(CommonContext);

    useEffect(() => {
      const datas: AnyObj[] = [];
      const ids = config?.matterConfig?.map((d: { id: any; }) => d.id);
      const val = searchValue?.trim();
      const lys = val ? layout?.filter((l) => l?.label?.indexOf(searchValue) >= 0) : layout;
      ids?.forEach((id: string) => {
        const a = lys?.find(d => d.id === id);
        a && datas.push(a);
      })
      if (enableClassification) {
        if (activeClassKey !== '_all') {
          const showIds = groupList?.find((g) => g.id === activeClassKey)?.layoutIds || [];
          const nowDatas = datas?.filter((da) => showIds.indexOf(da.id) >= 0);
          updateDatas(nowDatas);
          return;
        }
        let showIds: any[] = [];
        groupList?.filter((g) => !g.isHidden)?.forEach((g) => showIds = showIds.concat(g.layoutIds?.slice(0)|| []));
        const nowDatas = datas?.filter((da) => showIds.indexOf(da.id) >= 0);
        updateDatas(nowDatas);
        return;
      }
      updateDatas(datas);
    }, [activeClassKey, config?.classificationSetting, config?.matterConfig, enableClassification, groupList, layout, searchValue]);

    const prevent = useCallback((e: any) => e?.preventDefault?.(), []);

    const onDrag = useCallback((e: any) => {
      if (moveId) {
        const { clientX, clientY } = e;
        const moveState = {
          x: clientX,
          y: clientY,
        }
        updateMoveState(moveState)
      }
    }, [moveId]);

    const onDragStop = useCallback((e: any) => {
      if (dragging) {
        const moveDom = document.querySelector(`div[class~="weapp-ui-props-design-matter-association-design-drag-single"][id="${moveId}"]`)
        const moveDomWidth = moveDom?.clientWidth;
        const moveDomHeight = moveDom?.clientHeight;
        if (moveDomWidth && moveDomHeight) {
          const fromIndex = moveIndex;
          const { toIndex } = calculate(
            {width: moveDomWidth, height: moveDomHeight },
            moveState, draggingState, moveIndex, datas?.length, lineNum);
      
          if (enableClassification && activeClassKey !== '_all') {
            // 调整分类后数据顺序

            const fromData: any = datas?.slice(fromIndex, fromIndex + 1);
            const result = [...datas]?.filter((d, i) => i !== fromIndex);
            const nowDatas = [...result.slice(0, toIndex).concat(fromData).concat(result.slice(toIndex))];
            updateDatas(nowDatas);

            const toId = datas?.[toIndex]?.id;
            const ids = config?.matterConfig?.map((d: { id: any; }) => d.id);
            const resFromIndex = ids?.indexOf(moveId);
            const resToIndex = ids?.indexOf(toId);
            const resfromData: any = config?.matterConfig?.slice(resFromIndex, resFromIndex + 1);
            const res = [...config?.matterConfig]?.filter((d, i) => i !== resFromIndex);
            const resDatas = [...res.slice(0, resToIndex).concat(resfromData).concat(res.slice(resToIndex))];
            onConfigChange?.({ matterConfig: resDatas });
          } else {
            const fromData: any = datas?.slice(fromIndex, fromIndex + 1);
            const result = [...datas]?.filter((d, i) => i !== fromIndex);
            const nowDatas = [...result.slice(0, toIndex).concat(fromData).concat(result.slice(toIndex))];
            updateDatas(nowDatas);
            onConfigChange?.({ matterConfig: nowDatas });
          }
        }
        updateDragging(false);
        updateMoveState({});
        updateMoveId('');
      }
    }, [activeClassKey, config?.matterConfig, datas, dragging, draggingState, enableClassification, lineNum, moveId, moveIndex, moveState, onConfigChange]);

    useEffect(() => {
      window.document.addEventListener('mousemove', onDrag);
      window.document.addEventListener('mouseup', onDragStop);
      return () => {
        window.document.removeEventListener('mousemove', onDrag);
        window.document.removeEventListener('mouseup', onDragStop);
      }
    }, [onDrag, onDragStop]);

    const onMouseDown = useCallback((e: MouseEvent, id: string, moveIndex: number) => {
      prevent?.(e);
      if (!searchValue) {
        updateMoveId(id);
        updateMoveIndex(moveIndex);
        const { clientX, clientY } = e;
        updateMoveState({});
        updateDragging(true);
        updateDraggingState({
          x: clientX, y: clientY
        })
      }
    }, [prevent, searchValue]);

    const singleStyle = useMemo(() =>  lineNum ? {
      // flex: 0,
      width: `${100 / lineNum}%`,
    } : {}, [lineNum]);
    
    const rowsLength = Math.ceil(datas?.length / lineNum);

    const lastColNum = datas?.length % lineNum;

    const nowResult = useMemo(() => {
      const draggingStyle = dragging ? {
          transition: 'none',
          transform: `translate(${moveState.x - draggingState.x}px, ${moveState.y - draggingState.y}px)`,
        } : {}
      if (dragging) {
        const moveDom = document.querySelector(`div[class~="move"][id="${moveId}"]`)
        const moveDomWidth = moveDom?.clientWidth;
        const moveDomHeight = moveDom?.clientHeight;
        if (moveDomWidth && moveDomHeight) {
          const fromIndex = moveIndex;
          const { toIndex, offsetPositionX, offsetPositionY } = calculate(
            {width: moveDomWidth, height: moveDomHeight },
            moveState, draggingState, moveIndex, datas?.length, lineNum);
      
          const fromData: any = datas?.slice(fromIndex, fromIndex + 1);
          const result = [...datas]?.filter((d, i) => i !== fromIndex);
          return { datas: result.slice(0, toIndex).concat(fromData).concat(result.slice(toIndex)), style: { ...draggingStyle, transform: `translate(${moveState.x - draggingState.x - offsetPositionX}px, ${moveState.y - draggingState.y - offsetPositionY}px)` } };
        }
      }
      return { datas, style: draggingStyle };
    }, [datas, dragging, draggingState, lineNum, moveId, moveIndex, moveState]);

    const showDatas = useMemo(() => nowResult?.datas, [nowResult?.datas]);
    const draggingStyle = useMemo(() => nowResult?.style, [nowResult?.style]);

    useImperativeHandle(ref, () => {
      return {
        showDatas,
      }
    })

    return (
      <>
        { showGroup && (
          <TagGroup weId={`${props.weId || ''}_dbupwn`}
            value={activeClassKey}
            onChange={onChangeActiveClassKey}
            datas={menuGroup}
            prefixCls={`${cardDetailFormClsPrefix}-tags`}
          />
        )}
        {
          showDatas?.length > 0 ? (
            <div className={`${matterAssociationClsPrefix}-design-drag`} ref={containerRef}>
              {
                showDatas?.map((data, index) => {
                  const num = (index + 1) % lineNum;
                  const cls = classnames(`${cardDetailFormClsPrefix}-tags-single ${matterAssociationClsPrefix}-design-drag-single`, {
                    [`${matterAssociationClsPrefix}-design-drag-single-first`]: num === 1,
                    [`${matterAssociationClsPrefix}-design-drag-single-last`]: num === 0,
                    needBorderBottom: lineNum * (rowsLength - 1) > index,
                  })
                  return (<div key={data?.id} className={`${matterAssociationClsPrefix}-design-drag-single-outer`} style={singleStyle} onMouseDown={prevent}>
                    <Single weId={`${props.weId || ''}_85efop@${data.id}`} data={data} className={cls} style={draggingStyle} onMouseDown={onMouseDown} moveId={moveId} moveIndex={index} searchValue={searchValue} />
                  </div>)
                })
              }
              {
                lastColNum > 0 && (
                  <div className={`.${matterAssociationClsPrefix}-design-drag-single-outer`} style={singleStyle}>
                    <div className={`${cardDetailFormClsPrefix}-tags-single ${matterAssociationClsPrefix}-design-drag-single needLeftBorder`} key={`${matterAssociationClsPrefix}-design-drag-single-i`} style={singleStyle} />
                  </div>
                )
              }
            </div>
          ) : (
            <div className={`${cardDetailFormClsPrefix}-tags-empty`}>
              {getLabel('213265','暂无数据')}
            </div>
          )
        }
      </>
    )
  }
));

export default DesignDrag;