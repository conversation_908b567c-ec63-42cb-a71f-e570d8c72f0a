import { BaseProps, Icon, constants } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import React, { useCallback } from "react";
import { memo } from "react";

interface TagFooterProps extends BaseProps {
  weId?: any;
  visible: boolean;
  isMobile?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}
const { cardDetailFormClsPrefix } = constants;

const TagFooter = memo(React.forwardRef<{}, TagFooterProps>(
  (props) => {
    const { visible, isMobile, onVisibleChange } = props;

    const onClick = useCallback(() => { onVisibleChange?.(!visible); }, [onVisibleChange, visible]);
    
    return <div className={`${cardDetailFormClsPrefix}-tags-footer`} onClick={onClick}>
      <span>
        {
          visible ? getLabel('245656','收起') : getLabel('233611','更多及事项关联')
        }
      </span>
      <Icon weId={`${props.weId || ''}_eqpd0q`}
        name={visible ?
          isMobile ? "Icon-up-arrow03" : "Icon-up-arrow01" :
          isMobile ? "Icon-Down-arrow03" : "Icon-Down-arrow01"}
        className={`${cardDetailFormClsPrefix}-tags-footer-icon`}
        size="sm"
      />
    </div>
  }
));

export default TagFooter;