import { AnimateHeight, BaseProps, CardDetailFormStoreType, FormLayoutType, Icon, Input, constants } from "@weapp/ui";
import { observer } from "mobx-react";
import React, { ReactText, useCallback, useContext, useMemo, useRef, useState } from "react";
import { CommonContextProps } from "../../types";
import { CommonContext } from "../../common/utils";
import DesignDrag, { DesignDragRef } from "./DesignDrag";
import { matterAssociationClsPrefix } from "../../../../constants";
import TagGroup from "./TagGroup";
import TagFooter from "./TagFooter";

interface DesignDragContainerProps extends BaseProps {
  store: CardDetailFormStoreType;
  layout: FormLayoutType;
  weId?: any;
}

const { cardDetailFormClsPrefix, EASE_IN_OUT } = constants;
const DesignDragContainer = observer(React.forwardRef<{}, DesignDragContainerProps>(
  (props) => {
    const { config } = useContext<CommonContextProps>(CommonContext);
    const [activeClassKey, updateActiveClassKey] = useState<string>('_all');
    const [searchValue, updateSearchValue] = useState<ReactText>();
    const [visible, updateVisible] = useState<boolean>(true);
    const { classificationSetting, matterConfig, enableSearch } = config || {};
    const { enableClassification, classificationSetting: _groupList1 } = classificationSetting
    //@ts-ignore
    const { layout, store: { groupList: _groupList } } = props;
    const groupList = useMemo(() => _groupList1 || _groupList, [_groupList1, _groupList])
    const menuGroup = useMemo(() => groupList?.filter((g: any) => {
      const show = !(g.isHidden || g.isDisable);
      if (show) {
        const lids = g.layoutIds?.filter((ids: any) => matterConfig?.find((c: { id: any; }) => c.id === ids));
        return lids?.length > 0;
      }
      return false;
    }), [groupList, matterConfig]);

    const onChangeActiveClassKey = useCallback((value: string) => {
      updateActiveClassKey(value);
    }, []);

    const onVisibleChange = useCallback((visible: boolean) => updateVisible(visible), []);
    
    const onSearchValue = useCallback((value: ReactText) => {
      updateSearchValue(value);
      updateActiveClassKey('_all');
    }, []);

    return <div className={`${cardDetailFormClsPrefix} ${matterAssociationClsPrefix}-design-drag-container`}>
      <div className={`${cardDetailFormClsPrefix}-collapse`}>
        <AnimateHeight weId={`${props.weId || ''}_xva4yg`} 
          appear={visible}
          duration={160}
          height={visible ? 'auto' : 0}
          easing={EASE_IN_OUT}
        >
        <div className={`${cardDetailFormClsPrefix}-tags`}>
          { enableSearch && <div className={`${cardDetailFormClsPrefix}-tags-search`}>
              <Input weId={`${props.weId || ''}_7mj7tv`} 
                value={searchValue} 
                prefix={<Icon weId={`${props.weId || ''}_le0mmp`}  size={'s'} name="Icon-search" />}
                onChange={onSearchValue}
                allowClear
              />
            </div>
          }
          <DesignDrag weId={`${props.weId || ''}_39cml0`}
            layout={layout}
            lineNum={parseInt(config?.lineNums || 5)}
            enableClassification={enableClassification}
            activeClassKey={activeClassKey}
            groupList={groupList}
            searchValue={searchValue as string}
            showGroup
            onChangeActiveClassKey={onChangeActiveClassKey}
            menuGroup={menuGroup}
          />
        </div>
        </AnimateHeight>
        <TagFooter weId={`${props.weId || ''}_wd8e3h`} visible={visible} onVisibleChange={onVisibleChange} />
      </div>
    </div>
  }
));

export default DesignDragContainer;