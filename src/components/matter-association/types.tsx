import { ViewProps } from "@weapp/ebdcoms";
import { AnyObj, BaseProps, RadioOptionType, RadioValueType } from "@weapp/ui";

export type ModeType = 'normal' | 'form';
export interface CommonProps extends BaseProps{
  weId?: any;
  config?: AnyObj;
  page?: AnyObj;
  form?: AnyObj;
  formData?: AnyObj; // 表单数据
  id?: string; // 组件唯一id
  isMobile?: boolean; // 移动端标记
  className?: string;
  mode?: ModeType;
  isDesign?: boolean; //设计视图标记
  containerRef?: React.RefObject<HTMLDivElement>;
  events?: any;
  module?: string;
  customParam?: AnyObj;
  formStore?: AnyObj;
  formProps?: AnyObj;
  coms?: any;
  resetLayoutByUserConfig?: (layouts: any[], comId?: string, useCustomConfig?: boolean) => void;
  cardTB?: string;
  onConfigChange?: (config: AnyObj) => void;
}
export interface CommonRef {
  save?: () => void | Promise<any>; // 保存
}
export interface FormConfigProps extends CommonProps {
}

export interface NormalProps extends CommonProps {
}

export interface NormalRef {
  pageRef?: PageConfigRef | null;
}

export interface CommonApiParams {
  objectId?: string; // 事项id
  objectName?: string; // 事项名称
  objectModule?: string; // 事项所属模块
  module?: string; // 模块标记
}
export interface CommonContextProps extends CommonProps {
  save?: (value: any[], preValue: any[]) => Promise<any>; // 保存
  relevance?: AnyObj; // 关联数据
  deleteData?: (data: AnyObj) => Promise<any>; // 单条数据删除
  commonApiParams?: CommonApiParams; // 接口相关参数
  onConfigChange?: (config: AnyObj) => void;
  preView?: boolean; // 预览页面标记
}

export interface LabelPositionProps extends BaseProps {
  weId?: any;
  value: AnyObj;
  onChange: (data: AnyObj) => void;
}

export interface SingleLabelPositionProps extends BaseProps {
  weId?: any;
  value: RadioValueType;
  onChange: (data: RadioValueType, type: string) => void;
  type: string;
  data: RadioOptionType[];
} 

export interface SaveMethodProps extends BaseProps {
  weId?: any;
  value: any;
  onChange: (data: any) => void;
  page?: AnyObj;
}
export interface MatterAssociationViewProps extends ViewProps<AnyObj> {
  events?: any;
  onMount?: (context: any) => void;
}

export interface ClassificationSettingProps extends BaseProps {
  weId?: any;
  value: any;
  onChange: (data: any) => void;
  config: AnyObj;
  page?: AnyObj;
}

export interface PageConfigProps extends BaseProps {
  weId?: any;
}

export interface PageConfigRef {
  doSave?: (config?: any) => any;
}