import { AnyObj } from "@weapp/ui";
import { ModeType } from "../types"

export const getMode = (module: string): ModeType => {
  const wfModule = new Set(["EBUILDERFORM", "FORMDATAREPORT", "WORKFLOW"]);
  const mode: ModeType = wfModule?.has(module) ? 'form' : 'normal';
  return mode
}

/*

1.业务表单
/formreport/forms/formCreateProcess
/sp/formreport/designer/business

2.流程表单
/sp/workflow/formdesigner

3.eb表单
/sp/ebdform/formbuilder

4.页面设计器
/sp/ebddesigner/

*/
export const customHide = (props: AnyObj, client: string) => {
  const pathname = window.location.pathname;
  // const pathnames = ["/sp/workflow/formdesigner", "/sp/ebddesigner"];
  const pathnames = ["/formreport/forms/formCreateProcess", "/sp/formreport/designer/business", "/sp/workflow/formdesigner", "/sp/ebdform/formbuilder", "/sp/ebddesigner"];
  return pathnames?.find((path) => pathname?.indexOf(path) >= 0) ? false : true;
}