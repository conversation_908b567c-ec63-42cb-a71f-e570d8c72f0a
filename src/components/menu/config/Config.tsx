import { getLabel } from "@weapp/utils";
import React, { memo, useCallback, useMemo } from "react";
import { default as MenuDesignBaseConfig, MenuDesignDataType } from "../../../components-share/menu-design";

const MenuDesignConfig = memo(React.forwardRef<any, any>(
  (props) => {
    const { value: _value, onFormChange } = props;

    const onChange = useCallback((value: MenuDesignDataType) => {
      onFormChange?.({ data: value });
    }, [onFormChange]);
    
    const value = useMemo(() => _value || [], [_value]);

    return <MenuDesignBaseConfig value={value || []} onChange={onChange} weId={`${props.weId || ''}_9sjfac`} />
  }
))

const config = {
  /** 表单配置项，同公共组件Form，扩展属性参考下面定义的FormItemProps */
  items: {
    data: {
      label: getLabel('213274','页签设置'),
      labelSpan: 24,
      itemType: 'CUSTOM',
      customRender: (props: any) => (<MenuDesignConfig {...props} weId={`${props.weId || ''}_9sjfac`} />)
    }
  },
};

export default config;