import { useCallback } from "react";
import { getLabel } from "@weapp/utils";
import { FormStore, Select, TimePicker } from "@weapp/ui";
import { FormItems } from "@weapp/ebdcoms";
import {
  overRepeatSelectData,
  restDayConfigSelectData,
  frequencySelectData,
} from "../frequency/constants";

const CustomStartTimeType = (props: any) => {
  const {
    onFormChange,
    config: { startTimeType, startTimeAssociation },
  } = props;
  const seletData = [
    {
      id: "custom",
      content: getLabel("261069", "用户选择"),
    },
    {
      id: "system",
      content: getLabel("261070", "关联字段"),
    },
  ];
  let selectData = props?.getComponentListByKeys?.(["Date"], true) || [];
  selectData = selectData.map((item: any) => {
    item.content = item.title;
    item.id = item.fieldId;
    return item;
  });

  const onStartTimeTypeChange = useCallback(
    (value: any) => {
      onFormChange?.({ startTimeType: value });
    },
    [onFormChange]
  );

  const onAssociationChange = useCallback(
    (value: any) => {
      onFormChange?.({ startTimeAssociation: value });
    },
    [onFormChange]
  );

  return (
    <div style={{ display: "flex" }}>
      <Select
        weId={`${props.weId || ""}_vi6mxf`}
        data={seletData}
        onChange={onStartTimeTypeChange}
        value={startTimeType}
      />
      {startTimeType === "system" && (
        <Select
          weId={`${props.weId || ""}_z5ks7k`}
          data={selectData}
          defaultValue={startTimeAssociation}
          onChange={onAssociationChange}
          style={{ marginLeft: "10px" }}
        />
      )}
    </div>
  );
};

const CustomTriggleTimeType = (props: any) => {
  const {
    onFormChange,
    config: { defTriggerTime, associationTimeTriggle, appointTimeTriggle },
  } = props;
  const seletData = [
    {
      id: "null",
      content: getLabel("261072", "空值"),
    },
    {
      id: "sysTime",
      content: getLabel("261073", "系统时间"),
    },
    {
      id: "appointTime",
      content: getLabel("261074", "指定时间"),
    },
    {
      id: "association",
      content: getLabel("261070", "关联字段"),
    },
  ];
  let selectData = props?.getComponentListByKeys?.(["Time"], true) || [];
  selectData = selectData.map((item: any) => {
    item.content = item.title;
    item.id = item.fieldId;
    return item;
  });

  const onTriggleTypeChange = useCallback(
    (value: any) => {
      onFormChange?.({ defTriggerTime: value });
    },
    [onFormChange]
  );

  const onAppointTimeChange = useCallback(
    (value: any) => {
      onFormChange?.({ appointTimeTriggle: value });
    },
    [onFormChange]
  );

  const onAssociationChange = useCallback(
    (value: any) => {
      onFormChange?.({ associationTimeTriggle: value });
    },
    [onFormChange]
  );

  return (
    <div style={{ display: "flex" }}>
      <Select
        weId={`${props.weId || ""}_vi6mxf`}
        data={seletData}
        onChange={onTriggleTypeChange}
        value={defTriggerTime}
      />
      {defTriggerTime === "appointTime" && (
        <TimePicker
          weId={`${props.weId || ""}_ve9p1g`}
          defaultValue={appointTimeTriggle}
          format="HH:mm"
          onChange={onAppointTimeChange}
          style={{ marginLeft: "10px" }}
        />
      )}
      {defTriggerTime === "association" && (
        <Select
          weId={`${props.weId || ""}_z5ks7k`}
          data={selectData}
          defaultValue={associationTimeTriggle}
          onChange={onAssociationChange}
          style={{ marginLeft: "10px" }}
        />
      )}
    </div>
  );
};

const items: FormItems = {
  comShowFormat: {
    itemType: "SELECT",
    data: [
      {
        id: "tile",
        content: getLabel("261067", "平铺显示"),
      },
      {
        id: "combination",
        content: getLabel("261068", "组合显示"),
      },
    ],
    style: { width: "100%" },
  },
  startTimeType: {
    itemType: "CUSTOM",
    customRender: CustomStartTimeType,
  },
  triggerTimeShowType: {
    itemType: "SELECT",
    data: [
      {
        id: "edit",
        content: getLabel("207020", "编辑"),
      },
      {
        id: "required",
        content: getLabel("261071", "必填"),
      },
      {
        id: "hidden",
        content: getLabel("206984", "隐藏"),
      },
    ],
    style: { width: "100%" },
  },
  defTriggerTime: {
    itemType: "CUSTOM",
    customRender: CustomTriggleTimeType,
  },
  frequencyType: {
    itemType: "SELECT",
    data: [
      {
        id: "all",
        content: getLabel("223932", "全部"),
      },
      {
        id: "appoint",
        content: getLabel("261076", "指定选项"),
      },
    ],
    style: { width: "100%" },
  },
  frequencyTypeSelect: {
    itemType: "SELECT",
    data: frequencySelectData,
    otherParams: {
      multiple: true,
      allowSelectAll: true,
    },
    style: { width: "100%" },
  },
  showOverRepeat: {
    itemType: "SWITCH",
    otherParams: { size: "sm" },
  },
  overRepeatType: {
    itemType: "SELECT",
    data: overRepeatSelectData,
    style: { width: "100%" },
  },
  showRestDayConfig: {
    itemType: "SWITCH",
    otherParams: { size: "sm" },
  },
  restDayConfig: {
    itemType: "SELECT",
    data: restDayConfigSelectData,
    style: { width: "100%" },
  },
  showInitionRule: {
    itemType: "SWITCH",
    otherParams: { size: "sm" },
  },
};

const layout = [
  [
    {
      id: "comShowFormat",
      items: ["comShowFormat"],
      hide: false,
      label: getLabel("261077", "组件显示格式"),
      labelSpan: 24,
      groupId: "field",
    },
  ],
  [
    {
      id: "startTimeType",
      label: getLabel("19504", "开始时间"),
      items: ["startTimeType"],
      hide: false,
      labelSpan: 24,
      groupId: "field",
      helpTip: getLabel(
        "261083",
        "选择“关联字段”时，如关联的字段无数据，则取表单保存时间作为开始时间。"
      ),
    },
  ],
  [
    {
      id: "triggerTimeShowType",
      items: ["triggerTimeShowType"],
      label: getLabel("261093", "【触发时间】显示属性"),
      labelSpan: 24,
      groupId: "field",
      helpTip: getLabel("261085", "选择“隐藏”时，触发时间将读取“开始时间”。"),
    },
  ],
  [
    {
      id: "defTriggerTime",
      items: ["defTriggerTime"],
      label: getLabel("261078", "默认触发时间"),
      labelSpan: 24,
      groupId: "field",
    },
  ],
  [
    {
      id: "frequencyType",
      items: ["frequencyType"],
      label: getLabel("261079", "频率可选项"),
      labelSpan: 24,
      groupId: "field",
      cascadeRulesOuter: {
        frequencyType: {
          all: {
            hide: ["frequencyTypeSelect"],
            show: [],
          },
          appoint: {
            hide: [],
            show: ["frequencyTypeSelect"],
          },
        },
      },
    },
  ],
  [
    {
      id: "frequencyTypeSelect",
      items: ["frequencyTypeSelect"],
      label: "",
      labelSpan: 0,
      groupId: "field",
      hide: false,
    },
  ],
  [
    {
      id: "showOverRepeat",
      items: ["showOverRepeat"],
      label: getLabel("261080", "结束重复"),
      labelSpan: 12,
      align: "right",
      groupId: "field",
      cascadeRulesOuter: {
        showOverRepeat: {
          false: {
            hide: ["overRepeatType"],
            show: [],
          },
          true: {
            hide: [],
            show: ["overRepeatType"],
          },
        },
      },
    },
  ],
  [
    {
      id: "overRepeatType",
      items: ["overRepeatType"],
      label: getLabel("261081", "结束重复默认值"),
      itemType: "SELECT",
      labelSpan: 24,
      groupId: "field",
      data: overRepeatSelectData,
    },
  ],
  [
    {
      id: "showRestDayConfig",
      items: ["showRestDayConfig"],
      label: getLabel("261082", "非工作日处理方式"),
      labelSpan: 16,
      align: "right",
      groupId: "field",
      cascadeRulesOuter: {
        showRestDayConfig: {
          false: {
            hide: ["restDayConfig"],
            show: [],
          },
          true: {
            hide: [],
            show: ["restDayConfig"],
          },
        },
      },
    },
  ],
  [
    {
      id: "restDayConfig",
      items: ["restDayConfig"],
      label: getLabel("261086", "非工作日处理方式默认值"),
      labelSpan: 24,
      groupId: "field",
    },
  ],
  [
    {
      id: "showInitionRule",
      items: ["showInitionRule"],
      label: getLabel("261087", "显示规则说明"),
      labelSpan: 12,
      align: "right",
      groupId: "field",
    },
  ],
];

const FrequencyConfig = {
  items,
  layout,
  fixedArea: false,
  defaultConfig: {
    type: "Frequency",
    comShowFormat: "tile",
    startTimeType: "custom",
    triggerTimeShowType: "edit",
    defTriggerTime: "null",
    frequencyType: "all",
    showOverRepeat: true,
    showRestDayConfig: true,
    showInitionRule: true,
    restDayConfig: "normal",
    overRepeatType: "date",
  },
  references: {
    comShowFormat: ["showInitionRule"],
    showOverRepeat: ["overRepeatType"],
    showRestDayConfig: ["restDayConfig"],
    frequencyType: ["frequencyTypeSelect"],
    triggerTimeShowType: ["defTriggerTime"],
  },
  customHide: (formStore: FormStore, col: any) => {
    const formDatas = formStore?.datas || {};
    const { id } = col;

    if (
      (id === "overRepeatType" && !formDatas.showOverRepeat) ||
      (id === "restDayConfig" && !formDatas.showRestDayConfig) ||
      (id === "frequencyTypeSelect" && formDatas.frequencyType === "all") ||
      (id === "defTriggerTime" && formDatas.triggerTimeShowType === "hidden")
    ) {
      return {
        ...col,
        hide: true,
      };
    }

    if (id === "comShowFormat") {
      if (formDatas.comShowFormat === "combination") {
        formStore?.setItemProps("showInitionRule", {
          disabled: true,
          value: true,
        });
      } else if (formDatas.comShowFormat === "tile") {
        formStore?.setItemProps("showInitionRule", {
          disabled: false,
          value: formDatas.showInitionRule,
        });
      }
    }
    return col;
  },
};

export default FrequencyConfig;
