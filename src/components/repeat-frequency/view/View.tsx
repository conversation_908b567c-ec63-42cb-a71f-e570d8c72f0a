import React, { PureComponent, createRef } from "react";
import { ViewProps } from "@weapp/ebdcoms";
import { observer } from "mobx-react";
import RepeatFrequency from "../frequency/index";
// @ts-ignore
import { IFormItemViewProps, FormStore, IFormComData } from "@weapp/formbuilder";
import { getSysTriggerTime, formSaveAndGetKey } from "../frequency/utils";

@observer
export default class RepeatFrequencyView extends PureComponent<
  IFormItemViewProps,
  any
> {
  repeatFrequency = createRef<any>();
  static defaultOpts = {
    render: { standalone: true },
    form: {
      padding: false,
      onBeforeSave: (value: any, com: IFormComData, formStore: FormStore) => {
        if (com.config?.triggerTimeShowType === "hidden") {
          com.config.triggerTime = getSysTriggerTime();
        }
      },
    },
  };

  constructor(props: any) {
    super(props);
    this.state = {
      // @ts-ignore
      computedConfig: formSaveAndGetKey({ ...props?.config, ...props?.getWidgetValue?.()?.contentObj }),
    };
  }

  listenEbFormEvents = () => {
    const { events } = this.props;
    events?.on?.("field.change", this.fieldChangeListen);
  };

  fieldChangeListen = (fieldId: string) => {
    const { computedConfig } = this.state;
    const {
      formStore,
      config: {
        associationTimeTriggle,
        startTimeAssociation,
        defTriggerTime,
        startTimeType,
      },
    } = this.props;
    if (!formStore) return;
    const comConfig: any = formStore.getWidgetCom(fieldId) || {};
    const id = comConfig.config.fieldId;
    const changedVal = formStore.getFieldValue(fieldId)?.content;

    // 触发时间关联
    if (id === associationTimeTriggle && defTriggerTime === "association") {
      let _computedConfig = {
        ...computedConfig,
        associationTimeTriggleVal: changedVal,
      };
      this.setState({
        computedConfig: _computedConfig,
      });
    }
    // 开始时间关联
    if (id === startTimeAssociation && startTimeType === "system") {
      let _computedConfig = {
        ...computedConfig,
        startTimeAssociationVal: changedVal,
      };
      this.setState({
        computedConfig: _computedConfig,
      });
    }
  };

  onChange = (data: any) => {
    // @ts-ignore
    this.props.onChange?.({ content: data?.initionRule, contentObj: data });
    // @ts-ignore
    this.props.onchangeComplete?.({
      content: data?.initionRule,
      contentObj: data,
    });
  };

  componentDidMount(): void {
    this.listenEbFormEvents();

    const { formStore } = this.props;
    const { computedConfig } = this.state;
    const {
      startTimeType,
      associationTimeTriggle,
      startTimeAssociation,
      defTriggerTime,
    } = computedConfig;
    const obj: any = {};
    let _triggleTime =
      defTriggerTime === "association" && associationTimeTriggle
        ? formStore?.getFieldValue(associationTimeTriggle)?.content
        : "";
    let _startTime =
      startTimeType === "system" && startTimeAssociation
        ? formStore?.getFieldValue(startTimeAssociation)?.content
        : "";

    if (_triggleTime) {
      obj.associationTimeTriggleVal = _triggleTime;
      obj.triggerTime = _triggleTime;
    }

    if (_startTime) {
      obj.startTimeAssociationVal = _startTime;
      obj.startTime = _startTime;
    }

    this.setState({
      computedConfig: {
        ...computedConfig,
        ...obj,
      },
    });
  }

  componentWillUnmount(): void {
    const { events } = this.props;
    events?.off?.("field.change", this.fieldChangeListen);
  }

  render() {
    // @ts-ignore
    const { page, readOnly } = this.props;
    const { computedConfig } = this.state;
    return (
      <RepeatFrequency
        weId={`${this.props.weId || ""}_59ooby`}
        onChange={this.onChange}
        mode="view"
        // @ts-ignore
        data={computedConfig}
        ref={this.repeatFrequency}
        pageInfo={page}
        readOnly={readOnly}
      />
    );
  }
}
