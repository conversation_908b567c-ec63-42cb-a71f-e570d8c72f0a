import React, { Component } from "react";
import {
  MRepeatFrequencyProps,
  RepeatFrequencyDataProps,
  FrequencyRuleType,
  FrequencyOptionData,
  FrequencyType,
} from "./types";
import {
  Form,
  FormStore,
  FormStoreType,
  FormLayoutType,
  FormItemProps,
  FormDatas,
  utils,
  AnyObj,
  MDialog,
  FormItem,
} from "@weapp/ui";
import { request, getLabel } from "@weapp/utils";
import { withRouter } from "react-router-dom";
import { RouteComponentProps } from "react-router";
import {
  frequencySelectData,
  restDayConfigSelectData,
  overRepeatSelectData,
  cascadeRules,
} from "./constants";
import { FrequencyRulePicker, OverRepeatTimesPicker } from "./components_mb";
import {
  initDefFrequencyRuleData,
  compareFrequencyRuleData,
  getFormatFrequencyData,
  definitionRule,
  hideTriggerTime,
  validateFrequency,
  initFrequencySelectData,
  otherShowHideLayout,
  getSysTriggerTime,
} from "./utils/index";
import { mRepeatFrequencyClsPrefix } from "../../../constants/index";

const { Item } = FormItem;

const { formatParentPath, isEmpty, isEqual } = utils;
const { ButtonGroup } = MDialog;

interface MRepeatFrequencyState {
  data: RepeatFrequencyDataProps;
  customPickerVisible: boolean;
  ruleChanged: boolean;
  innerDefinition: string;
}

class MRepeatFrequency extends Component<
  MRepeatFrequencyProps & Partial<RouteComponentProps>,
  MRepeatFrequencyState
> {
  formStore: FormStoreType;
  comFormStore: FormStoreType;
  constructor(props: MRepeatFrequencyProps) {
    super(props);
    this.state = {
      data: props.data,
      customPickerVisible: false,
      ruleChanged: false,
      innerDefinition: "",
    };
    this.formStore = new FormStore();
    this.comFormStore = new FormStore();
  }

  componentDidMount() {
    const {
      frequencyTypeSelect,
      frequencyType,
      startTimeType,
      startTimeAssociationVal,
      frequency,
      startTime,
      triggerTime,
      defTriggerTime,
      appointTimeTriggle,
      associationTimeTriggleVal,
    } = this.state.data;
    let currentSelectFrequencyType = frequency
      ? frequency
      : frequencyType === "all"
      ? "byHour"
      : frequencyTypeSelect?.[0] || "";
    let currentStartTime = startTime
      ? startTime
      : startTimeType === "system"
      ? startTimeAssociationVal || ""
      : getSysTriggerTime();
    let currentTriggleTime = triggerTime
      ? triggerTime
      : defTriggerTime === "sysTime"
      ? getSysTriggerTime()
      : defTriggerTime === "appointTime"
      ? appointTimeTriggle || ""
      : defTriggerTime === "association"
      ? associationTimeTriggleVal || ""
      : "";
    this.setState(
      {
        data: {
          ...this.state.data,
          frequency: currentSelectFrequencyType as FrequencyType,
          startTime: currentStartTime,
          triggerTime: currentTriggleTime,
        },
      },
      () => {
        this.initForm();
        this.initComForm();
      }
    );
  }

  componentDidUpdate(
    prevProps: Readonly<MRepeatFrequencyProps>,
    prevState: Readonly<MRepeatFrequencyState>,
    snapshot?: any
  ): void {
    const {
      startTimeType,
      triggerTimeShowType,
      showInitionRule,
      showOverRepeat,
      showRestDayConfig,
      comShowFormat,
      restDayConfig,
      overRepeatType,
      frequencyType,
      frequencyTypeSelect,
      defTriggerTime,
      appointTimeTriggle,
      startTimeAssociation,
      startTimeAssociationVal,
      associationTimeTriggleVal,
      disableEdit,
      readOnly,
    } = this.props.data;
    let currentSelectFrequencyType =
      frequencyType === "all" ? "byHour" : frequencyTypeSelect?.[0] || "";
    // eb配置项
    // 开始时间  关联数据
    if (
      prevProps?.data?.startTimeType !== startTimeType ||
      prevProps?.data?.startTimeAssociation !== startTimeAssociation ||
      prevProps?.data?.startTimeAssociationVal !== startTimeAssociationVal
    ) {
      if (comShowFormat === "tile") {
        this.formStore.setHide("startTime", startTimeType === "system");
        this.formStore.updateDatas({
          startTime:
            startTimeType === "system"
              ? startTimeAssociationVal || ""
              : getSysTriggerTime(),
          startTimeAssociationVal: startTimeAssociationVal || "",
        });
      } else {
        this.comFormStore.updateDatas({
          startTime:
            startTimeType === "system"
              ? startTimeAssociationVal || ""
              : getSysTriggerTime(),
          startTimeAssociationVal: startTimeAssociationVal || "",
        });
      }
    }
    // 组件显示格式变化
    if (prevProps?.data?.comShowFormat !== comShowFormat) {
      this.formStore.setHide("startTime", comShowFormat === "combination");
    }

    // 结束重复
    if (prevProps?.data?.showOverRepeat !== showOverRepeat) {
      this.formStore.setHide("overRepeatType", !showOverRepeat);
    }

    // overRepeatType : never date times
    if (prevProps?.data?.overRepeatType !== overRepeatType) {
      this.formStore.updateDatas({ overRepeatType: overRepeatType || "" });
      const overRepeatTypeItems = ["overRepeatType"];
      if (overRepeatType !== "never") {
        overRepeatTypeItems.push(
          overRepeatType === "times" ? "overRepeatTimes" : "overRepeatDate"
        );
      }

      this.formStore.setLayoutProps("overRepeatType", {
        items: overRepeatTypeItems,
      });
    }

    // 非工作日处理方式
    if (prevProps?.data?.showRestDayConfig !== showRestDayConfig) {
      this.formStore.setHide("restDayConfig", !showRestDayConfig);
    }
    // restDayConfig : normal nextWorkDay never
    if (prevProps?.data?.restDayConfig !== restDayConfig) {
      this.formStore.updateDatas({ restDayConfig: restDayConfig || "" });
    }

    // 频率可选项: all, appoint
    if (
      prevProps?.data?.frequencyType !== frequencyType ||
      !isEqual(prevProps?.data?.frequencyTypeSelect, frequencyTypeSelect)
    ) {
      let _data = initFrequencySelectData(this.props.data);

      this.formStore.setItemProps("frequency", {
        data: _data,
      });

      this.formStore.updateDatas({
        frequency: currentSelectFrequencyType,
      });

      this.setState({
        data: {
          ...this.state.data,
          frequency: currentSelectFrequencyType as FrequencyType,
        },
      });

      frequencySelectData.map((item: FrequencyOptionData) => {
        this.formStore.setHide(
          item.key,
          item.id !== currentSelectFrequencyType
        );
      });

      // 需要同步切换 触发时间
      let hideStatus =
        triggerTimeShowType === "hidden"
          ? true
          : currentSelectFrequencyType === "byMinute" ||
            currentSelectFrequencyType === "byHour"
          ? true
          : false;
      this.formStore.setHide("triggerTime", hideStatus);
      this.formStore.setItemProps("triggerTime", {
        required: triggerTimeShowType === "required",
      });
    }

    // 触发时间
    if (prevProps?.data?.triggerTimeShowType !== triggerTimeShowType) {
      if (
        currentSelectFrequencyType !== "byMinute" &&
        currentSelectFrequencyType !== "byHour"
      ) {
        this.formStore.setHide("triggerTime", triggerTimeShowType === "hidden");
        this.formStore.setItemProps("triggerTime", {
          required: triggerTimeShowType === "required",
        });
        this.formStore.updateDatas({
          triggerTimeShowType: triggerTimeShowType || "",
        });
      }
    }

    // 默认触发时间 除了系统时间统一先置空
    if (prevProps?.data?.defTriggerTime !== defTriggerTime) {
      this.formStore.updateDatas({
        triggerTime:
          defTriggerTime === "sysTime"
            ? getSysTriggerTime()
            : defTriggerTime === "appointTime"
            ? appointTimeTriggle || ""
            : defTriggerTime === "association"
            ? associationTimeTriggleVal || ""
            : "",
        defTriggerTime: defTriggerTime || "",
      });
    }

    // 指定默认触发时间
    if (prevProps?.data?.appointTimeTriggle !== appointTimeTriggle) {
      this.formStore.updateDatas({
        triggerTime: appointTimeTriggle || "",
      });
    }

    // 关联触发时间
    // 关联数据特殊处理，手动更新表单
    if (
      prevProps?.data?.associationTimeTriggleVal !==
        associationTimeTriggleVal ||
      prevProps?.data?.startTimeAssociationVal !== startTimeAssociationVal
    ) {
      this.formStore.updateDatas({
        triggerTime: associationTimeTriggleVal || "",
        associationTimeTriggleVal: associationTimeTriggleVal || "",
      });
      this.updateFormData();
    }

    // display 、readOnly 、 required 、disableEdit
    if (
      prevProps?.data?.disableEdit !== disableEdit ||
      prevProps?.data?.readOnly !== readOnly
    ) {
      const target: string[] = [
        "startTime",
        "triggerTime",
        "frequency",
        "overRepeatType",
        "overRepeatDate",
        "overRepeatTimes",
        "restDayConfig",
        "initionRule",
        "frequencyRuleByDay",
        "frequencyRuleByHour",
        "frequencyRuleByMinute",
        "frequencyRuleByMonth",
        "frequencyRuleByQuarter",
        "frequencyRuleByWeek",
        "frequencyRuleByYear",
      ];
      target.map((item: string) => {
        this.formStore.setItemProps(item, {
          readOnly: disableEdit,
        });
      });
    }
  }

  initComForm = () => {
    const { data } = this.state;
    const { readOnly } = this.props;
    const { disableEdit: _disableEdit } = this.props.data;
    const disableEdit = readOnly || _disableEdit;
    const items = {
      startTime: {
        itemType: "DATETIMEPICKER",
        isHoldRight: true,
        required: true,
        readOnly: disableEdit,
        otherParams: {
          timeFormat: "HH:mm",
          allowClear: false,
          style: {
            width: "100%",
          },
        },
      },
      // 规则释义
      initionRule: {
        itemType: "CUSTOM",
        customRender: (data: any) => (
          <Item
            weId={`${this.props.weId || ""}_l53uuy`}
            placeholder={getLabel("261563", "请选择")}
            onClick={this.initDetail}
          >
            {getLabel("236754", "规则说明")}：{data.props.value}
          </Item>
        ),
      },
    } as FormItemProps;
    const layout = [
      [
        {
          id: "startTime",
          label: getLabel("19504", "开始时间"),
          items: ["startTime"],
          labelSpan: 6,
          hide: false,
        },
      ],
      [
        {
          id: "initionRule",
          label: getLabel("236750", "定时频率"),
          items: ["initionRule"],
          labelSpan: 24,
          span: 24,
          hide: false,
          wrapClassName: `${mRepeatFrequencyClsPrefix}-form-item-container`,
        },
      ],
    ];
    this.comFormStore.initForm({
      data: { ...data },
      items,
      layout,
      groups: [],
    });
    this.getDefinitionRule();
  };

  initFrequencyLayout = () => {
    const { frequency } = this.state.data;
    let array: any = [];
    frequencySelectData.map((item: FrequencyOptionData, index: number) => {
      array[index] = [
        {
          id: [item.key],
          label: "",
          items: [item.key],
          labelSpan: 0,
          hide: frequency !== item.id || frequency === "never",
        },
      ];
    });
    return array;
  };

  overRepeatTimesChange = (value: React.ReactText) => {
    this.formStore.updateDatas({
      overRepeatTimes: value,
    });
    this.setState({
      data: {
        ...this.state.data,
        overRepeatTimes: value,
      },
    });
    this.getDefinitionRule();
  };

  initFormItems = () => {
    const { prefixCls, readOnly } = this.props;
    const {
      disableEdit: _disableEdit,
      defTriggerTime,
      overRepeatTimes,
    } = this.props.data;
    const disableEdit = readOnly || _disableEdit;
    return {
      startTime: {
        itemType: "DATETIMEPICKER",
        readOnly: disableEdit,
        isHoldRight: true,
        otherParams: {
          timeFormat: "HH:mm",
          allowClear: false,
        },
      },
      triggerTime: {
        itemType: "DATETIMEPICKER",
        readOnly: disableEdit || defTriggerTime === "association",
        isHoldRight: true,
        otherParams: {
          timeFormat: "HH:mm",
          type: "time",
        },
      },
      frequency: {
        itemType: "SELECT",
        readOnly: disableEdit,
        data: initFrequencySelectData(this.props.data),
        isHoldRight: true,
      },
      // 结束重复
      overRepeatType: {
        itemType: "SELECT",
        readOnly: disableEdit,
        data: overRepeatSelectData,
        isHoldRight: true,
      },
      // 结束重复（按具体日期）
      overRepeatDate: {
        itemType: "DATETIMEPICKER",
        readOnly: disableEdit,
        otherParams: {
          timeFormat: "HH:mm:ss",
        },
        isHoldRight: true,
      },
      // 结束重复（按执行次数）
      overRepeatTimes: {
        itemType: "CUSTOM",
        readOnly: disableEdit,
        customRender: (props: any) => (
          <OverRepeatTimesPicker
            weId={`${this.props.weId || ""}_y4r7vu`}
            value={overRepeatTimes}
            prefixCls={prefixCls}
            onChange={this.overRepeatTimesChange}
            readOnly={disableEdit}
          />
        ),
        isHoldRight: true,
      },
      // 非工作日处理方式
      restDayConfig: {
        itemType: "SELECT",
        readOnly: disableEdit,
        isHoldRight: true,
        data: restDayConfigSelectData,
      },
      // 根据 定时评率 切换对应的配置rule
      ...this.initFrequencyItems(),
    } as FormItemProps;
  };

  initFormLayout = () => {
    const {
      showOverRepeat,
      showRestDayConfig,
      overRepeatType,
      frequency,
      triggerTimeShowType,
    } = this.state.data;
    // 初始化回显 “结束重复” 行内联动字段
    const overRepeatTypeItems =
      overRepeatType === "times"
        ? ["overRepeatType", "overRepeatTimes"]
        : overRepeatType === "date"
        ? ["overRepeatType", "overRepeatDate"]
        : ["overRepeatType"];
    const layout: Array<FormLayoutType> = [
      [
        {
          id: "startTime",
          label: getLabel("19504", "开始时间"),
          items: ["startTime"],
          labelSpan: 10,
          hide: frequency === "never",
        },
      ],
      [
        {
          id: "triggerTime",
          label: getLabel("236749", "触发时间"),
          items: ["triggerTime"],
          labelSpan: 10,
          hide:
            hideTriggerTime(frequency, triggerTimeShowType || "") ||
            frequency === "never",
        },
      ],
      [
        {
          id: "frequency",
          label: getLabel("236750", "定时频率"),
          items: ["frequency"],
          labelSpan: 6,
          hide: false,
          cascadeRulesOuter: {
            frequency: {
              ...this.initFrequencyCascadeRulesOuter(),
            },
          },
        },
      ],
      ...this.initFrequencyLayout(),
      [
        {
          id: "overRepeatType",
          label: getLabel("236751", "结束重复"),
          items: overRepeatTypeItems,
          labelSpan: 10,
          hide: !showOverRepeat || frequency === "never",
        },
      ],
      [
        {
          id: "restDayConfig",
          label: getLabel("236752", "非工作日处理方式"),
          items: ["restDayConfig"],
          labelSpan: 10,
          hide: !showRestDayConfig || frequency === "never",
        },
      ],
    ];
    return layout;
  };

  //初始化form表单
  initForm = () => {
    const { data } = this.state;
    this.formStore.initForm({
      data: { ...data },
      items: this.initFormItems(),
      layout: this.initFormLayout(),
      groups: [],
    });
    this.getDefinitionRule();
  };

  initFrequencyCascadeRulesOuter = () => {
    let target: any = {},
      customKeyArr: string[] = [];
    frequencySelectData.map((item: FrequencyOptionData) => {
      customKeyArr.push(item.key);
    });
    frequencySelectData.map((item: FrequencyOptionData) => {
      const hideArr = customKeyArr.filter((i) => i !== item.key);
      target[item.id] = {
        show: [item.key, ...otherShowHideLayout(this.state.data, item.id)],
        hide: [...hideArr, ...otherShowHideLayout(this.state.data, item.id)],
      };
    });
    target.never = {
      hide: [
        ...target.never.hide,
        "startTime",
        "triggerTime",
        "overRepeatType",
        "overRepeatDate",
        "overRepeatTimes",
        "restDayConfig",
        "initionRule",
      ],
    };
    return target;
  };

  // 频率规则发生改变
  frequencyRuleChange = (data: { frequencyRule: FrequencyRuleType }) => {
    const { frequencyRule } = data;
    this.formStore.updateDatas({
      frequencyRule: {
        ...frequencyRule,
      },
    });
    this.updateInitionRule();
    this.updateFormData();
  };

  initFrequencyItems = () => {
    const { readOnly, prefixCls } = this.props;
    const {
      frequencyRule,
      frequency,
      disableEdit: _disableEdit,
    } = this.state.data;
    const disableEdit = readOnly || _disableEdit;
    let target = {};
    const initData = initDefFrequencyRuleData();
    frequencySelectData.map((item: FrequencyOptionData) => {
      // @ts-ignore
      target[item.key] = {
        itemType: "CUSTOM",
        readOnly: disableEdit,
        customRender: (props: any) => {
          return (
            <FrequencyRulePicker
              prefixCls={prefixCls}
              weId={`${this.props.weId || ""}_xecvx1`}
              frequency={item.id}
              readOnly={disableEdit}
              frequencyRule={
                item.id === frequency
                  ? compareFrequencyRuleData(initData, frequencyRule)
                  : initData
              }
              onChange={this.frequencyRuleChange}
            />
          );
        },
      };
    });
    return target;
  };

  getItems = (rule: Array<string>) => {
    let tempRule: Array<string> = [];
    const { initDatas } = this.formStore;
    rule.forEach((ru) => {
      tempRule.push(ru);
      if (cascadeRules[ru]) {
        const initValue = initDatas[ru];
        if (
          typeof initValue === "string" &&
          initValue &&
          cascadeRules[ru][initValue]
        ) {
          tempRule = tempRule.concat(cascadeRules[ru][initValue]);
        }
      }
    });
    return tempRule;
  };

  formDataChange = (value?: FormDatas, otherParams?: any) => {
    const { defTriggerTime, associationTimeTriggleVal } = this.props.data;
    if (value) {
      const key = Object.keys(value)[0];
      if (cascadeRules[key]) {
        const dataKey: any = value[key];
        const rule =
          cascadeRules[key] instanceof Array
            ? isEmpty(value[key])
              ? []
              : cascadeRules[key]
            : cascadeRules[key][dataKey];
        if (rule) {
          const { items: formItems, getDefaultValue } = this.formStore;
          const {
            layoutConfig: { id, items: oldItems },
          } = otherParams;
          const beforeItems =
            oldItems.length > 1
              ? oldItems.slice(0, oldItems.indexOf(key) + 1)
              : oldItems;
          const items = this.getItems(rule); // 联动带出字段
          // 初始化联动字段值（存在通用的字段）
          items.forEach((it) => {
            this.formStore.setItemProps(it, {
              value: getDefaultValue(formItems[it].itemType, it),
            });
          });
          // 字段联动显示
          this.formStore.setLayoutProps(id, {
            items: [...beforeItems, ...items],
          });
        }
      }

      if (key === "frequency" && defTriggerTime === "association") {
        this.formStore.updateDatas({
          triggerTime: associationTimeTriggleVal || "",
        });
      }
    }
    this.updateInitionRule();
    this.updateFormData();
  };

  // 获取初始化的数据
  getInitComputedData = () => {
    const { ruleChanged, data } = this.state;
    return ruleChanged ? this.formStore.getFormDatas() : data;
  };

  getFormData = () => {
    return getFormatFrequencyData(this.getInitComputedData());
  };

  // 获取规则获取的后台时间节点数据
  getCountData = async (otherParams?: AnyObj) => {
    const initComputedData = this.getInitComputedData();
    if (initComputedData?.frequency === "never") {
      return {
        triggerPreview: [],
        playDayList: [],
        definitionRule: "",
      };
    }
    const target = getFormatFrequencyData(initComputedData);
    const { basicRule, restDayConfig, formData, triggerTime } = target || {};
    const res = await request({
      url: "/api/component/base/rescurrence/preview",
      method: "post",
      data: {
        basicRule,
        restDayConfig,
        formData: JSON.stringify(formData),
        triggerTime,
        ...otherParams,
      },
    });
    if (res.code === 200) {
      return {
        ...res.data,
        definitionRule: definitionRule(this.getInitComputedData()),
      };
    }
    return {
      triggerPreview: [],
      playDayList: [],
      definitionRule: "",
    };
  };

  // 释义展示内容
  customInputRender = () => {
    return <div>{definitionRule(this.getInitComputedData())}</div>;
  };

  initDetail = () => {
    this.formStore.setHide("startTime", true);
    let parentPath = formatParentPath(this.props as any);
    this.props.history && this.props.history.push(`${parentPath}/frequencydetail`);
  };

  frequencyChange = (val: any) => {
    let parentPath = formatParentPath(this.props as any);
    this.props.history && this.props?.history.push(`${parentPath}/frequencydetail`);
  };

  handleCancel = () => {
    this.setState({
      ruleChanged: false,
    });

    this.formStore.updateDatas({
      ...this.state.data,
    });
    this.props.history && this.props?.history.go(-1);
  };

  handleConfirm = () => {
    const data = this.formStore.getFormDatas();
    if (validateFrequency(data)) {
      this.setState({
        data: {
          ...data,
        },
        ruleChanged: true,
      });
      this.getDefinitionRule();
      this.props.history && this.props?.history.go(-1);
    }
  };

  showMDialogByRoute = () => {
    let parentPath = formatParentPath(this.props as any);
    this.props.history && this.props?.history.push(`${parentPath}/route`);
  };

  // 数据更新至表单
  updateFormData = () => {
    const {
      mode,
      onChange,
      data: { comShowFormat },
    } = this.props;
    if (mode === "view") {
      if (comShowFormat === "combination") {
        let data = this.formStore.getFormDatas();
        let comData = this.comFormStore.getFormDatas();
        onChange({ ...data, startTime: comData.startTime });
      } else {
        let data = this.formStore.getFormDatas();
        onChange(data);
      }
    }
  };

  getDefinitionRule = () => {
    let val = definitionRule(this.formStore.getFormDatas());
    this.formStore.updateDatas({
      initionRule: val,
    });
    // let comVal = definitionRule(this.comFormStore.getFormDatas());
    this.comFormStore.updateDatas({
      initionRule: val,
    });
    this.updateFormData();
  };

  updateInitionRule = () => {
    if (this.props.data.showInitionRule) {
      this.setState({
        innerDefinition: definitionRule(this.formStore.getFormDatas()),
      });
    }
  };

  render() {
    const { innerDefinition } = this.state;
    const {
      data: { comShowFormat, showInitionRule, disableEdit, initionRule },
      readOnly,
      prefixCls,
    } = this.props;
    const containerClassName = `${mRepeatFrequencyClsPrefix}-container`;
    return (
      <>
        {comShowFormat === "tile" ? (
          // 平铺模式
          <>
            <Form
              weId={`${this.props.weId || ""}_xwf3nn`}
              isMobile
              store={this.formStore}
              onChange={this.formDataChange}
              className={containerClassName}
            />
            {showInitionRule && (
              <p className={`${prefixCls}-router-dialog-content-definition`}>
                {getLabel("236754", "规则说明")}：{innerDefinition || initionRule}
              </p>
            )}
          </>
        ) : (
          // 展开模式
          <Form
            weId={`${this.props.weId || ""}_k80seh`}
            isMobile
            store={this.comFormStore}
            onChange={this.formDataChange}
            className={containerClassName}
          />
        )}
        <MDialog
          weId={`${this.props.weId || ""}_vu2jd9`}
          destroyOnClose
          path={`${formatParentPath(this.props as any)}/frequencydetail`}
          isRouteLayout={true}
          title={getLabel("236753", "设置频率")}
        >
          <div className={`${prefixCls}-router-dialog-content`}>
            <Form
              weId={`${this.props.weId || ""}_yud5rh`}
              isMobile
              store={this.formStore}
              onChange={this.formDataChange}
              className={containerClassName}
            />
            {showInitionRule && (
              <p className={`${prefixCls}-router-dialog-content-definition`}>
                {getLabel("236754", "规则说明")}：{innerDefinition || initionRule}
              </p>
            )}
          </div>
          <ButtonGroup
            weId={`${this.props.weId || ""}_tgo0wt`}
            datas={[
              {
                id: "cancel",
                content: getLabel("52130", "取消"),
                onClick: this.handleCancel,
              },
              {
                id: "confirm",
                content: getLabel("31291", "确定"),
                onClick: this.handleConfirm,
                disabled: readOnly || disableEdit,
                type: "primary",
              },
            ]}
          />
        </MDialog>
      </>
    );
  }
}

// @ts-ignores
export default withRouter(MRepeatFrequency);
