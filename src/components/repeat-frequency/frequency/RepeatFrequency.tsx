import React, { Component } from "react";
import {
  RepeatFrequencyProps,
  RepeatFrequencyDataProps,
  FrequencyRuleType,
  FrequencyOptionData,
  FrequencyType,
} from "./types";
import {
  Form,
  FormStoreType,
  FormStore,
  FormItemProps,
  FormDatas,
  utils,
  AnyObj,
  Dialog,
  Button,
} from "@weapp/ui";
import { request, getLabel } from "@weapp/utils";
import {
  restDayConfigSelectData,
  overRepeatSelectData,
  cascadeRules,
  frequencySelectData,
} from "./constants";
import { FrequencyRulePicker, OverRepeatTimesPicker } from "./components";
import {
  initDefFrequencyRuleData,
  getFormatFrequencyData,
  compareFrequencyRuleData,
  hideTriggerTime,
  definitionRule,
  initFrequencySelectData,
  otherShowHideLayout,
  getSysTriggerTime,
} from "./utils/index";
import { repeatFrequencyClsPrefix } from "../../../constants/index";

const { isEmpty, isEqual } = utils;

interface RepeatFrequencyState {
  data: RepeatFrequencyDataProps;
  comDiaVisible: boolean;
  initionRule: string;
}

export default class RepeatFrequency extends Component<
  RepeatFrequencyProps,
  RepeatFrequencyState
> {
  formStore: FormStoreType;
  comFormStore: FormStoreType;
  constructor(props: RepeatFrequencyProps) {
    super(props);
    this.state = {
      initionRule: "",
      data: props.data,
      comDiaVisible: false,
    };
    this.formStore = new FormStore();
    this.comFormStore = new FormStore();
  }

  componentDidMount() {
    const {
      frequencyTypeSelect,
      frequencyType,
      startTimeType,
      startTimeAssociationVal,
      frequency,
      startTime,
      triggerTime,
      defTriggerTime,
      appointTimeTriggle,
      associationTimeTriggleVal,
    } = this.state.data;
    let currentSelectFrequencyType = frequency
      ? frequency
      : frequencyType === "all"
      ? "byHour"
      : frequencyTypeSelect?.[0] || "";
    let currentStartTime = startTime
      ? startTime
      : startTimeType === "system"
      ? startTimeAssociationVal || ""
      : getSysTriggerTime();
    let currentTriggleTime = triggerTime
      ? triggerTime
      : defTriggerTime === "sysTime"
      ? getSysTriggerTime()
      : defTriggerTime === "appointTime"
      ? appointTimeTriggle || ""
      : defTriggerTime === "association"
      ? associationTimeTriggleVal || ""
      : "";
    this.setState(
      {
        data: {
          ...this.state.data,
          frequency: currentSelectFrequencyType as FrequencyType,
          startTime: currentStartTime,
          triggerTime: currentTriggleTime,
        },
      },
      () => {
        this.initForm();
        this.initComForm();
      }
    );
  }

  componentDidUpdate(
    prevProps: Readonly<RepeatFrequencyProps>,
    prevState: Readonly<RepeatFrequencyState>,
    snapshot?: any
  ): void {
    const {
      startTimeType,
      triggerTimeShowType,
      showInitionRule,
      showOverRepeat,
      showRestDayConfig,
      comShowFormat,
      restDayConfig,
      overRepeatType,
      frequencyType,
      frequencyTypeSelect,
      defTriggerTime,
      appointTimeTriggle,
      startTimeAssociation,
      startTimeAssociationVal,
      associationTimeTriggleVal,
      disableEdit,
      readOnly,
    } = this.props.data;
    let currentSelectFrequencyType =
      frequencyType === "all" ? "byHour" : frequencyTypeSelect?.[0] || "";
    // eb配置项
    // 开始时间  关联数据
    if (
      prevProps?.data?.startTimeType !== startTimeType ||
      prevProps?.data?.startTimeAssociation !== startTimeAssociation ||
      prevProps?.data?.startTimeAssociationVal !== startTimeAssociationVal
    ) {
      if (comShowFormat === 'tile'){
        this.formStore.setHide("startTime", startTimeType === "system");
        this.formStore.updateDatas({
          startTime:
            startTimeType === "system"
              ? startTimeAssociationVal || ""
              : getSysTriggerTime(),
          startTimeAssociationVal: startTimeAssociationVal || "",
        });
      } else {
        this.comFormStore.updateDatas({
          startTime:
            startTimeType === "system"
              ? startTimeAssociationVal || ""
              : getSysTriggerTime(),
          startTimeAssociationVal: startTimeAssociationVal || "",
        });
      }
    }

    // 组件显示格式变化
    if (prevProps?.data?.comShowFormat !== comShowFormat) {
      this.formStore.setHide("startTime", comShowFormat === "combination");
    }
    // 释义
    if (prevProps?.data?.showInitionRule !== showInitionRule) {
      this.formStore.setHide("initionRule", !showInitionRule);
    }
    // 结束重复
    if (prevProps?.data?.showOverRepeat !== showOverRepeat) {
      this.formStore.setHide("overRepeatType", !showOverRepeat);
    }

    // overRepeatType : never date times
    if (prevProps?.data?.overRepeatType !== overRepeatType) {
      this.formStore.updateDatas({ overRepeatType: overRepeatType || "" });
      const overRepeatTypeItems = ["overRepeatType"];
      if (overRepeatType !== "never") {
        overRepeatTypeItems.push(
          overRepeatType === "times" ? "overRepeatTimes" : "overRepeatDate"
        );
      }

      this.formStore.setLayoutProps("overRepeatType", {
        items: overRepeatTypeItems,
      });
    }

    // 非工作日处理方式
    if (prevProps?.data?.showRestDayConfig !== showRestDayConfig) {
      this.formStore.setHide("restDayConfig", !showRestDayConfig);
    }
    // restDayConfig : normal nextWorkDay never
    if (prevProps?.data?.restDayConfig !== restDayConfig) {
      this.formStore.updateDatas({ restDayConfig: restDayConfig || "" });
    }

    // 频率可选项: all, appoint
    if (
      prevProps?.data?.frequencyType !== frequencyType ||
      !isEqual(prevProps?.data?.frequencyTypeSelect, frequencyTypeSelect)
    ) {
      let _data = initFrequencySelectData(this.props.data);

      this.formStore.setItemProps("frequency", {
        data: _data,
      });

      this.formStore.updateDatas({
        frequency: currentSelectFrequencyType,
      });

      this.setState({
        data: {
          ...this.state.data,
          frequency: currentSelectFrequencyType as FrequencyType,
        },
      });

      this.formStore.setItemProps("triggerTime", {
        required: triggerTimeShowType === "required",
      });


      let target: any = {},
      customKeyArr: string[] = [];
      frequencySelectData.map((item: FrequencyOptionData) => {
        customKeyArr.push(item.key);
      });
      frequencySelectData.map((item: FrequencyOptionData) => {
        const hideArr = customKeyArr.filter((i) => i !== item.key);
        target[item.id] = {
          show: hideTriggerTime(item.id, triggerTimeShowType)
            ? [item.key, 'frequency', ...otherShowHideLayout(this.props.data, item.id)]
            : [item.key, 'frequency', "triggerTime", ...otherShowHideLayout(this.props.data, item.id)],
          hide: hideTriggerTime(item.id, triggerTimeShowType)
            ? [...hideArr, "triggerTime", ...otherShowHideLayout(this.props.data, item.id)]
            : hideArr,
        };
      });
      target.never = {
        hide: [
          ...target.never.hide,
          "startTime",
          "triggerTime",
          "overRepeatType",
          "overRepeatDate",
          "overRepeatTimes",
          "restDayConfig",
          "initionRule",
        ],
        show: ["frequency"]
      };

      if (currentSelectFrequencyType) {
        if (target[currentSelectFrequencyType as string].hide) {
          target[currentSelectFrequencyType as string].hide.map((item: string) => {
            this.formStore.setHide(item, true)
          })
        }
        if (target[currentSelectFrequencyType as string].show) {
          target[currentSelectFrequencyType as string].show.map((item: string) => {
            this.formStore.setHide(item, false)
          })
        }
      }

      // 没有默认值的场景下 全部置空
      if (!currentSelectFrequencyType) {
        target.never.hide.map((item: string) => {
          this.formStore.setHide(item, true)
        })

        this.formStore.setHide('frequency', true)
      }
    }

    // 触发时间
    if (prevProps?.data?.triggerTimeShowType !== triggerTimeShowType) {
      if (
        currentSelectFrequencyType !== "byMinute" &&
        currentSelectFrequencyType !== "byHour"
      ) {
        this.formStore.setHide("triggerTime", triggerTimeShowType === "hidden");
        this.formStore.setItemProps("triggerTime", {
          required: triggerTimeShowType === "required",
        });
        this.formStore.updateDatas({
          triggerTimeShowType: triggerTimeShowType || "",
        });
      }
    }

    // 默认触发时间 除了系统时间统一先置空
    if (prevProps?.data?.defTriggerTime !== defTriggerTime) {
      this.formStore.updateDatas({
        triggerTime:
          defTriggerTime === "sysTime"
            ? getSysTriggerTime()
            : defTriggerTime === "appointTime"
            ? appointTimeTriggle || ""
            : defTriggerTime === "association"
            ? associationTimeTriggleVal || ""
            : "",
        defTriggerTime: defTriggerTime || "",
      });
    }

    // 指定默认触发时间
    if (prevProps?.data?.appointTimeTriggle !== appointTimeTriggle) {
      this.formStore.updateDatas({
        triggerTime: appointTimeTriggle || "",
      });
    }

    // 关联触发时间
    // 关联数据特殊处理，手动更新表单
    if (
      prevProps?.data?.associationTimeTriggleVal !==
        associationTimeTriggleVal ||
      prevProps?.data?.startTimeAssociationVal !== startTimeAssociationVal
    ) {
      this.formStore.updateDatas({
        triggerTime: associationTimeTriggleVal || "",
        associationTimeTriggleVal: associationTimeTriggleVal || "",
      });
      this.updateFormData();
    }

    // display 、readOnly 、 required 、disableEdit
    if (
      prevProps?.data?.disableEdit !== disableEdit ||
      prevProps?.data?.readOnly !== readOnly
    ) {
      const target: string[] = [
        "startTime",
        "triggerTime",
        "frequency",
        "overRepeatType",
        "overRepeatDate",
        "overRepeatTimes",
        "restDayConfig",
        "initionRule",
        "frequencyRuleByDay",
        "frequencyRuleByHour",
        "frequencyRuleByMinute",
        "frequencyRuleByMonth",
        "frequencyRuleByQuarter",
        "frequencyRuleByWeek",
        "frequencyRuleByYear",
      ];
      target.map((item: string) => {
        this.formStore.setItemProps(item, {
          readOnly: readOnly || disableEdit,
        });
      });
    }

    this.getDefinitionRule();
  }

  // 结束重复发生变化
  overRepeatTimesChange = (value: React.ReactText) => {
    this.formStore.updateDatas({
      overRepeatTimes: value,
    });
    this.setState({
      data: {
        ...this.state.data,
        overRepeatTimes: value,
      },
    });
    this.getDefinitionRule();
    this.updateFormData();
  };

  // 频率规则发生改变
  frequencyRuleChange = (data: { frequencyRule: FrequencyRuleType }) => {
    const { frequencyRule } = data;
    this.formStore.updateDatas({
      frequencyRule: {
        ...frequencyRule,
      },
      // triggleTime: this.state.data.triggleTime
    });
    this.getDefinitionRule();
    this.updateFormData();
  };

  // 初始化频率规则选项
  initFrequencyItems = () => {
    const { readOnly } = this.props;
    const {
      frequencyRule,
      frequency,
      disableEdit: _disableEdit,
    } = this.state.data;
    const disableEdit = readOnly || _disableEdit;
    let target = {},
      initData = initDefFrequencyRuleData();
    frequencySelectData.map((item: FrequencyOptionData) => {
      // @ts-ignore
      target[item.key] = {
        itemType: "CUSTOM",
        readOnly: disableEdit,
        customRender: (props: any) => (
          <FrequencyRulePicker
            weId={`${props.weId || ""}_ypj8y3`}
            frequency={item.id}
            readOnly={disableEdit}
            frequencyRule={
              item.id === frequency
                ? compareFrequencyRuleData(initData, frequencyRule)
                : initData
            }
            onChange={this.frequencyRuleChange}
          />
        ),
      };
    });
    return target;
  };

  initFormItems = () => {
    const { readOnly } = this.props;
    const { disableEdit: _disableEdit, defTriggerTime, triggerTimeShowType } = this.props.data;
    const disableEdit = readOnly || _disableEdit;
    return {
      // 开始时间
      startTime: {
        itemType: "DATETIMEPICKER",
        readOnly: disableEdit,
        required: true,
        otherParams: {
          timeFormat: "HH:mm",
          allowClear: false,
        },
        isHoldRight: true,
        style: { width: 'auto' }
      },
      // 触发时间
      triggerTime: {
        itemType: "TIMEPICKER",
        readOnly: disableEdit || defTriggerTime === "association",
        otherParams: {
          timeFormat: "HH:mm",
        },
        required: triggerTimeShowType === "required",
        style: { width: 'auto' }
      },
      // 定时频率
      frequency: {
        itemType: "SELECT",
        readOnly: disableEdit,
        data: initFrequencySelectData(this.props.data),
      },
      // 结束重复
      overRepeatType: {
        itemType: "SELECT",
        readOnly: disableEdit,
        data: overRepeatSelectData,
      },
      // 结束重复（按具体日期）
      overRepeatDate: {
        itemType: "DATETIMEPICKER",
        readOnly: disableEdit,
        otherParams: {
          timeFormat: "HH:mm:ss",
        },
      },
      // 结束重复（按执行次数）
      overRepeatTimes: {
        itemType: "CUSTOM",
        readOnly: disableEdit,
        customRender: (props: any) => (
          <OverRepeatTimesPicker
            weId={`${props.weId || ""}_q7d31c`}
            value={this.state.data.overRepeatTimes}
            onChange={this.overRepeatTimesChange}
            readOnly={disableEdit}
          />
        ),
      },
      // 非工作日处理方式
      restDayConfig: {
        itemType: "SELECT",
        readOnly: disableEdit,
        data: restDayConfigSelectData,
      },
      // 规则释义
      initionRule: {
        itemType: "CUSTOM",
        readOnly: disableEdit,
        customRender: (data: any) => {
          return (
            <div className={`${repeatFrequencyClsPrefix}-initionRule`}>
              {getLabel("236754", "规则说明")}：{data.props.value}
            </div>
          );
        },
      },
      // 根据 定时频率 切换对应的配置rule
      ...this.initFrequencyItems(),
    } as FormItemProps;
  };

  initFrequencyLayout = () => {
    const { frequency } = this.state.data;
    let array: any = [];
    frequencySelectData.map((item: FrequencyOptionData, index: number) => {
      array[index] = [
        {
          id: [item.key],
          label: "",
          items: [item.key],
          labelSpan: 6,
          hide: frequency !== item.id || frequency === "never",
        },
      ];
    });
    return array;
  };

  initFrequencyCascadeRulesOuter = () => {
    const { triggerTimeShowType } = this.state.data;
    let target: any = {},
      customKeyArr: string[] = [];
    frequencySelectData.map((item: FrequencyOptionData) => {
      customKeyArr.push(item.key);
    });
    frequencySelectData.map((item: FrequencyOptionData) => {
      const hideArr = customKeyArr.filter((i) => i !== item.key);
      target[item.id] = {
        show: hideTriggerTime(item.id, triggerTimeShowType)
          ? [item.key, ...otherShowHideLayout(this.state.data, item.id)]
          : [item.key, "triggerTime", ...otherShowHideLayout(this.state.data, item.id)],
        hide: hideTriggerTime(item.id, triggerTimeShowType)
          ? [...hideArr, "triggerTime", ...otherShowHideLayout(this.state.data, item.id)]
          : hideArr,
      };
    });
    target.never = {
      hide: [
        ...target.never.hide,
        "startTime",
        "triggerTime",
        "overRepeatType",
        "overRepeatDate",
        "overRepeatTimes",
        "restDayConfig",
        "initionRule",
      ],
    };
    return target;
  };

  initFormLayout = () => {
    const {
      showOverRepeat,
      showRestDayConfig,
      overRepeatType,
      frequency,
      triggerTimeShowType,
      showInitionRule,
      startTimeType,
    } = this.state.data;
    // 初始化回显 “结束重复” 行内联动字段
    const overRepeatTypeItems =
      overRepeatType === "times"
        ? ["overRepeatType", "overRepeatTimes"]
        : overRepeatType === "date"
        ? ["overRepeatType", "overRepeatDate"]
        : ["overRepeatType"];
    return [
      [
        {
          id: "startTime",
          label: getLabel("19504", "开始时间"),
          items: ["startTime"],
          labelSpan: 6,
          hide: frequency === "never" || startTimeType !== "custom",
        },
      ],
      [
        {
          id: "triggerTime",
          label: getLabel("236749", "触发时间"),
          items: ["triggerTime"],
          labelSpan: 6,
          hide:
            hideTriggerTime(frequency, triggerTimeShowType || "") ||
            frequency === "never",
        },
      ],
      [
        {
          id: "frequency",
          label: getLabel("236750", "定时频率"),
          items: ["frequency"],
          labelSpan: 6,
          hide: false,
          cascadeRulesOuter: {
            frequency: {
              ...this.initFrequencyCascadeRulesOuter(),
            },
          },
        },
      ],
      ...this.initFrequencyLayout(),
      [
        {
          id: "overRepeatType",
          label: getLabel("236751", "结束重复"),
          items: overRepeatTypeItems,
          labelSpan: 6,
          hide: !showOverRepeat || frequency === "never",
        },
      ],
      [
        {
          id: "restDayConfig",
          label: getLabel("236752", "非工作日处理方式"),
          items: ["restDayConfig"],
          labelSpan: 6,
          hide: !showRestDayConfig || frequency === "never",
        },
      ],
      [
        {
          id: "initionRule",
          label: "",
          items: ["initionRule"],
          labelSpan: 6,
          hide: !showInitionRule || frequency === "never",
        },
      ],
    ];
  };

  initForm = () => {
    const { data } = this.state;
    this.formStore.initForm({
      data: { ...data },
      items: this.initFormItems(),
      layout: this.initFormLayout(),
      groups: [],
    });
    this.getDefinitionRule();
  };

  handleVisibleShow = () => {
    this.setState({ comDiaVisible: true });
    this.formStore.setHide("startTime", true);
  };

  handleVisibleClose = () => {
    this.setState({ comDiaVisible: false });
  };

  initComForm = () => {
    const { data } = this.state;
    const items = {
      startTime: {
        itemType: "DATETIMEPICKER",
        required: true,
        otherParams: {
          timeFormat: "HH:mm",
          allowClear: false,
        },
        style: { width: 'auto' }
      },
      // 规则释义
      initionRule: {
        itemType: "CUSTOM",
        customRender: (data: any) => (
          <div
            className={`${repeatFrequencyClsPrefix}-initionRule-combination`}
            onClick={this.handleVisibleShow}
          >
            {getLabel("236754", "规则说明")}：{data.props.value}
          </div>
        ),
      },
    } as FormItemProps;
    const layout = [
      [
        {
          id: "startTime",
          label: getLabel("19504", "开始时间"),
          items: ["startTime"],
          labelSpan: 6,
          hide: false,
        },
      ],
      [
        {
          id: "initionRule",
          label: getLabel("236750", "定时频率"),
          items: ["initionRule"],
          labelSpan: 6,
          hide: false,
        },
      ],
    ];
    this.comFormStore.initForm({
      data: { ...data },
      items,
      layout,
      groups: [],
    });
    this.getDefinitionRule();
  };

  getItems = (rule: Array<string>) => {
    let tempRule: Array<string> = [];
    const { initDatas } = this.formStore;
    rule.forEach((ru) => {
      tempRule.push(ru);
      if (cascadeRules[ru]) {
        const initValue = initDatas[ru];
        if (
          typeof initValue === "string" &&
          initValue &&
          cascadeRules[ru][initValue]
        ) {
          tempRule = tempRule.concat(cascadeRules[ru][initValue]);
        }
      }
    });
    return tempRule;
  };

  formDataChange = (value?: FormDatas, otherParams?: any) => {
    const { defTriggerTime, associationTimeTriggleVal } = this.props.data;
    if (value) {
      const key = Object.keys(value)[0];
      if (cascadeRules[key]) {
        const dataKey: any = value[key];
        const rule =
          cascadeRules[key] instanceof Array
            ? isEmpty(value[key])
              ? []
              : cascadeRules[key]
            : cascadeRules[key][dataKey];
        if (rule) {
          const { items: formItems, getDefaultValue } = this.formStore;
          const {
            layoutConfig: { id, items: oldItems },
          } = otherParams;
          const beforeItems =
            oldItems.length > 1
              ? oldItems.slice(0, oldItems.indexOf(key) + 1)
              : oldItems;
          const items = this.getItems(rule); // 联动带出字段
          // 初始化联动字段值（存在通用的字段）
          items.forEach((it) => {
            this.formStore.setItemProps(it, {
              value: getDefaultValue(formItems[it].itemType, it),
            });
          });
          // 字段联动显示
          this.formStore.setLayoutProps(id, {
            items: [...beforeItems, ...items],
          });
        }
      }

      if (key === 'frequency' && defTriggerTime === 'association') {
        this.formStore.updateDatas({
          triggerTime: associationTimeTriggleVal || ""
        });
      }
    }
    this.getDefinitionRule();
    this.updateFormData();
  };

  getFormData = () => {
    const data = this.formStore.getFormDatas();
    const target = getFormatFrequencyData(data);
    return target;
  };

  getCountData = async (otherParams?: AnyObj) => {
    const data = this.formStore.getFormDatas();
    if (data?.frequency === "never") {
      return {
        triggerPreview: [],
        playDayList: [],
      };
    }
    const target = getFormatFrequencyData(data);
    const { basicRule, restDayConfig, formData, triggerTime } = target || {};
    const res = await request({
      url: "/api/component/base/rescurrence/preview",
      method: "post",
      data: {
        basicRule,
        restDayConfig,
        formData: JSON.stringify(formData),
        triggerTime,
        ...otherParams,
      },
    });
    if (res.code === 200) {
      return {
        ...res.data,
        definitionRule: this.state.initionRule,
      };
    }
    return {
      triggerPreview: [],
      playDayList: [],
    };
  };

  // 更新释义
  getDefinitionRule = () => {
    let val = definitionRule(this.formStore.getFormDatas());
    this.formStore.updateDatas({
      initionRule: val,
    });
    let comVal = definitionRule(this.comFormStore.getFormDatas());
    this.comFormStore.updateDatas({
      initionRule: comVal,
    });
  };

  // 保存的时候同步dialog表单的数据至组合显示的表单里
  handleSave = () => {
    this.comFormStore.updateDatas(this.formStore.getFormDatas());
    this.setState({ comDiaVisible: false });
  };

  // 数据更新至表单
  updateFormData = () => {
    if (this.props.mode === "view") {
      let data = this.formStore.getFormDatas();
      this.props.onChange(data);
    }
  };

  render() {
    const { comDiaVisible } = this.state;
    const {
      data: { comShowFormat, display, disableEdit },
      readOnly,
      pageInfo,
    } = this.props;
    let containerClassName =
      pageInfo?.layoutType === "EXCEL"
        ? `${repeatFrequencyClsPrefix}-container-form-left`
        : `${repeatFrequencyClsPrefix}-container-form-right`;
    return (
      <div style={{ display: !!display ? "block" : "none", overflowX: "auto" }}>
        {comShowFormat === "tile" ? (
          <Form
            weId={`${this.props.weId || ""}_g6yiyh`}
            store={this.formStore}
            onChange={this.formDataChange}
            className={containerClassName}
          />
        ) : (
          <>
            <Form
              weId={`${this.props.weId || ""}_fgqw5g`}
              store={this.comFormStore}
              onChange={this.formDataChange}
              className={containerClassName}
            />
            <Dialog
              weId={`${this.props.weId || ""}_3g0xm5`}
              visible={comDiaVisible}
              closable
              scale
              title={getLabel("281424", "定时频率设置")}
              onClose={this.handleVisibleClose}
              footer={[
                <Button
                  weId={`${this.props.weId || ""}_hy5oxs`}
                  type={"primary"}
                  onClick={this.handleSave}
                  disabled={readOnly || disableEdit}
                >
                  {getLabel("207006", "保存")}
                </Button>,
              ]}
            >
              <Form
                weId={`${this.props.weId || ""}_g6yiyh`}
                store={this.formStore}
                onChange={this.formDataChange}
                className={containerClassName}
              />
            </Dialog>
          </>
        )}
      </div>
    );
  }
}
