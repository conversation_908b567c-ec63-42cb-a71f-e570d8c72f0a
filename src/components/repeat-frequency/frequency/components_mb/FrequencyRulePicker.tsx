import { Component, useCallback } from "react";
import { FrequencyRulePickerProps, FrequencyRuleType } from "../types";
import { MCheckbox, AnyObj, Icon, MSelect } from "@weapp/ui";
import { isRegular, formartContentShow } from "../utils";
import { CustomPicker } from "./index";
import {
  normalTimesSelectData,
  hourOptions,
  weekDayOptions,
  monthOptions,
  sortSelectData,
  dateTypePickerDataNoSign,
  dayOptions,
  dateOptions,
  weekOptions,
  quarterOptions,
  quarterMonthOptions,
} from "../constants";
import { getLabel } from "@weapp/utils";
import { isCnWord } from "../utils";

const CustomItem = (props: any) => {
  const {
    onClick,
    mode,
    data,
    labelText,
    sigleLine,
    value,
    parentKey,
    propsKey,
    prefixCls,
    gridHeaderTitle,
    needStartEndLabel,
    isSingleSelect
  } = props;
  const handleClick = useCallback(() => {
    onClick &&
      onClick({
        value,
        data,
        mode,
        labelText,
        parentKey,
        key: propsKey,
        gridHeaderTitle,
        needStartEndLabel,
        isSingleSelect
      });
  }, [
    value,
    data,
    mode,
    onClick,
    labelText,
    parentKey,
    propsKey,
    gridHeaderTitle,
    needStartEndLabel,
    isSingleSelect
  ]);
  return (
    <div
      onClick={handleClick}
      className={
        sigleLine ? `${prefixCls}-item-main-single` : `${prefixCls}-item-main`
      }
    >
      {props.children}
    </div>
  );
};

export interface FrequencyRulePickerState {
  frequencyRule: FrequencyRuleType;
  customPickerVal: string[] | string;
  frequencyPickerVisible: boolean;
  customPickerData: any[];
  customPickerMode: "picker" | "grid";
  customLabelText: string;
  parentKey: "regularRule" | "appointRule" | "";
  key: string;
  gridHeaderTitle: string;
  needStartEndLabel: boolean;
  isSingleSelect?: boolean;
}

export default class FrequencyRulePicker extends Component<
  FrequencyRulePickerProps,
  FrequencyRulePickerState
> {
  constructor(props: FrequencyRulePickerProps) {
    super(props);
    this.state = {
      frequencyRule: props.frequencyRule,
      frequencyPickerVisible: false,
      customPickerVal: [],
      customPickerMode: "picker",
      customPickerData: [],
      customLabelText: "",
      parentKey: "",
      key: "",
      gridHeaderTitle: "",
      needStartEndLabel: false,
      isSingleSelect: false
    };
  }

  componentDidMount(): void {
    this.onDataChange({
      frequencyRule: { ...this.state.frequencyRule },
    });
  }

  onDataChange = (value: any) => {
    const { frequencyRule, parentKey, key } = this.state;
    this.setState(
      parentKey
        ? {
            frequencyRule: {
              ...frequencyRule,
              [parentKey]: {
                ...frequencyRule[parentKey],
                [key]: value,
              },
            },
          }
        : {
            frequencyRule: {
              ...frequencyRule,
              [key]: value,
            },
          },
      () => {
        this.props.onChange({
          frequencyRule: {
            ...this.state.frequencyRule,
          },
        });
      }
    );
  };

  onVisibleChange = (visible: boolean = false) => {
    if (this.props.readOnly) return
    this.setState({ frequencyPickerVisible: visible });
  }

  renderFrequencyPicker = () => {
    const { prefixCls } = this.props;
    const {
      customPickerVal,
      frequencyPickerVisible,
      customPickerData,
      customPickerMode,
      customLabelText,
      gridHeaderTitle,
      needStartEndLabel,
      isSingleSelect
    } = this.state;
    return (
      <CustomPicker
        weId={`${this.props.weId || ""}_jiunmq`}
        prefixCls={prefixCls}
        value={
          typeof customPickerVal === "string"
            ? [customPickerVal]
            : customPickerVal
        }
        data={customPickerData}
        visible={frequencyPickerVisible}
        mode={customPickerMode}
        labelText={customLabelText}
        onVisibleChange={this.onVisibleChange}
        onChange={this.onDataChange}
        gridHeaderTitle={gridHeaderTitle}
        needStartEndLabel={needStartEndLabel}
        isSingleSelect={isSingleSelect}
      />
    );
  };

  customItemClick = (param: AnyObj) => {
    if (this.props.readOnly) return
    const {
      mode,
      data,
      labelText,
      value,
      parentKey,
      key,
      gridHeaderTitle,
      needStartEndLabel,
      isSingleSelect
    } = param;
    this.setState({
      frequencyPickerVisible: true,
      customPickerVal: value,
      customPickerData: data,
      customPickerMode: mode,
      customLabelText: labelText,
      parentKey,
      key,
      gridHeaderTitle,
      needStartEndLabel,
      isSingleSelect
    });
  };

  onSelectChange = (value: any, data: AnyObj) => {
    const { propsKey, parentKey } = data;
    this.setState(
      {
        parentKey,
        key: propsKey,
      },
      () => {
        this.onDataChange(value);
      }
    );
  };

  // 季度重复逻辑抽离
  quarterOption = (type: "appoint" | "regular") => {
    const { prefixCls } = this.props;
    const {
      frequencyRule: { appointRule, regularRule },
    } = this.state;
    const targetData = type === "appoint" ? appointRule : regularRule;
    const { quarterMonth } = targetData || {};
    return [
      <CustomItem
        prefixCls={prefixCls}
        weId={`${this.props.weId || ""}_zxlvc5`}
        onClick={this.customItemClick}
        mode="grid"
        data={quarterMonthOptions}
        value={quarterMonth}
        parentKey={`${type}Rule`}
        propsKey="quarterMonth"
        gridHeaderTitle={getLabel("58250", "选择月份")}
      >
        <div className={`${prefixCls}-item-main-label`}>
          {getLabel("27671", "月份")}
        </div>
        <div className={`${prefixCls}-item-main-text`}>
          <div className={`${prefixCls}-item-main-text-ellipsis`}>
            {formartContentShow(quarterMonth || [], quarterMonthOptions) ||
              getLabel("15088", "请选择")}
          </div>
          <Icon
            weId={`${this.props.weId || ""}_9vpqej`}
            className={`${prefixCls}-item-arrow`}
            name="Icon-Right-arrow01"
            color="#999999"
          />
        </div>
      </CustomItem>,
      ...this.monthYearSameOption(type),
    ];
  };

  // 月和年的重复逻辑抽离
  monthYearSameOption = (type: "appoint" | "regular") => {
    const { prefixCls, readOnly } = this.props;
    const {
      frequencyRule: { appointRule, regularRule },
    } = this.state;
    const targetData = type === "appoint" ? appointRule : regularRule;
    const {
      sortByDateOrWeek,
      sort,
      appointWeeks,
      appointDays,
      appointMonthDays,
    } = targetData || {};
    const firstContent = (
      <CustomItem
        prefixCls={prefixCls}
        weId={`${this.props.weId || ""}_gsf8xc`}
      >
        <div className={`${prefixCls}-item-main-label`}>
          {getLabel("236746", "按")}
        </div>
        <MSelect
          weId={`${this.props.weId || ""}_jaaq5h`}
          className={`${prefixCls}-item-main-text`}
          data={dateTypePickerDataNoSign}
          value={sortByDateOrWeek}
          isHoldRight
          // eslint-disable-next-line react/jsx-no-bind
          onChange={(e) => {
            this.onSelectChange(e, {
              propsKey: "sortByDateOrWeek",
              parentKey: `${type}Rule`,
            });
          }}
          readOnly={!!readOnly}
          multiple={false}
        />
      </CustomItem>
    );
    let secondContent = null;
    if (sortByDateOrWeek === "date") {
      secondContent = (
        <CustomItem
          weId={`${this.props.weId || ""}_ir7uhz`}
          prefixCls={prefixCls}
        >
          <MSelect
            weId={`${this.props.weId || ""}_mchcqa`}
            className={`${prefixCls}-item-main-label`}
            data={sortSelectData}
            value={sort}
            multiple={false}
            // eslint-disable-next-line react/jsx-no-bind
            onChange={(e) => {
              this.onSelectChange(e, {
                propsKey: "sort",
                parentKey: `${type}Rule`,
              });
            }}
            readOnly={!!readOnly}
          />
          <div className={`${prefixCls}-item-main-text`}>
            {sort !== "appoint" ? (
              <CustomItem
                weId={`${this.props.weId || ""}_do2qss`}
                prefixCls={prefixCls}
                onClick={this.customItemClick}
                sigleLine
                mode="grid"
                data={dayOptions}
                labelText={getLabel("62966", "日")}
                value={appointMonthDays}
                parentKey={`${type}Rule`}
                propsKey="appointMonthDays"
                gridHeaderTitle={getLabel("28636", "选择日期")}
                needStartEndLabel
              >
                <div className={`${prefixCls}-item-main-text-ellipsis`}>
                  {formartContentShow(
                    appointMonthDays || [],
                    dayOptions,
                    true
                  ) || getLabel("15088", "请选择")}
                </div>
                <Icon
                  weId={`${this.props.weId || ""}_awjbdk`}
                  className={`${prefixCls}-item-arrow`}
                  name="Icon-Right-arrow01"
                  color="#999999"
                />
              </CustomItem>
            ) : (
              <CustomItem
                weId={`${this.props.weId || ""}_kxhvna`}
                prefixCls={prefixCls}
                onClick={this.customItemClick}
                sigleLine
                mode="grid"
                data={dateOptions}
                labelText={""}
                value={appointMonthDays}
                parentKey={`${type}Rule`}
                propsKey="appointMonthDays"
                gridHeaderTitle={getLabel("28636", "选择日期")}
              >
                <div className={`${prefixCls}-item-main-text-ellipsis`}>
                  {formartContentShow(appointMonthDays || [], dateOptions) ||
                    getLabel("15088", "请选择")}
                </div>
                <Icon
                  weId={`${this.props.weId || ""}_rouw8p`}
                  className={`${prefixCls}-item-arrow`}
                  name="Icon-Right-arrow01"
                  color="#999999"
                />
              </CustomItem>
            )}
          </div>
        </CustomItem>
      );
    } else if (sortByDateOrWeek === "week") {
      secondContent = (
        <CustomItem
          weId={`${this.props.weId || ""}_eftrxv`}
          prefixCls={prefixCls}
        >
          <div className={`${prefixCls}-item-main-half`}>
            <CustomItem
              weId={`${this.props.weId || ""}_9wuo3i`}
              prefixCls={prefixCls}
              onClick={this.customItemClick}
              sigleLine
              mode="grid"
              data={weekOptions}
              labelText={""}
              value={appointWeeks}
              parentKey={`${type}Rule`}
              propsKey="appointWeeks"
              gridHeaderTitle={getLabel("236745", "第几周")}
              isSingleSelect
            >
              <div className={`${prefixCls}-item-main-half-ellipsis`}>
                {formartContentShow(appointWeeks || [], weekOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_6xbuwc`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </CustomItem>
          </div>

          <div className={`${prefixCls}-item-main-half`}>
            <CustomItem
              weId={`${this.props.weId || ""}_do2qss`}
              prefixCls={prefixCls}
              onClick={this.customItemClick}
              sigleLine
              mode="grid"
              data={weekDayOptions}
              labelText={""}
              value={appointDays}
              parentKey={`${type}Rule`}
              propsKey="appointDays"
              gridHeaderTitle={getLabel("236744", "选择星期")}
            >
              <div className={`${prefixCls}-item-main-half-ellipsis`}>
                {formartContentShow(appointDays || [], weekDayOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_96pgjs`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </CustomItem>
          </div>
        </CustomItem>
      );
    }
    return [firstContent, secondContent];
  };

  customInputRender = (com: any, data: any) => {
    return data.length > 0 ? data[0].content : getLabel("15088", "请选择");
  };

  optionItemRender = () => {
    const { frequency, prefixCls } = this.props;
    const {
      frequencyRule: { regularRule, appointRule },
    } = this.state;
    const {
      times,
      appointMonths,
      appointTimesDatas: _appointTimesDatas,
    } = regularRule || {};
    const {
      appointTimesDatas,
      appointQuarters,
      appointMonths: _appointMonths,
      appointHourDatas,
    } = appointRule || {};
    let regular = null,
      appoint = null,
      regularOtherConfig = null,
      appointOtherConfig = null;

    switch (frequency) {
      case "byMinute":
        regular = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_zxlvc5`}
            onClick={this.customItemClick}
            mode="picker"
            data={normalTimesSelectData()}
            labelText={getLabel("236802", "分钟")}
            value={times}
            parentKey="regularRule"
            propsKey="times"
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              {times}
              {getLabel("236802", "分钟")}
              <Icon
                weId={`${this.props.weId || ""}_vjxahp`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );
        break;

      case "byHour":
        regular = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_zxlvc5`}
            onClick={this.customItemClick}
            mode="picker"
            data={normalTimesSelectData()}
            labelText={getLabel("22084", "小时")}
            value={times}
            parentKey="regularRule"
            propsKey="times"
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              {times}
              {getLabel("22084", "小时")}
              <Icon
                weId={`${this.props.weId || ""}_xr3cku`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );
        appoint = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_k2t2ly`}
            onClick={this.customItemClick}
            mode="grid"
            data={hourOptions}
            value={appointHourDatas}
            parentKey="appointRule"
            propsKey="appointHourDatas"
            gridHeaderTitle={getLabel("58262", "选择时间")}
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("222879", "指定")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              <div className={`${prefixCls}-item-main-text-ellipsis`}>
                {formartContentShow(appointHourDatas || [], hourOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_7tk6tp`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );
        break;

      case "byDay":
        regular = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_zxlvc5`}
            onClick={this.customItemClick}
            mode="picker"
            data={normalTimesSelectData()}
            labelText={getLabel("62966", "日")}
            value={times}
            parentKey="regularRule"
            propsKey="times"
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              {times}
              {getLabel("62966", "日")}
              <Icon
                weId={`${this.props.weId || ""}_c57he5`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );
        break;

      case "byWeek":
        regular = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_zxlvc5`}
            onClick={this.customItemClick}
            mode="picker"
            data={normalTimesSelectData()}
            labelText={getLabel("35049", "周")}
            value={times}
            parentKey="regularRule"
            propsKey="times"
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              {times}{getLabel("35049", "周")}
              <Icon
                weId={`${this.props.weId || ""}_ijzbdt`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );

        regularOtherConfig = [
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_e28prl`}
            onClick={this.customItemClick}
            mode="grid"
            data={weekDayOptions}
            value={_appointTimesDatas}
            labelText={getLabel("35049", "周")}
            parentKey="regularRule"
            propsKey="appointTimesDatas"
            gridHeaderTitle={getLabel("236744", "选择星期")}
          >
            <div className={`${prefixCls}-item-main-label`}></div>
            <div className={`${prefixCls}-item-main-text`}>
              <div className={`${prefixCls}-item-main-text-ellipsis`}>
                {formartContentShow(_appointTimesDatas || [], weekDayOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_1s2qpv`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>,
        ];

        appoint = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_gsf8xc`}
            onClick={this.customItemClick}
            mode="grid"
            data={weekDayOptions}
            value={appointTimesDatas}
            labelText={getLabel("35049", "周")}
            parentKey="appointRule"
            propsKey="appointTimesDatas"
            gridHeaderTitle={getLabel("236744", "选择星期")}
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("222879", "指定")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              <div className={`${prefixCls}-item-main-text-ellipsis`}>
                {formartContentShow(appointTimesDatas || [], weekDayOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_rzjs6f`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );
        break;

      case "byMonth":
        regular = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_zxlvc5`}
            onClick={this.customItemClick}
            mode="picker"
            data={normalTimesSelectData()}
            value={times}
            labelText={getLabel("35044", "月")}
            parentKey="regularRule"
            propsKey="times"
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              {times}
              {getLabel("35044", "月")}
              <Icon
                weId={`${this.props.weId || ""}_tkntnz`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );

        regularOtherConfig = this.monthYearSameOption("regular");

        appoint = (
          <CustomItem
            prefixCls={prefixCls}
            weId={`${this.props.weId || ""}_gsf8xc`}
            onClick={this.customItemClick}
            mode="grid"
            data={monthOptions}
            value={_appointMonths}
            labelText={getLabel("35044", "月")}
            parentKey="appointRule"
            propsKey="appointMonths"
            gridHeaderTitle={getLabel("28636", "选择日期")}
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("222879", "指定")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              <div className={`${prefixCls}-item-main-text-ellipsis`}>
                {formartContentShow(_appointMonths || [], monthOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_ueseot`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );

        appointOtherConfig = this.monthYearSameOption("appoint");
        break;

      case "byQuarter":
        regular = (
          <CustomItem
            weId={`${this.props.weId || ""}_c3tz6x`}
            prefixCls={prefixCls}
            onClick={this.customItemClick}
            mode="picker"
            data={normalTimesSelectData()}
            labelText={getLabel("19623", "季度")}
            parentKey="regularRule"
            propsKey="times"
            value={times}
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              {times}
              {getLabel("19623", "季度")}
              <Icon
                weId={`${this.props.weId || ""}_1m7ui0`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );

        regularOtherConfig = this.quarterOption("regular");

        appoint = (
          <CustomItem
            weId={`${this.props.weId || ""}_d0cboy`}
            prefixCls={prefixCls}
            onClick={this.customItemClick}
            mode="grid"
            data={quarterOptions}
            value={appointQuarters}
            parentKey="appointRule"
            propsKey="appointQuarters"
            gridHeaderTitle={getLabel("121382", "选择季度")}
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("222879", "指定")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              <div className={`${prefixCls}-item-main-text-ellipsis`}>
                {formartContentShow(appointQuarters || [], quarterOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_at1qlv`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );

        appointOtherConfig = this.quarterOption("appoint");
        break;

      case "byYear":
        regular = (
          <CustomItem
            weId={`${this.props.weId || ""}_ekla6e`}
            prefixCls={prefixCls}
            onClick={this.customItemClick}
            mode="picker"
            data={normalTimesSelectData()}
            labelText={getLabel("35046", "年")}
            parentKey="regularRule"
            propsKey="times"
            value={times}
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              {times}
              {getLabel("35046", "年")}
              <Icon
                weId={`${this.props.weId || ""}_3mcto9`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>
        );
        regularOtherConfig = [
          <CustomItem
            weId={`${this.props.weId || ""}_valmgy`}
            prefixCls={prefixCls}
            onClick={this.customItemClick}
            mode="grid"
            data={monthOptions}
            value={appointMonths}
            labelText={getLabel("35044", "月")}
            parentKey="regularRule"
            propsKey="appointMonths"
            gridHeaderTitle={getLabel("58250", "选择月份")}
          >
            <div className={`${prefixCls}-item-main-label`}>
              {getLabel("35048", "每")}
            </div>
            <div className={`${prefixCls}-item-main-text`}>
              <div className={`${prefixCls}-item-main-text-ellipsis`}>
                {formartContentShow(appointMonths || [], monthOptions) ||
                  getLabel("15088", "请选择")}
              </div>
              <Icon
                weId={`${this.props.weId || ""}_ju3rb0@`}
                className={`${prefixCls}-item-arrow`}
                name="Icon-Right-arrow01"
                color="#999999"
              />
            </div>
          </CustomItem>,
          ...this.monthYearSameOption("regular"),
        ];
        break;
    }
    return {
      regular,
      appoint,
      regularOtherConfig,
      appointOtherConfig,
    };
  };

  onTypeChange = (val: any, id: "appoint" | "regular") => {
    const { frequency } = this.props;
    const _isRegular = isRegular(frequency);
    if (!_isRegular && val) {
      this.setState(
        {
          parentKey: "",
          key: "type",
        },
        () => {
          this.onDataChange(id);
        }
      );
    }
  };

  customOptionRender = (item: AnyObj) => {
    const { id } = item;
    const { prefixCls, readOnly } = this.props;
    const {
      frequencyRule: { type },
    } = this.state;
    const { regular, appoint, regularOtherConfig, appointOtherConfig } =
      this.optionItemRender();
    const itemStyle = isCnWord() ? {
      // paddingRight: "16px",
    } : {};
    return (
      <>
        <div className={`${prefixCls}-item`} style={itemStyle}>
          <MCheckbox
            className={`${prefixCls}-item-leftfix`}
            weId={`${this.props.weId || ""}_114n1c`}
            value={type === id}
            // eslint-disable-next-line react/jsx-no-bind
            onChange={(val: any) => this.onTypeChange(val, id)}
            readOnly={!!readOnly}
          />
          {id === "regular" ? regular : appoint}
        </div>
        {id === "regular" &&
          regularOtherConfig &&
          regularOtherConfig.map((item, index) => {
            return (
              <div className={`${prefixCls}-item`} key={index} style={itemStyle}> 
                <div className={`${prefixCls}-item-leftfix`}></div>
                {item}
              </div>
            );
          })}
        {id === "appoint" &&
          appointOtherConfig &&
          appointOtherConfig.map((item, index) => {
            return (
              <div className={`${prefixCls}-item`} key={index} style={itemStyle}>
                <div className={`${prefixCls}-item-leftfix`}></div>
                {item}
              </div>
            );
          })}
      </>
    );
  };

  render() {
    const { frequency } = this.props;

    const _data = isRegular(frequency)
      ? [{ id: "regular" }]
      : [{ id: "regular" }, { id: "appoint" }];

    return (
      <>
        {_data.map((item) => {
          return <div key={item.id}>{this.customOptionRender(item)}</div>;
        })}
        {this.renderFrequencyPicker()}
      </>
    );
  }
}
