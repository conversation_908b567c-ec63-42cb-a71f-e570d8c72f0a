import React, { CSSProperties } from "react";
import { classnames, getLabel, classUseMemo } from "@weapp/utils";
import {
  MDialog,
  MPickerView,
  MGridCheckbox,
  Button,
  BaseProps,
  MDialogProps,
} from "@weapp/ui";
import { formartContentShow } from "../utils";

interface CustomPickerProps extends BaseProps {
  value: string[] | string;
  data: any[];
  visible: boolean;
  mode: "picker" | "grid";
  labelText?: string;
  onChange: (data: any[]) => void;
  onVisibleChange: (visible: boolean) => void;
  dialogProps?: MDialogProps;
  dialogStyle?: CSSProperties;
  gridHeaderTitle?: string;
  needStartEndLabel?: boolean;
  isSingleSelect?: boolean;
}

interface CustomPickerState {
  prevProps?: CustomPickerProps;
  tempHandle: boolean;
  tempValue: string[] | string;
}

class CustomPicker extends React.PureComponent<
  CustomPickerProps,
  CustomPickerState
> {
  constructor(props: CustomPickerProps) {
    super(props);
    this.state = {
      tempHandle: false,
      tempValue: "",
    };
  }

  onDialogClose = () => {
    const { onVisibleChange } = this.props;
    this.setState({
      tempValue: "",
      tempHandle: false,
    });
    onVisibleChange(false);
  };

  handleSelectAll = () => {
    const { data, value } = this.props;
    const { tempHandle, tempValue } = this.state;
    let target = tempHandle ? tempValue : value;
    if (target.length === data.length) {
      this.setState({ tempHandle: true, tempValue: [] });
    } else {
      const _data = data.map((item) => item.id);
      this.setState({
        tempHandle: true,
        tempValue: _data,
      });
    }
  };

  onGridConfirm = () => {
    this.onDialogClose();
    this.props.onChange((this.state.tempValue as any) || this.props.value);
  };

  onPickerConfirm = () => {
    this.onDialogClose();
    this.props.onChange((this.state.tempValue as any) || this.props.value);
  };

  handleGridCheckBoxChange = (value: string[], data: any[]) => {
    this.setState({ tempValue: value, tempHandle: true });
  };

  handlePickerViewChange = (val: any) => {
    this.setState({
      tempValue: val.times,
      tempHandle: true,
    });
  };

  computedPickerVal = () => {
    const { value } = this.props;
    const { tempValue, tempHandle } = this.state;
    const target = tempHandle ? tempValue : value;
    return {
      times: target,
    };
  };

  renderDialogNav = () => {
    const {
      prefixCls,
      labelText,
      mode,
      data,
      gridHeaderTitle,
      value,
      needStartEndLabel,
      isSingleSelect,
    } = this.props;
    const { tempValue, tempHandle } = this.state;
    const target = tempHandle ? tempValue : value;
    let _showValue =
      typeof target === "object"
        ? formartContentShow(target, data, needStartEndLabel || false)
        : target;
    return mode === "picker" ? (
      <div className={`${prefixCls}-nav`}>
        <div className={`${prefixCls}-nav-left`} onClick={this.onDialogClose}>
          {getLabel("52130", "取消")}
        </div>
        <div className={`${prefixCls}-nav-middle`}>
          {_showValue}
          {labelText}
        </div>
        <div
          className={`${prefixCls}-nav-right`}
          onClick={this.onPickerConfirm}
        >
          {getLabel("31291", "确定")}
        </div>
      </div>
    ) : (
      <div className={`${prefixCls}-nav`}>
        <div className={`${prefixCls}-nav-left`}>{gridHeaderTitle}</div>
        <div className={`${prefixCls}-nav-middle`}>{_showValue}</div>
        {isSingleSelect ? (
          <div className={`${prefixCls}-nav-right`} style={{ width: '20px' }}></div>
        ) : (
          <div
            className={`${prefixCls}-nav-right`}
            onClick={this.handleSelectAll}
          >
            {target.length === data.length
              ? getLabel("22138", "取消全选")
              : getLabel("19234", "全选")}
          </div>
        )}
      </div>
    );
  };

  renderDialogContent = () => {
    const { prefixCls, mode, data, labelText, value } = this.props;
    const { tempValue, tempHandle } = this.state;
    const target = tempHandle ? tempValue : value;
    const contentStyle: CSSProperties = classUseMemo(
      "contentStyle",
      this,
      () => ({ height: 42 }),
      []
    );

    const highLightTextStyle: CSSProperties = classUseMemo(
      "highLightTextStyle",
      this,
      () => ({ marginLeft: 60 }),
      []
    );

    return mode === "grid" ? (
      <div className={classnames(`${prefixCls}-grid_checkbox_wrap`)}>
        <MGridCheckbox
          weId={`${this.props.weId || ""}_h3tz5o`}
          options={data}
          value={typeof target === "object" ? target : []}
          onChange={this.handleGridCheckBoxChange}
          contentStyle={contentStyle}
          // @ts-ignore
          isSingleSelect={this.props.isSingleSelect}
        />
      </div>
    ) : (
      <MPickerView
        weId={`${this.props.weId || ""}_9engdt`}
        // @ts-ignore
        optionGroups={data}
        value={this.computedPickerVal()}
        onChange={this.handlePickerViewChange}
        columnHeight={252}
        highLightText={labelText}
        highLightTextStyle={highLightTextStyle}
        cycleTime={2}
      />
    );
  };

  renderDialogFooter = () => {
    const { prefixCls } = this.props;
    return (
      <div className={classnames(`${prefixCls}-footer`)}>
        <Button
          weId={`${this.props.weId || ""}_ayeh0k`}
          onClick={this.onPickerConfirm}
          type="primary"
          style={{ width: "100%", height: "100%" }}
        >
          {getLabel("31291", "确定")}
        </Button>
      </div>
    );
  };

  render() {
    const { prefixCls, dialogProps, dialogStyle, mode, visible } = this.props;
    return (
      <MDialog
        weId={`${this.props.weId || ""}_e1f5ol`}
        className={prefixCls}
        visible={visible}
        mask
        maskClosable
        placement={"bottom"}
        onClose={this.onDialogClose}
        footer={[]}
        {...dialogProps}
      >
        <div className={classnames(`${prefixCls}-content`)} style={dialogStyle}>
          {this.renderDialogNav()}
          {this.renderDialogContent()}
          {mode === "grid" && this.renderDialogFooter()}
        </div>
      </MDialog>
    );
  }
}

export default CustomPicker;
