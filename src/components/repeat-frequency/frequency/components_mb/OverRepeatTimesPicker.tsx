import React, { Component, CSSProperties } from "react";
import { classUseMemo, getLabel } from "@weapp/utils";
import { MPickerView, MDialog, Icon, Button } from "@weapp/ui";
import { normalTimesSelectData } from "../constants";

export default class OverRepeatTimes extends Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      value: props.value,
      visible: false,
      tempVal: props.value,
    };
  }

  handleClick = () => {
    if (this.props.readOnly) return
    this.setState({
      visible: true,
    });
  };

  onDialogClose = () => {
    this.setState({
      visible: false,
      value: this.state.tempVal,
    });
    this.props.onChange(this.state.tempVal as any);
  };

  computedPickerVal = () => {
    const { value } = this.state;
    return {
      times: value,
    };
  };

  handlePickerViewChange = (val: any) => {
    this.setState({
      value: val.times,
    });
  };

  onPickerConfirm = () => {
    this.setState({
      visible: false,
      tempVal: this.state.value,
    });
    this.props.onChange(this.state.value as any);
  };

  render() {
    const { prefixCls } = this.props;
    const { value, visible } = this.state;
    const highLightTextStyle: CSSProperties = classUseMemo(
      "highLightTextStyle",
      this,
      () => ({ marginLeft: 60 }),
      []
    );
    return (
      <>
        <div
          onClick={this.handleClick}
          className={`${prefixCls}-over-repeat-times`}
        >
          {value}
          <Icon
            weId={`${this.props.weId || ""}_6xbuwc`}
            className={`${prefixCls}-item-arrow`}
            name="Icon-Right-arrow01"
            color="#999999"
          />
        </div>
        <MDialog
          weId={`${this.props.weId || ""}_468fo2`}
          className={prefixCls}
          visible={visible}
          onClose={this.onDialogClose}
          destroyOnClose
          mask
          maskClosable
          placement={"bottom"}
          footer={[]}
        >
          <div className={`${prefixCls}-content`}>
            <div className={`${prefixCls}-nav`}>
              <div className={`${prefixCls}-nav-left`}>
                <Button
                  weId={`${this.props.weId || ""}_agfzeg`}
                  type="link"
                  className={`cancel`}
                  onClick={this.onDialogClose}
                >
                  {getLabel("52130", "取消")}
                </Button>
              </div>
              <div className={`${prefixCls}-nav-middle`}>
                {value}
                {getLabel("184269", "次")}
              </div>
              <div className={`${prefixCls}-nav-right`}>
                <Button
                  weId={`${this.props.weId || ""}_07702b`}
                  type="link"
                  className={`confirm`}
                  onClick={this.onPickerConfirm}
                >
                  {getLabel("31291", "确定")}
                </Button>
              </div>
            </div>
            <MPickerView
              weId={`${this.props.weId || ""}_qakxtr`}
              optionGroups={normalTimesSelectData()}
              value={this.computedPickerVal()}
              onChange={this.handlePickerViewChange}
              columnHeight={252}
              highLightText={getLabel("184269", "次")}
              highLightTextStyle={highLightTextStyle}
              cycleTime={2}
            />
          </div>
        </MDialog>
      </>
    );
  }
}
