import { repeatFrequencyClsPrefix } from "../../../constants/index";
import { Loadable } from "@weapp/ui";
import { RepeatFrequencyType } from "./types";

const EbRepeatFrequency = Loadable({
  name: "EbRepeatFrequency",
  loader: () =>
    import(
      /* webpackChunkName: "repeat_frequency" */
      "./RepeatFrequency"
    ),
}) as RepeatFrequencyType;

EbRepeatFrequency.defaultProps = {
  prefixCls: repeatFrequencyClsPrefix,
};

export { definitionRule as DefinitionRule } from "./utils";

export type {
  RepeatFrequencyProps,
  FrequencyType,
  RepeatFrequencyDataProps,
} from "./types";

export default EbRepeatFrequency;
