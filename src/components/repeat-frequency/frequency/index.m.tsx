import { mRepeatFrequencyClsPrefix } from "../../../constants";
import { Loadable } from "@weapp/ui";
import { MRepeatFrequencyType } from "./types";

const MEbRepeatFrequency = Loadable({
  name: "MEbRepeatFrequency",
  loader: () =>
    import(
      /* webpackChunkName: "m_repeat_frequency" */
      "./MRepeatFrequency"
    ),
}) as MRepeatFrequencyType;

MEbRepeatFrequency.defaultProps = {
  prefixCls: mRepeatFrequencyClsPrefix,
};

export type { MRepeatFrequencyProps } from "./types";

export default MEbRepeatFrequency;
