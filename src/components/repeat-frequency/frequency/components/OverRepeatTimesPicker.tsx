import React, { Component } from "react";
import { Input } from "@weapp/ui";
import { repeatFrequencyClsPrefix } from "../../../../constants/index";
import { MAXINPUTTIMES } from "../constants";
import { getLabel } from "@weapp/utils";

const { InputNumber } = Input;

export default class OverRepeatTimesPicker extends Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      value: props.value,
    };
  }

  overRepeatTimesChange = (value: React.ReactText) => {
    if (/^[1-9]\d*$/.test(value as string)) {
      let target = value;
      if ((value as number) >= MAXINPUTTIMES) {
        target = MAXINPUTTIMES;
      }
      this.setState({ value: target });
      this.props.onChange(target);
    } else {
      this.setState({ value: "" });
      this.props.onChange("");
    }
  };

  render() {
    const { readOnly } = this.props;
    return (
      <div className={`${repeatFrequencyClsPrefix}-rule-radio-option`} style={{ lineHeight: '30px' }}>
        <span>{getLabel("10068", "重复")}</span>
        <InputNumber
          weId={`${this.props.weId || ""}_isn5ra`}
          value={this.state.value}
          onChange={this.overRepeatTimesChange}
          readOnly={readOnly}
        />
        <span>{getLabel("236743", "$x次后结束").replace("$x", "")}</span>
      </div>
    );
  }
}
