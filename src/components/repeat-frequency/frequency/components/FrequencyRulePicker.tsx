import React, { Component, useCallback } from "react";
import { getLabel } from "@weapp/utils";
import {
  Radio,
  Input,
  Select,
  RadioValueType,
  RadioOptionProps,
  SelectValueType,
} from "@weapp/ui";
import { repeatFrequencyClsPrefix } from "../../../../constants";
import {
  dateTypePickerData,
  sortSelectData,
  sortNoAppointSelectData,
  hourOptions,
  dayOptions,
  dateOptions,
  weekDayOptions,
  weekOptions,
  monthOptions,
  quarterOptions,
  quarterMonthOptions,
  MAXINPUTTIMES,
} from "../constants";
import { FrequencyRulePickerProps, FrequencyRuleType } from "../types";
import { isRegular } from "../utils/index";

const { InputNumber } = Input;

const CustomSelect = (props: any) => {
  const _prefix = `${repeatFrequencyClsPrefix}-rule-custom-select`;
  const {
    data,
    multiple,
    singleAndMultiple,
    allowSelectAll,
    value,
    propsKey,
    onChange,
    parentKey,
    readOnly
  } = props;
  const handleOnChange = useCallback(
    (_value: SelectValueType) => {
      onChange?.(_value, propsKey, parentKey);
    },
    [onChange, propsKey, parentKey]
  );
  return (
    <span className={_prefix}>
      <Select
        weId={`${props.weId || ""}_ykj7t3`}
        data={data}
        value={value}
        multiple={multiple}
        singleAndMultiple={singleAndMultiple}
        allowSelectAll={allowSelectAll}
        onChange={handleOnChange}
        readOnly={readOnly}
      />
    </span>
  );
};

interface FrequencyRulePickerStates {
  frequencyRule: FrequencyRuleType;
}

export default class FrequencyRulePicker extends Component<
  FrequencyRulePickerProps,
  FrequencyRulePickerStates
> {
  constructor(props: any) {
    super(props);
    this.state = {
      frequencyRule: props.frequencyRule,
    };
  }

  componentDidMount(): void {
    this.props.onChange({
      frequencyRule: {
        ...this.state.frequencyRule,
      },
    });
  }

  onDataChange = (
    parentKey: "regularRule" | "appointRule" | "",
    key: string,
    value: any
  ) => {
    this.setState(
      parentKey
        ? {
            frequencyRule: {
              ...this.state.frequencyRule,
              [parentKey]: {
                ...this.state.frequencyRule[parentKey],
                [key]: value,
              },
            },
          }
        : {
            frequencyRule: {
              ...this.state.frequencyRule,
              [key]: value,
            },
          },
      () => {
        this.props.onChange({
          frequencyRule: {
            ...this.state.frequencyRule,
          },
        });
      }
    );
  };

  onRadioChange = (val: RadioValueType) => {
    this.onDataChange("", "type", val);
  };

  handleCustomChange = (
    value: SelectValueType,
    key: string,
    parentKey: "regular" | "appoint"
  ) => {
    const _parentKey = `${parentKey}Rule`;
    // @ts-ignore
    this.onDataChange(_parentKey, key, value);
  };

  handleTimesChange = (value: React.ReactText) => {
    if (/^[1-9]\d*$/.test(value as string)) {
      let target = value;
      if ((value as number) >= MAXINPUTTIMES) {
        target = MAXINPUTTIMES;
      }
      this.onDataChange("regularRule", "times", target);
    } else {
      this.onDataChange("regularRule", "times", "");
    }
  };

  optionItemRender = () => {
    const { frequency, readOnly } = this.props;
    const {
      frequencyRule: { regularRule, appointRule },
    } = this.state;
    const _prefix = readOnly ? `${repeatFrequencyClsPrefix}-rule-radio-option-readonly` : `${repeatFrequencyClsPrefix}-rule-radio-option-container`;
    const {
      times,
      appointMonths,
      appointTimesDatas: _appointTimesDatas,
    } = regularRule || {};
    const {
      appointTimesDatas,
      appointQuarters,
      appointMonths: _appointMonths,
      appointHourDatas,
    } = appointRule || {};
    let regular = null,
      appoint = null;
    const _readOnly = !!readOnly;
    switch (frequency) {
      case "byMinute":
        regular = (
          <div className={_prefix}>
            <span>{getLabel("35048", "每")}</span>
            <InputNumber
              weId={`${this.props.weId || ""}_2nwtgs`}
              value={times}
              onChange={this.handleTimesChange}
              readOnly={_readOnly}
            />
            <span>
              {getLabel("236738", "$x分钟触发一次").replace("$x", "")}
            </span>
          </div>
        );
        break;

      case "byHour":
        regular = (
          <div className={_prefix}>
            <span>{getLabel("35048", "每")}</span>
            <InputNumber
              weId={`${this.props.weId || ""}_ze5iww`}
              value={times}
              onChange={this.handleTimesChange}
              readOnly={_readOnly}
            />
            <span>
              {getLabel("236739", "$x小时触发一次").replace("$x", "")}
            </span>
          </div>
        );
        appoint = (
          <div className={_prefix}>
            <span>{getLabel("222879", "指定")}</span>
            <CustomSelect
              weId={`${this.props.weId || ""}_eyjoqb`}
              parentKey={"appoint"}
              data={hourOptions}
              value={appointHourDatas}
              propsKey="appointHourDatas"
              multiple
              allowSelectAll
              onChange={this.handleCustomChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("10540", "触发")}</span>
          </div>
        );
        break;

      case "byDay":
        regular = (
          <div className={_prefix}>
            <span>{getLabel("35048", "每")}</span>
            <InputNumber
              weId={`${this.props.weId || ""}_3bc9aq`}
              value={times}
              onChange={this.handleTimesChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("236740", "$x日触发一次").replace("$x", "")}</span>
          </div>
        );
        break;

      case "byWeek":
        regular = (
          <div className={_prefix}>
            <span>{getLabel("35048", "每")}</span>
            <InputNumber
              weId={`${this.props.weId || ""}_7scdzj`}
              value={times}
              onChange={this.handleTimesChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("35049", "周")}</span>
            <CustomSelect
              weId={`${this.props.weId || ""}_kdd0f3`}
              parentKey={"regular"}
              data={weekDayOptions}
              value={_appointTimesDatas}
              propsKey="appointTimesDatas"
              onChange={this.handleCustomChange}
              multiple
              allowSelectAll
              readOnly={_readOnly}
            />
            <span>{getLabel("10540", "触发")}</span>
          </div>
        );
        appoint = (
          <div className={_prefix}>
            <span>{getLabel("222879", "指定")}</span>
            <CustomSelect
              weId={`${this.props.weId || ""}_o1z4q2`}
              parentKey={"appoint"}
              data={weekDayOptions}
              value={appointTimesDatas}
              propsKey="appointTimesDatas"
              onChange={this.handleCustomChange}
              multiple
              allowSelectAll
              readOnly={_readOnly}
            />
            <span>{getLabel("10540", "触发")}</span>
          </div>
        );
        break;

      case "byMonth":
        regular = (
          <div className={_prefix}>
            <span>{getLabel("35048", "每")}</span>
            <InputNumber
              weId={`${this.props.weId || ""}_1nk110`}
              value={times}
              onChange={this.handleTimesChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("35044", "月")}</span>
            {this.monthYearSameOption("regular")}
          </div>
        );
        appoint = (
          <div className={_prefix}>
            <span>{getLabel("236741", "指定月")}</span>
            <CustomSelect
              weId={`${this.props.weId || ""}_9ijtjc`}
              parentKey={"appoint"}
              data={monthOptions}
              value={_appointMonths}
              propsKey="appointMonths"
              multiple
              allowSelectAll
              onChange={this.handleCustomChange}
              readOnly={_readOnly}
            />
            {this.monthYearSameOption("appoint")}
          </div>
        );
        break;

      case "byQuarter":
        regular = (
          <div className={_prefix}>
            <span>{getLabel("35048", "每")}</span>
            <InputNumber
              weId={`${this.props.weId || ""}_ix305r`}
              value={times}
              onChange={this.handleTimesChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("19623", "季度")}</span>
            {this.quarterOption("regular")}
          </div>
        );
        appoint = (
          <div className={_prefix}>
            <span>{getLabel("236742", "指定季度")}</span>
            <CustomSelect
              weId={`${this.props.weId || ""}_41jvfz`}
              data={quarterOptions}
              value={appointQuarters}
              propsKey="appointQuarters"
              parentKey="appoint"
              multiple
              allowSelectAll
              onChange={this.handleCustomChange}
              readOnly={_readOnly}
            />
            {this.quarterOption("appoint")}
          </div>
        );
        break;

      case "byYear":
        regular = (
          <div className={_prefix}>
            <span>{getLabel("35048", "每")}</span>
            <InputNumber
              weId={`${this.props.weId || ""}_3bc9aq`}
              value={times}
              onChange={this.handleTimesChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("35046", "年")}</span>
            {/* 选择月份： 1月 - 12月 */}
            <CustomSelect
              weId={`${this.props.weId || ""}_3jbr1j`}
              parentKey="regular"
              data={monthOptions}
              value={appointMonths}
              propsKey="appointMonths"
              multiple
              allowSelectAll
              onChange={this.handleCustomChange}
              readOnly={_readOnly}
            />
            {this.monthYearSameOption("regular")}
          </div>
        );
        break;
    }
    return {
      regular,
      appoint,
    };
  };

  // 季度重复逻辑抽离
  quarterOption = (type: "appoint" | "regular") => {
    const { readOnly } = this.props;
    const {
      frequencyRule: { appointRule, regularRule },
    } = this.state;
    const targetData = type === "appoint" ? appointRule : regularRule;
    const { quarterMonth } = targetData || {};
    const _readOnly = !!readOnly;
    return (
      <>
        <CustomSelect
          weId={`${this.props.weId || ""}_ri7e5g`}
          data={quarterMonthOptions}
          value={quarterMonth}
          propsKey="quarterMonth"
          parentKey={type}
          multiple
          allowSelectAll
          onChange={this.handleCustomChange}
          readOnly={_readOnly}
        />
        {this.monthYearSameOption(type)}
      </>
    );
  };

  // 月和年的重复逻辑抽离
  monthYearSameOption = (type: "appoint" | "regular") => {
    const { readOnly } = this.props;
    const {
      frequencyRule: { appointRule, regularRule },
    } = this.state;
    const targetData = type === "appoint" ? appointRule : regularRule;
    const {
      sortByDateOrWeek,
      sort,
      appointWeeks,
      appointDays,
      appointMonthDays,
    } = targetData || {};
    const _readOnly = !!readOnly;
    return (
      <>
        {/* 选择“按日期”或“按周” */}
        <CustomSelect
          weId={`${this.props.weId || ""}_2gpv7g`}
          parentKey={type}
          value={sortByDateOrWeek}
          propsKey="sortByDateOrWeek"
          data={dateTypePickerData}
          onChange={this.handleCustomChange}
          readOnly={_readOnly}
        />
        {/* 
          根据 sortByDateOrWeek 展示不同的选择项 
          sortByDateOrWeek 为 date 展示“正数”、“倒数”、“指定”
          sortByDateOrWeek 为 week 展示“第一周”..."第四周"、“最后1周”
        */}
        {sortByDateOrWeek === "date" && (
          <>
            <CustomSelect
              weId={`${this.props.weId || ""}_fa09i5`}
              parentKey={type}
              data={sortSelectData}
              value={sort}
              propsKey="sort"
              onChange={this.handleCustomChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("35141", "第")}</span>
            {sort !== "appoint" ? (
              <CustomSelect
                weId={`${this.props.weId || ""}_42efsp`}
                parentKey={type}
                data={dayOptions}
                value={appointMonthDays}
                propsKey="appointMonthDays"
                multiple
                allowSelectAll
                onChange={this.handleCustomChange}
                readOnly={_readOnly}
              />
            ) : (
              <CustomSelect
                weId={`${this.props.weId || ""}_5a079g`}
                parentKey={type}
                data={dateOptions}
                value={appointMonthDays}
                propsKey="appointMonthDays"
                multiple
                allowSelectAll
                onChange={this.handleCustomChange}
                readOnly={_readOnly}
              />
            )}
            {sort !== "appoint" && <span>{getLabel("35063", "天")}</span>}
            <span>{getLabel("10540", "触发")}</span>
          </>
        )}

        {sortByDateOrWeek === "week" && (
          // 满足会议模块暂时调整成单选 toreupdate
          <>
            <CustomSelect
              weId={`${this.props.weId || ""}_em8jf8`}
              parentKey={type}
              data={weekOptions}
              value={appointWeeks}
              propsKey="appointWeeks"
              multiple
              singleAndMultiple
              allowSelectAll={false}
              onChange={this.handleCustomChange}
              readOnly={_readOnly}
            />
            <CustomSelect
              weId={`${this.props.weId || ""}_23wp3b`}
              parentKey={type}
              data={weekDayOptions}
              value={appointDays}
              propsKey="appointDays"
              multiple
              allowSelectAll
              onChange={this.handleCustomChange}
              readOnly={_readOnly}
            />
            <span>{getLabel("10540", "触发")}</span>
          </>
        )}
      </>
    );
  };

  // radio 样式复写
  customOptionRender = (_props: RadioOptionProps, ele: React.ReactNode) => {
    const { option } = _props,
      { regular, appoint } = this.optionItemRender();
    function customRender(_content: any) {
      const _prefix = `${repeatFrequencyClsPrefix}-rule-radio-option`;
      return (
        <div className={_prefix}>
          <div className={`${_prefix}-handler`}>{ele}</div>
          {_content}
        </div>
      );
    }
    let content = ele;
    if (option) {
      content = customRender(option.id === "regular" ? regular : appoint);
    }
    return content;
  };

  render() {
    const { frequency, readOnly } = this.props;
    const {
      frequencyRule: { type },
    } = this.state;
    const _data = isRegular(frequency)
      ? [{ id: "regular" }]
      : [{ id: "regular" }, { id: "appoint" }];
    return (
      <Radio
        vertical
        canReversedChoose={false}
        weId={`${this.props.weId || ""}_droy4r`}
        data={_data}
        value={type}
        onChange={this.onRadioChange}
        customOptionRender={this.customOptionRender}
        readOnly={!!readOnly}
      />
    );
  }
}
