import { getLabel } from "@weapp/utils";
import {
  FrequencyType,
  FrequencyKeyType,
  OptionData,
  OptionsType,
  FrequencyOptionsType,
  CascadeRules,
} from "./types";
import { initDefFrequencyRuleData } from "./utils";

export const MAXINPUTTIMES = 99;

export const initBasicData = {
  startTime: new Date().getTime(),
  // frequency: "byHour",
  restDayConfig: "normal",
  // overRepeatType: "never",
  overRepeatType: "date",
  overRepeatTimes: 2,
  overRepeatDate: "",
  frequencyRule: initDefFrequencyRuleData(),
};

export const cascadeRules: CascadeRules = {
  overRepeatType: {
    never: [],
    date: ["overRepeatDate"],
    times: ["overRepeatTimes"],
  },
};

const getFrequencySelectData = () => {
  return [
    {
      key: FrequencyKeyType["frequencyRuleByMinute"],
      id: FrequencyType["byMinute"],
      value: FrequencyType["byMinute"],
      label: getLabel("223201", "按分钟"),
      content: getLabel("223201", "按分钟"),
    },
    {
      key: FrequencyKeyType["frequencyRuleByHour"],
      id: FrequencyType["byHour"],
      value: FrequencyType["byHour"],
      label: getLabel("222776", "按小时"),
      content: getLabel("222776", "按小时"),
    },
    {
      key: FrequencyKeyType["frequencyRuleByDay"],
      id: FrequencyType["byDay"],
      value: FrequencyType["byDay"],
      label: getLabel("18604", "按日"),
      content: getLabel("18604", "按日"),
    },
    {
      key: FrequencyKeyType["frequencyRuleByWeek"],
      id: FrequencyType["byWeek"],
      value: FrequencyType["byWeek"],
      label: getLabel("17058", "按周"),
      content: getLabel("17058", "按周"),
    },
    {
      key: FrequencyKeyType["frequencyRuleByMonth"],
      id: FrequencyType["byMonth"],
      value: FrequencyType["byMonth"],
      label: getLabel("18695", "按月"),
      content: getLabel("18695", "按月"),
    },
    {
      key: FrequencyKeyType["frequencyRuleByQuarter"],
      id: FrequencyType["byQuarter"],
      value: FrequencyType["byQuarter"],
      label: getLabel("222777", "按季度"),
      content: getLabel("222777", "按季度"),
    },
    {
      key: FrequencyKeyType["frequencyRuleByYear"],
      id: FrequencyType["byYear"],
      value: FrequencyType["byYear"],
      label: getLabel("17961", "按年"),
      content: getLabel("17961", "按年"),
    },
    {
      key: FrequencyKeyType["frequencyRuleByNever"],
      id: FrequencyType["never"],
      value: FrequencyType["never"],
      label: getLabel("281418", "不启用（立即触发）"),
      content: getLabel("281418", "不启用（立即触发）"),
    },
  ];
};
export const frequencySelectData = getFrequencySelectData();

const getOverRepeatSelectData = () => {
  return [
    // {
    //   id: "never",
    //   value: "never",
    //   label: getLabel("222829", "永不"),
    //   content: getLabel("222829", "永不"),
    // },
    {
      id: "date",
      value: "date",
      label: getLabel("97173", getLabel("97173", "日期")),
      content: getLabel("97173", getLabel("97173", "日期")),
    },
    {
      id: "times",
      value: "times",
      label: getLabel("18115", "次数"),
      content: getLabel("18115", "次数"),
    },
  ];
};
export const overRepeatSelectData = getOverRepeatSelectData();

const getRestDayConfigSelectData = () => {
  return [
    {
      id: "normal",
      value: "id",
      label: getLabel("222844", "正常触发"),
      content: getLabel("222844", "正常触发"),
    },
    {
      id: "nextWorkDay",
      value: "nextWorkDay",
      label: getLabel("222845", "推迟到下个工作日"),
      content: getLabel("222845", "推迟到下个工作日"),
    },
    {
      id: "never",
      value: "never",
      label: getLabel("222845", "推迟到下个工作日"),
      content: getLabel("222846", "不触发"),
    },
  ];
};
export const restDayConfigSelectData = getRestDayConfigSelectData();

const getDateTypePickerData = () => {
  return [
    {
      id: "date",
      value: "date",
      label: getLabel("222861", "按日期"),
      content: getLabel("222861", "按日期"),
    },
    {
      id: "week",
      value: "week",
      label: getLabel("17058", "按周"),
      content: getLabel("17058", "按周"),
    },
  ];
};
export const dateTypePickerData = getDateTypePickerData();

const getDateTypePickerDataNoSign = () => {
  return [
    {
      id: "date",
      value: "date",
      label: getLabel("97173", "日期"),
      content: getLabel("97173", "日期"),
    },
    {
      id: "week",
      value: "week",
      label: getLabel("35049", "周"),
      content: getLabel("35049", "周"),
    },
  ];
};
export const dateTypePickerDataNoSign = getDateTypePickerDataNoSign();

const getSortSelectData = () => {
  return [
    {
      id: "head",
      value: "head",
      label: getLabel("18817", "正数"),
      content: getLabel("18817", "正数"),
    },
    {
      id: "tail",
      value: "tail",
      label: getLabel("11894", "倒数"),
      content: getLabel("11894", "倒数"),
    },
    {
      id: "appoint",
      value: "appoint",
      label: getLabel("222879", "指定"),
      content: getLabel("222879", "指定"),
    },
  ];
};
export const sortSelectData = getSortSelectData();

export const getSortNoAppointSelectData = () => {
  return [
    {
      id: "head",
      value: "head",
      label: getLabel("18817", "正数"),
      content: getLabel("18817", "正数"),
    },
    {
      id: "tail",
      value: "tail",
      label: getLabel("11894", "倒数"),
      content: getLabel("11894", "倒数"),
    },
  ];
};
export const sortNoAppointSelectData = getSortNoAppointSelectData();

// 分钟
// -> 每分钟 -> 用times-picker选择

// 小时
// -> 每小时 -> 用times-picker选择
// -> 指定小时(getHourOptions) -> 用frequency-picker选择
// --------------------
// 获取指定小时
const getHourOptions = () => {
  const hourOptions: OptionsType = [];
  for (let i = 0; i < 24; i++) {
    const _i = String(i);
    const optionData: OptionData = {
      id: _i,
      value: _i,
      label: `${i < 10 ? `0${i}` : i}:00`,
      content: `${i < 10 ? `0${i}` : i}:00`,
    };
    hourOptions.push(optionData);
  }
  return hourOptions;
};
export const hourOptions: OptionsType = getHourOptions();

// 日
// -> 每几日 -> 用times-picker选择
// --------------------
// 获取指定日（主要用于在月份下级选择，不可单独选择）
const getDayOptions = () => {
  const dayOptions: OptionsType = [];
  for (let i = 1; i <= 31; i++) {
    const _i = String(i);
    const optionData: OptionData = {
      id: _i,
      value: _i,
      label: _i,
      content: _i,
    };
    dayOptions.push(optionData);
  }
  return dayOptions;
};
export const dayOptions = getDayOptions();
// 获取指定日期（主要用于在月份下级选择，不可单独选择）
const getDateOptions = (needLastDay: boolean = true) => {
  const label = getLabel("62966", "日");
  const dateOptions: OptionsType = [];
  for (let i = 1; i <= 31; i++) {
    const _i = String(i);
    const optionData: OptionData = {
      id: _i,
      value: _i,
      label: `${i}${label}`,
      content: `${i}${label}`,
    };
    dateOptions.push(optionData);
  }
  if (needLastDay)
    dateOptions.push({
      id: "-1",
      value: "-1",
      label: getLabel("222631", "最后1日"),
      content: getLabel("222631", "最后1日"),
    });
  return dateOptions;
};
export const dateOptions: OptionsType = getDateOptions();

// 周
// -> 每几周/每周 -> 指定星期几(可多选 getWeekDayOptions) -> 用frequency-picker选择
// --------------------
// 获取第几周（主要用于在月份下级选择，不可单独选择）
const getWeekOptions = (needLastDay: boolean = true) => {
  const weekOptions: OptionsType = [
    {
      id: "1",
      value: "1",
      label: getLabel("222605", "第1周"),
      content: getLabel("222605", "第1周"),
      single: true,
    },
    {
      id: "2",
      value: "2",
      label: getLabel("222606", "第2周"),
      content: getLabel("222606", "第2周"),
      single: true,
    },
    {
      id: "3",
      value: "3",
      label: getLabel("222608", "第3周"),
      content: getLabel("222608", "第3周"),
      single: true,
    },
    {
      id: "4",
      value: "4",
      label: getLabel("222609", "第4周"),
      content: getLabel("222609", "第4周"),
      single: true,
    },
  ];
  if (needLastDay)
    weekOptions.push({
      id: "-1",
      value: "-1",
      label: getLabel("222632", "最后1周"),
      content: getLabel("222632", "最后1周"),
      single: true,
    });
  return weekOptions;
};
export const weekOptions = getWeekOptions();

// 获取星期几（可单独直接使用，也可用于在周下级选择）
const getWeekDayOptions = () => {
  const weekOptions: OptionsType = [
    {
      id: "MO",
      value: "MO",
      label: getLabel("13032", "星期一"),
      content: getLabel("13032", "星期一"),
    },
    {
      id: "TU",
      value: "TU",
      label: getLabel("12973", "星期二"),
      content: getLabel("12973", "星期二"),
    },
    {
      id: "WE",
      value: "WE",
      label: getLabel("13027", "星期三"),
      content: getLabel("13027", "星期三"),
    },
    {
      id: "TH",
      value: "TH",
      label: getLabel("13709", "星期四"),
      content: getLabel("13709", "星期四"),
    },
    {
      id: "FR",
      value: "FR",
      label: getLabel("12984", "星期五"),
      content: getLabel("12984", "星期五"),
    },
    {
      id: "SA",
      value: "SA",
      label: getLabel("13390", "星期六"),
      content: getLabel("13390", "星期六"),
    },
    {
      id: "SU",
      value: "SU",
      label: getLabel("15126", "星期日"),
      content: getLabel("15126", "星期日"),
    },
  ];
  return weekOptions;
};
export const weekDayOptions: OptionsType = getWeekDayOptions();

// 月
// -> 每几月/指定月(可多选 getMonthOptions) -> 指定天数(可多选 正数/倒数 getDayOptions) -> 用frequency-picker选择
// -> 每几月/指定月(可多选 getMonthOptions) -> 指定日期(可多选 getDateOptions) -> 用frequency-picker选择
// -> 每几月/指定月(可多选 getMonthOptions) -> 指定第几周(可多选 getWeekOptions) -> 指定星期几(可多选 getWeekDayOptions) -> 用frequency-picker选择
// --------------------
// 获取指定月份
const getMonthOptions = () => {
  const monthOptions: OptionsType = [
    {
      id: "1",
      value: "1",
      label: getLabel("19366", "1月"),
      content: getLabel("19366", "1月"),
    },
    {
      id: "2",
      value: "2",
      label: getLabel("19383", "2月"),
      content: getLabel("19383", "2月"),
    },
    {
      id: "3",
      value: "3",
      label: getLabel("19401", "3月"),
      content: getLabel("19401", "3月"),
    },
    {
      id: "4",
      value: "4",
      label: getLabel("19410", "4月"),
      content: getLabel("19410", "4月"),
    },
    {
      id: "5",
      value: "5",
      label: getLabel("19424", "5月"),
      content: getLabel("19424", "5月"),
    },
    {
      id: "6",
      value: "6",
      label: getLabel("19437", "6月"),
      content: getLabel("19437", "6月"),
    },
    {
      id: "7",
      value: "7",
      label: getLabel("19451", "7月"),
      content: getLabel("19451", "7月"),
    },
    {
      id: "8",
      value: "8",
      label: getLabel("19461", "8月"),
      content: getLabel("19461", "8月"),
    },
    {
      id: "9",
      value: "9",
      label: getLabel("19473", "9月"),
      content: getLabel("19473", "9月"),
    },
    {
      id: "10",
      value: "10",
      label: getLabel("13042", "10月"),
      content: getLabel("13042", "10月"),
    },
    {
      id: "11",
      value: "11",
      label: getLabel("13047", "11月"),
      content: getLabel("13047", "11月"),
    },
    {
      id: "12",
      value: "12",
      label: getLabel("13056", "12月"),
      content: getLabel("13056", "12月"),
    },
  ];
  return monthOptions;
};
export const monthOptions: OptionsType = getMonthOptions();

// 季度
// -> 每几季度/指定季度(可多选 getQuarterOptions) -> 指定第几月(可多选 正数/倒数) -> 指定天数(可多选 正数/倒数 getDayOptions) -> 用frequency-picker选择
// -> 每几季度/指定季度(可多选 getQuarterOptions) -> 指定第几月(可多选 正数/倒数) -> 指定日期(可多选 getDateOptions) -> 用frequency-picker选择
// -> 每几季度/指定季度(可多选 getQuarterOptions) -> 指定第几月(可多选 正数/倒数) -> 指定第几周(可多选 getWeekOptions) -> 指定星期几(可多选 getWeekDayOptions) -> 用frequency-picker选择
// --------------------
// 获取指定周
const getQuarterOptions = () => {
  const quarterOptions: OptionsType = [
    {
      id: "1",
      value: "1",
      label: getLabel("164064", "第1季度"),
      content: getLabel("164064", "第1季度"),
    },
    {
      id: "2",
      value: "2",
      label: getLabel("164065", "第2季度"),
      content: getLabel("164065", "第2季度"),
    },
    {
      id: "3",
      value: "3",
      label: getLabel("164066", "第3季度"),
      content: getLabel("164066", "第3季度"),
    },
    {
      id: "4",
      value: "4",
      label: getLabel("164067", "第4季度"),
      content: getLabel("164067", "第4季度"),
    },
  ];
  return quarterOptions;
};
export const quarterOptions: OptionsType = getQuarterOptions();

const getQuarterMonthOptions = () => {
  return [
    {
      id: "1",
      value: "1",
      label: getLabel("236925", "第$x个月").replace("$x", "1"),
      content: getLabel("236925", "第$x个月").replace("$x", "1"),
    },
    {
      id: "2",
      value: "2",
      label: getLabel("236925", "第$x个月").replace("$x", "2"),
      content: getLabel("236925", "第$x个月").replace("$x", "2"),
    },
    {
      id: "3",
      value: "3",
      label: getLabel("236925", "第$x个月").replace("$x", "3"),
      content: getLabel("236925", "第$x个月").replace("$x", "3"),
    },
  ];
}
export const quarterMonthOptions = getQuarterMonthOptions()

// 年
// -> 每几年 -> 指定月份(可多选 getMonthOptions) -> 指定天数(可多选 正数/倒数 getDayOptions) -> 用frequency-picker选择
// -> 每几年 -> 指定月份(可多选 getMonthOptions) -> 指定日期(可多选 getDateOptions) -> 用frequency-picker选择
// -> 每几年 -> 指定月份(可多选 getMonthOptions) -> 指定第几周(可多选 getWeekOptions) -> 指定星期几(可多选 getWeekDayOptions) -> 用frequency-picker选择

// 选取次数
export const normalTimesSelectData = function () {
  let times = [];
  for (let i = 1; i <= MAXINPUTTIMES; i++) {
    const _i = i.toString();
    times.push({ value: i, label: _i });
  }
  return { times };
};
