.@{repeatFrequencyClsPrefix}-rule {
  &-radio-option:nth-child(2) {
    margin-top: calc(10 * var(--hd));
  }

  &-radio-option {
    display: flex;
    // align-items: center;
    width: 100%;
    font-size: calc(12 * var(--hd));
    // line-height: calc(50 * var(--hd));
    input {
      width: calc(80 * var(--hd));
      margin: 0 calc(10 * var(--hd));
    }

    .is-readonly {
      width: auto;
    }

    .ui-select {
      margin: 0 calc(10 * var(--hd)) calc(5 * var(--hd)) calc(10 * var(--hd));
    }

    .ui-input-wrap {
      min-width: calc(var(--hd) * 100);
      max-width: calc(var(--hd) * 250) !important;
    }

    &-handler {
      height: calc(30 * var(--hd));
      margin-right: calc(10 * var(--hd));
      display: flex;
      align-items: center;
      // max-height: 40px;
      .ui-radio-input {
        width: calc(14 * var(--hd));
      }
    }

    .ui-select-input-tags-group.is-multiple .ui-select-input-tag {
      padding-right: calc(8 * var(--hd));
    }
    
    .ui-select-input-selected.ui-select-single-multiple-selected {
      margin-bottom: 0;
    }

    &-readonly {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      line-height: calc(30 * var(--hd));
      .@{repeatFrequencyClsPrefix}-rule-custom-select {
        line-height: calc(29 * var(--hd));
      }

      .ui-select {
        margin: 0 calc(10 * var(--hd));
      }
    }

    &-container {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      line-height: calc(30 * var(--hd));
    }
  }

  &-custom-select {
    .ui-scroller__wrap {
      margin: 0 !important;
    }
  }
}

.@{repeatFrequencyClsPrefix}-initionRule,
.@{repeatFrequencyClsPrefix}-initionRule-combination {
  font-size: var(--font-size-12);
  color: var(--secondary-fc);
  word-break: break-word;
}

.@{repeatFrequencyClsPrefix}-initionRule-combination {
  color: var(--main-fc);
  &:hover {
    color: var(--primary);
    cursor: pointer;
    text-decoration: underline;
  }
}
.@{repeatFrequencyClsPrefix}-container-form-left,
.@{repeatFrequencyClsPrefix}-container-form-right {
  min-width: calc(300 * var(--hd));
  .ui-formItem-label {
    justify-content: right;
  }
  .ui-formItem-wrapper {
    margin-left: calc(20 * var(--hd));
  }
}

.@{repeatFrequencyClsPrefix}-container-form-left {
  .ui-formItem-label {
    justify-content: left;
  }
}

.@{repeatFrequencyClsPrefix}-container-form-right {
  .ui-formItem-label {
    justify-content: right;
  }
}
