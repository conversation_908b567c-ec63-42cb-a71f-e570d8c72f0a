import { ComponentType } from "react";
import { BaseProps, MGridCheckboxOptionData, AnyObj } from "@weapp/ui";

//----------------------------------------通用type----------------------------------------
export interface CascadeRules {
  [key: string]: any;
}

export enum FrequencyType {
  "byMinute" = "byMinute",
  "byHour" = "byHour",
  "byDay" = "byDay",
  "byWeek" = "byWeek",
  "byMonth" = "byMonth",
  "byQuarter" = "byQuarter",
  "byYear" = "byYear",
  "never" = "never",
}

export enum FrequencyKeyType {
  "frequencyRuleByMinute" = "frequencyRuleByMinute",
  "frequencyRuleByHour" = "frequencyRuleByHour",
  "frequencyRuleByDay" = "frequencyRuleByDay",
  "frequencyRuleByWeek" = "frequencyRuleByWeek",
  "frequencyRuleByMonth" = "frequencyRuleByMonth",
  "frequencyRuleByQuarter" = "frequencyRuleByQuarter",
  "frequencyRuleByYear" = "frequencyRuleByYear",
  "frequencyRuleByNever" = "frequencyRuleByNever",
}

export interface RuleType {
  times: number | string; // 次数

  appointMonths?: string[]; // 指定的月份

  sortByDateOrWeek?: "date" | "week"; // 按“周” 或 “日期”进行排序

  sort?: "head" | "tail" | "appoint"; // 正序、降序、指定

  appointWeeks?: string[]; // 指定的周

  appointDays?: string[]; // 指定的星期几

  appointMonthDays?: string[]; // 指定的月份对应的天数

  appointQuarters?: string[]; // 指定的季度

  appointQuarterDays?: string[]; // 指定的季度对应的天数

  appointTimesDatas?: string[]; // 指定触发的次数 | 指定周几

  quarterMonth?: string[]; // 季度的月份

  appointHourDatas?: string[]; // 指定触发的小时次数
}

export interface RegularRuleType extends RuleType {}

export interface AppointRuleType extends RuleType {}

export interface FrequencyRuleType {
  type: "regular" | "appoint"; // 固定 | 指定

  regularRule?: RegularRuleType; // 固定规则

  appointRule?: AppointRuleType; // 指定规则
}

export interface RepeatFrequencyDataProps {
  startTime: string; // 开始时间

  startTimeType?: "system" | "custom"; // 开始时间类型  “用户选择” “关联字段”

  startTimeAssociation?: string; // 开始时间关联

  startTimeAssociationVal?: string; // 开始时间关联数据

  comShowFormat?: "tile" | "combination"; // 组件显示格式 “平铺显示” “组合显示”

  triggerTimeShowType?: "edit" | "required" | "hidden"; // 触发时间类型

  triggerTime?: string; // 触发时间

  defTriggerTime?: string; // 默认触发时间

  appointTimeTriggle?: string; // 指定默认触发时间

  associationTimeTriggle?: string; // 关联触发时间

  associationTimeTriggleVal?: string; // 关联触发时间数据

  showInitionRule?: boolean; // 是否展示释义

  frequencyType?: "all" | "appoint"; //频率可选项 “全部” “指定”

  frequencyTypeSelect?: FrequencyOptionsType[];

  frequency: FrequencyType; // 定时频率

  showRestDayConfig?: boolean; // 是否展示 ”非工作日处理方式“
  restDayConfig?: "never" | "nextWorkDay" | "normal";

  showOverRepeat?: boolean; // 是否展示 ”结束重复“
  overRepeatType?: "never" | "date" | "times";
  overRepeatDate?: string; // overRepeatType为date时
  overRepeatTimes?: number | string; // overRepeatType为times时

  frequencyRule: FrequencyRuleType; // 频率规则

  frequencySelectConfig?: FrequencyType[];

  display?: boolean; // 显示

  readOnly?: boolean; // 编辑

  required?: boolean; // 必填

  disableEdit?: boolean; // 禁止手工编辑

  initionRule?: string;
}

export interface OptionData extends MGridCheckboxOptionData {
  /* 数据的提示 -> 兼容select与picker-view组件数据格式 */
  label?: string;
  content?: string;
  single?: boolean;
}
export type OptionsType = OptionData[];

export interface FrequencyOptionData extends MGridCheckboxOptionData {
  key: FrequencyKeyType;
  id: FrequencyType;
  value: FrequencyType;
}

export type FrequencyOptionsType = FrequencyOptionData[];

//----------------------------------------通用----------------------------------------
export interface FrequencyRulePickerProps {
  prefixCls?: string;
  frequency: FrequencyType;
  frequencyRule: FrequencyRuleType;
  onChange: (data: { frequencyRule: FrequencyRuleType }) => void;
  readOnly?: Boolean;
  showWeekMonthAppoint?: Boolean;
}

//----------------------------------------PC端----------------------------------------

export type RepeatFrequencyType = ComponentType<RepeatFrequencyProps>;

export interface RepeatFrequencyProps extends BaseProps {
  data: RepeatFrequencyDataProps;
  ref: any;
  onChange: (data: any) => void;
  mode: "view" | "design";
  pageInfo?: any;
  readOnly?: boolean;
  // 是否展示“周”和“月”的指定选项 
  showWeekMonthAppoint?: Boolean;
}

//----------------------------------------h5端----------------------------------------
export type MRepeatFrequencyType = ComponentType<MRepeatFrequencyProps>;

export interface MRepeatFrequencyProps extends BaseProps {
  data: RepeatFrequencyDataProps;
  ref: any;
  [key: string]: any;
  onChange: (data: any) => void;
}
