import React, { PureComponent, createRef } from "react";
import { DesignOptions, DesignProps } from "@weapp/ebdcoms";
import { getLabel } from "@weapp/utils";
import RepeatFrequency from "../frequency/index.m";
import { initEbConfigVal, getSysTriggerTime } from "../frequency/utils";

const customHide = () => {
  return !window.enableUiPropsDesign_Frequency;
}
export default class MRepeatFrequencyDesign extends PureComponent<
  DesignProps,
  any
> {
  repeatFrequency = createRef<any>();
  static defaultOpts: DesignOptions = {
    componentKey: "Frequency",
    form: {
      mask: true,
    },
    render: { standalone: true },
    compList: {
      customHide,
    },
    onBeforeSave: (com: any, store: any) => {
      if (com.config?.triggerTimeShowType === "hidden") {
        com.config.triggerTime = getSysTriggerTime();
      }
      if (com.config?.frequencyType === "appoint") {
        // 兼容 frequencyTypeSelect 字段不存在的场景
        if (
          !com.config?.frequencyTypeSelect ||
          (com.config?.frequencyTypeSelect &&
            com.config?.frequencyTypeSelect.length === 0)
        ) {
          return {
            error: getLabel("288854", "请设置频率可选项"),
          };
        }
      }
    },
  };
  static defaultProps: any = {
    type: "Frequency",
    config: {
      title: getLabel("284269", "重复频率"),
      fieldId: "",
      formId: "",
      titleEnabled: true,
      footerEnabled: true,
      ...initEbConfigVal(),
    },
  };

  render() {
    const { config } = this.props;
    // @ts-ignore
    return <RepeatFrequency weId={`${this.props.weId || ''}_rnrghy`} data={{...initEbConfigVal(), ...config}} ref={this.repeatFrequency} />;
  }
}
