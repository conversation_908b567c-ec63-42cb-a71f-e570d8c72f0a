// ui 转 EB组件 映射关系
export const browser_trans_config = {
  type: 'browser_type',
}

export const browserPanel_trans_config = {
  ...browser_trans_config,
}

export const browserPanel_trans_func = (props: any) => {
  const { browser_type, pageSize: propsPageSize, panelHeight, ...resProps } = props;
  const pageSize = propsPageSize ? Number(propsPageSize) : 10;

  const config = {
    ...resProps,
    type: browser_type,
    browserListProps: { pageSize },
    browserTableProps: { pageSize },
    panelHeight,
    browser_key: (panelHeight || 0) + pageSize,
  }
  return config;
}