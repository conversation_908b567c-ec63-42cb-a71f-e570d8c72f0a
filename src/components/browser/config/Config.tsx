import { LABEL_SPAN } from "../../../constants";
import { commonConfig } from "../../../ebConfig";
import BrowserTypeConfig from "./BrowserTypeConfig";

const config = {
  ...commonConfig,
  items: {
    browser_type: {
      itemType: 'INPUT',
    },
    module: {
      itemType: 'CUSTOM',
      customRender: (props: any) => (<BrowserTypeConfig
        weId={`${props.weId || ''}_6dgvvw`}
        {...props}
      />)
    }
  },
  layout: [
    [
      {
        id: 'common',
        label: '类型',
        labelSpan: LABEL_SPAN,
        items: ['browser_type'],
        groupId: '',
        hide: true,
      },
    ],
    [
      {
        id: 'common',
        label: '模块标识',
        labelSpan: LABEL_SPAN,
        items: ['module'],
        groupId: '',
        hide: false,
      },
    ],
  ],
  /** 关联字段，如{a: ['b']}，每次a字段值发生改变时，强制渲染b字段组件 */
  references: {
    module: ['browser_type'],
  },
};

export default config;