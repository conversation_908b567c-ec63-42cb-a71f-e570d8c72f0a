import React from "react";
import { RequestOptionConfig, RequestPromise, request } from "@weapp/utils";
import { BrowserType, BrowserValueType, AnyObj } from "@weapp/ui";
import { browserClsPrefix } from "../../../constants";
import { CommonConfigCustomProps } from "../../../types/common";

interface BrowserTypeConfigProps extends CommonConfigCustomProps {
}
interface BrowserTypeConfigStates {
  typeValue: BrowserValueType;
  loading: boolean;
  groupId: string;
  dataResult: any;
  dataMap: AnyObj;
}

/*
 dataMap：编辑标准页面组件
 1. 存储module、type对应的数据关系，用于处理默认值 { module_type: [data] }
 2. type、module存在重复的情况，通过params区分
 3. params 对标Browser 的commonParams、dataParams 过滤浏览框数据
 4. ‼️ 浏览类型设置功能 先屏蔽（设置保存之后，需要同步更新BrowserTypeConfig缓存的对应关系，需BrowserType支持回调）
*/

const All_Group_Type = 'all';

class BrowserTypeConfig extends React.Component<BrowserTypeConfigProps, BrowserTypeConfigStates> {
  constructor(props: BrowserTypeConfigProps) {
    super(props);
    this.state = {
      typeValue: [],
      loading: false,
      groupId: All_Group_Type,
      dataResult: {}, // browser-type 接口返回的数据
      dataMap: {},
    }
  }
  componentDidMount() {
    this.fetchData();
  }
  fetch = (options: RequestOptionConfig) => {
    const { loginFree, commonParams } = this.props?.form?.datas || {};
    try {
      if (!options.url) {
        return Promise.reject(new Error('The request url is empty!'));
      } else {
        // 处理未登录状态
        if (loginFree) {
          const apiPatt = /^\/api/;
          options.url = apiPatt.test(options.url) ? options.url.replace(apiPatt, '/papi') : options.url;
        }
      }
      // 处理接口公共参数
      if (options.method === 'POST') {
        options.data = Object.assign({}, commonParams, options.data);
      } else {
        options.params = Object.assign({}, commonParams, options.params);
      }
      return request(options);
    } catch (error) {
      return Promise.reject(error);
    }
  }
  fetchData = () => {
    const { dataURL = '/api/bcw/relevanceBrowser/browserBean', dataParams } = this.props?.form?.datas || {};
    this.setState({ loading: true, groupId: All_Group_Type });
    this.fetch({
      url: dataURL as string,
      params: {
        filter: true,
        ...dataParams as any,
      },
    }).then(res => {
      this.updateDataMap(res?.data?.data || []);
      this.setState({ dataResult: { ...res, data: { ...res?.data, isAuth: false } } });
    }).catch(err => {
      console?.error?.(err);
    }).finally(() => {
      this.setState({ loading: false });
    });
  }
  updateDataMap = (datas: Array<any>) => {
    const dataMap: AnyObj = {};
    datas?.forEach((data: any) => {
      data.items?.forEach((item: any) => {
        const key = `${item.module}_${item.type}`;
        if (!dataMap[key]) dataMap[key] = [];
        dataMap[key].push(item);
      })
    })
    const { config, form } = this.props;
    const allConfigs = Object.assign({}, form?.datas, config); // 合并datas即config，获取最全的配置
    const val = `${allConfigs.module}_${allConfigs.browser_type}`;
    let nState = { dataMap };
    if (val && dataMap[val]) {
      let typeValue = dataMap[val];
      if (dataMap[val].length > 1) {
        // 同type、module的情况需根据params进一步细化获取默认选中值
        const { commonParams, dataParams } = allConfigs;
        const params = Object.assign({}, commonParams, dataParams);
        const hasParams = Object.keys(params).length > 0;
        if (!hasParams) typeValue = dataMap[val].filter((v: { params: any; }) => !v.params); // 无特殊params参数配置
        else if (dataMap[val].length === 2) typeValue = dataMap[val].filter((v: { params: any; }) => v.params); // 有特殊params参数配置，且只有两项的情况
        else {
          let result = null;
          for(let i=0; i<dataMap[val].length && !result; i++) {
            let pas = dataMap[val][i].params;
            if (!pas) continue;
            pas = JSON.parse(pas);
            const preKeys = Object.keys(pas).sort().join();
            const keys = Object.keys(params).sort().join();
            preKeys === keys && (result = dataMap[val][i]);
          }
          result && (typeValue = [result]);
        }
      }
      nState = Object.assign(nState, { typeValue });
    }
    this.setState(nState);
  }
  onTypeChange = (value: BrowserValueType) => {
    this.setState({ typeValue: value });
    const val = value?.[0];
    const params = val?.params ? JSON.parse(val?.params) : {};
    val && this.props.onFormChange?.({
      module: val.module,
      browser_type: val.type,
      commonParams: {
        ...this.props.form?.datas?.commonParams as any,
        ...params,
      },
      dataParams: {
        ...this.props.form?.datas?.dataParams as any,
        ...params,
      }
    });
  }
  _fetchData = (options: RequestOptionConfig): RequestPromise<any> => {
    if (options?.url === '/api/bcw/relevanceBrowser/browserBean') return new Promise((res) => res(this.state.dataResult));
    return request(options);
  }
  render() {
    const { typeValue } = this.state;
    return (
      <>
        <BrowserType
          value={typeValue}
          onChange={this.onTypeChange}
          weId={`${this.props.weId || ''}_9sjfac`}
          fetchData={this._fetchData}
          className={`${browserClsPrefix}-config-module`}
        />
      </>
    )
  }
}

export default BrowserTypeConfig;