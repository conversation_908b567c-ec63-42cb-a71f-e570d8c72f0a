import { IComData } from '@weapp/designer';
import { AnyObj, utils } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import PageSizeConfig from '../../../../components-share/page-size-config';
import { browserClsPrefix, LABEL_SPAN, uiPropsDesignClsPrefix } from '../../../../constants';
import BrowserTypeConfig from '../../config/BrowserTypeConfig';
import { default as browserConfig } from '../../config/Config';

const { needSync } = utils;
const config: AnyObj = {
  /** 是否使用标题配置 */
  title: true,
  /** 是否使用底部区域配置 */
  footer: true,
  /** 是否使用固定区域配置 */
  fixedArea: false,
  groups: [
    {
      id: 'config',
      title: getLabel('219086','内容设置'),
      visible: true,
      custom: false,
    },
  ],
  /** 表单配置项，同公共组件Form，扩展属性参考下面定义的FormItemProps */
  items: {
    browser_type: {
      itemType: 'INPUT',
    },
    module: {
      itemType: 'CUSTOM',
      customRender: (props: any) => (<BrowserTypeConfig weId={`${props.weId || ''}_4hiioe`}
        {...props}
      />)
    },
    panelHeight: {
      itemType: 'INPUTNUMBER',
      className: `${uiPropsDesignClsPrefix}-occupy-whole-row`,
    },
    pageSize: {
      itemType: 'CUSTOM',
      customRender: (props: any) => (<PageSizeConfig weId={`${props.weId || ''}_1dv2bk`}
        {...props}
        className={`${props.className} ${uiPropsDesignClsPrefix}-occupy-whole-row`}
        dataKey="pageSize"
      />)
    }
  },
  /** 关联字段，如{a: ['b']}，每次a字段值发生改变时，强制渲染b字段组件 */
  references: {
    /** filteredCom值更新之后，触发comName渲染 */
    ...browserConfig?.references,
  },
  layout: [
    [
      {
        id: 'browser_type',
        label: getLabel('219097','类型'),
        labelSpan: LABEL_SPAN,
        items: ['browser_type'],
        groupId: 'config',
        hide: true,
      },
    ],
    [
      {
        id: 'module',
        label: getLabel('219098','数据来源'),
        labelSpan: LABEL_SPAN,
        items: ['module'],
        groupId: 'config',
        hide: false,
      },
    ],
    [
      {
        id: 'pageSize',
        label: getLabel('219099','每页行数'),
        labelSpan: LABEL_SPAN,
        items: ['pageSize'],
        groupId: 'config',
        hide: false,
        wrapClassName: `${browserClsPrefix}-panel-config-pageSize`,
        helpTip: getLabel('219230','仅对列表浏览框生效'),
      }
    ],
    [
      {
        id: 'panelHeight',
        label: getLabel('219100','高度(px)'),
        labelSpan: LABEL_SPAN,
        items: ['panelHeight'],
        groupId: 'config',
        hide: false,
      }
    ],
  ],
};

const getConfig = (__com?: IComData, store?: AnyObj) => {
  const { labelSpan, fields } = store || {};
  let layout = config?.layout;
  if (store) {
    if (needSync('fields', store)) {
      layout = layout?.filter((row: any[]) => {
        return row?.map((col) => fields?.indexOf(col?.id) >= 0);
      })
    }
    if (needSync('labelSpan', store)) {
      layout = layout?.map((row: any[]) => {
        return row?.map((col: any) => ({
          ...col,
          labelSpan: labelSpan,
        }))
      })
    }
  }
  return { ...config, layout };
}
export default getConfig;