import React,{PureComponent} from 'react';
import { ViewProps } from '@weapp/ebdcoms';
import { <PERSON><PERSON>er } from '@weapp/ui';
import ebMiddleware from '../../../../eb-middleware';

const { BrowserPanel } = Browser;
class BrowserPanelView extends PureComponent<ViewProps> {
  render() {
    const { className, config } = this.props;
    return (<BrowserPanel weId={`${this.props.weId || ''}_391rk9`}
      className={className}
      hideClearBtn
      {...config}
    />)
  }
}
export default ebMiddleware('BrowserPanelView', 'View', 'BrowserPanel', BrowserPanelView);