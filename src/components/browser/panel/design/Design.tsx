import { PureComponent } from 'react';
import { DesignProps } from '@weapp/ebdcoms';
import { <PERSON><PERSON><PERSON>, CorsComponent } from '@weapp/ui';
import ebMiddleware from '../../../../eb-middleware';

const { BrowserPanel } = Browser;

class BrowserPanelDesign extends PureComponent<DesignProps<any>> {
  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  static defaultProps: DesignProps = {
    // ------------------
    // 基础属性
    // ------------------
    type: '',
    // 关联组件Config中的属性，应该同Config中声明的属性字段保持一致
    config: {
      title: '关联浏览业务数据',
      panelHeight: 550,
      pageSize: '10',
    } as any,
  };

  emptyStyle = { height: 150, background: '#fff' }

  render() {
    const { className, config } = this.props;
    if (config?.type) {
      const { browser_key } = config;
      return (<BrowserPanel weId={`${this.props.weId || ''}_391rk9`}
        className={className}
        hideClearBtn
        key={browser_key}
        {...config}
      />)
    }
    return (
      <CorsComponent weId={`${this.props.weId || ''}_pk843i`} 
        app="@weapp/ebdcoms"
        compName="Empty"
        content={'请设置关联浏览业务数据配置项'}
        style={this.emptyStyle}
      />
    )
  }
};
export default ebMiddleware('BrowserPanelDesign', 'Design', 'BrowserPanel', BrowserPanelDesign);