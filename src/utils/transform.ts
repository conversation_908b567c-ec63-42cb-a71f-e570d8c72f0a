import { AnyObj } from '@weapp/ui';
import { corsImport } from '@weapp/utils';
/* transform utils */
export const transProps = (props: AnyObj, map: AnyObj) => {
  const newProps = Object.assign({}, props);
  map && Object.keys(map).forEach((mapKey: string) => {
    newProps[map[mapKey]] = newProps[mapKey];
  })
  return newProps;
}

export const revertProps = (props: AnyObj, map: AnyObj) => {
  const newProps = Object.assign({}, props);
  map && Object.keys(map).forEach((mapKey: string) => {
    newProps[mapKey] = newProps[map[mapKey]];
  })
  return newProps;
}

let getLocaleValue = (value: AnyObj) => value?.nameAlias;

// const getLocaleValueFunc = async () => {
//   const func = await corsImport('@weapp/ebdcoms')?.then((module) => module?.getLocaleValue);
//   getLocaleValue = func;
// }

// getLocaleValueFunc();

export const executeActions = (target: any, events: AnyObj[], props: AnyObj) => {
  corsImport('@weapp/ebdcoms').then((mod) => {
    const { executeActions } = mod;
    const { renderLayout, history, match, page, id, config } = props; // renderLayout参数直接从组件props获取
    const otherParams = {
      renderLayout, // 链接地址需要渲染当前布局函数
      page, // 页面信息
      comId: id, // 组件id
      datasetMap: config?.dataset, // 数据源集合
    };
    executeActions(target, events, {
      history,
      match,
      clientType: page?.client,
    }, otherParams);
  });
}

export { getLocaleValue };
