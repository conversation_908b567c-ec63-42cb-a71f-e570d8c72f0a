import { corsImport } from '@weapp/utils';

declare let window: any;

let asyncComs: Promise<any> | null = null;

type Callback = (ebdcoms: any) => void;

const ebdcoms = {
  async load(callback?: Callback) {
    if (!asyncComs) {
      asyncComs = await corsImport('@weapp/ebdcoms');
    }

    if (this.get()) {
      callback?.(this.get());
    }

    return asyncComs;
  },
  get(noError?:boolean) {
    if (window.weappEbdcoms) {
      return window.weappEbdcoms;
    }
    if (!noError) {
      // eslint-disable-next-line no-console
      console.error('@weapp/ebdcoms not loaded yet!');
    }

    return {} as any;
  },
  excu(name: string, noError?:boolean) {
    const coms = this.get(noError);

    if (typeof coms[name] === 'function') {
      return coms[name];
    }

    return () => {};
  },
  asyncExcu(name: string) {
    return async (...args: any[]) => {
      await this.load();

      return this.get()[name](...args);
    };
  },
};

export default ebdcoms;