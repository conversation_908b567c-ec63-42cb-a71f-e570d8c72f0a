
import { AssetsLibVal } from '@weapp/ebdcoms/lib/common/assets-lib';
import { ButtonConfigData } from '@weapp/ebdcoms/lib/common/button-config/types';
import { Component, FC } from 'react';

export type RenderType<P = any> = FC<P> | Component<P> | string;

export interface SortVal<P> {
  emptyContent: AssetsLibVal;
  descContent: AssetsLibVal;
  ascContent: AssetsLibVal;
  customRenderConfig?: RenderType<P>;
}

export interface SearchVal<P> {
  emptyContent: AssetsLibVal;
  selectedContent: AssetsLibVal;
  customRenderConfig?: RenderType<P>;
}

export enum ShowType {
  ICON = "ICON",
  FONTBTN = "FONTBTN",
  PAGINATION = "PAGINATION",
  FONTWITHICON = "FONTWITHICON",
  INPUT = "INPUT"
}

export enum BtnFrom {
  DEFAULT = "DEFAULT",
  CUSTOM = "CUSTOM"
}

export enum AlignType {
  LEFT = "LEFT",
  CENTER = "CENTER",
  RIGHT = "RIGHT"
}

export enum EventTo {
  /** 刷新组件 */
  REFRESHCOM = 'REFRESHCOM',
  /** 定制菜单 */
  CUSTOMEDITMENU = 'CUSTOMEDITMENU',
  /** 下一页 */
  NEXTPAGE = 'NEXTPAGE',
  /** 上一页 */
  PREPAGE = 'PREPAGE',
  /**跳转链接 */
  JUMPLINK = 'JUMPLINK',
  /** 高级搜索 */
  SEARCH = 'SEARCH',
  /** 快捷搜索 */
  QUICKSEARCH = 'QUICKSEARCH',
  /** 跳过节假日 */
  SKIPHOILDAY = 'SKIPHOILDAY',
  /** 审批列表更多 */
  WORKFLOWMORE = 'WORKFLOWMORE',
  /** 分页 */
  PAGINATION = 'PAGINATION',
  /** 列表展开/收缩 */
  LISTSPREAD = 'LISTSPREAD',
  /** 年月日切换 */
  YMDSWITCH = 'YMDSWITCH',
  /** 切换图表类型 */
  CHARTSWITCH = 'CHARTSWITCH',
  /** 最大化/最小化 */
  FULLSCREEN = 'FULLSCREEN',
  /** 门户跳转-临时 */
  PAGEJUMP = 'PAGEJUMP',
  /** 切换文档显示模式 */
  DOCTYPESWITCH = 'DOCTYPESWITCH',
  /** 未读文档的进入文档库 */
  UNREADDOCUMENTMORE = 'UNREADDOCUMENTMORE',
  /** 快速创建任务 */
  TASKFASTCREATE = 'TASKFASTCREATE',
  /** 换一批列表随机分页 */
  LISTCHANGEBATCH = 'LISTCHANGEBATCH',
  /** 关闭组件 */
  CLOSECOM = 'CLOSECOM',
  /** 组件更多 */
  MOREPAGE = 'MOREPAGE',
  /** 进入日程 */
  ENTERSCHEDULE = 'ENTERSCHEDULE',
  /** 安排日程 */
  ARRANGESCHEDULE = 'ARRANGESCHEDULE',
  STATISTICS = "STATISTICS",
  SORT = "SORT",
}

export enum EventsType {
  /** 单击事件 */
  Click = 'CLICK',
  /** 单击每一条数据事件 */
  ClickData = 'CLICKDATA',
  /** 载入 */
  DidMount = 'DIDMOUNT',
  /** 回车事件 */
  PressEnter = 'PRESSENTER',
}

export type EvtMap = {
  /** 事件类型 */
  type: EventsType;
  /** 事件id */
  id: string;
  /** 事件名 */
  name: string;
  /** 动作集合 */
  events: any;
  /** 禁止删除 */
  disableDelete?: boolean;
  /** 动作描述 */
  actionDesc?: string;
};

export interface BtnEvent {
  to: EventTo;
  value: string | EvtMap[][];
  customRenderConfig?: RenderType;
}

export type WithIconContent = {
  icon?: AssetsLibVal;
  title?: string;
};

export enum DateFormatType {
  YEAR = '1', // 年
  YEARMONTH = '2', // 年月
  YEARMONTHDAY = '4', // 年月日
}

export type ShowContentType<P = any> = string | AssetsLibVal | SortVal<P> | SearchVal<P> | WithIconContent;

export interface DiyBtn<T extends ShowContentType<P> = ShowContentType, P extends any = any> {
  id: string;
  defaultContent: string;
  showType: ShowType;
  showContent: T;
  event: BtnEvent;
  isOpen: boolean;
  showOrder: number;
  btnFrom: BtnFrom;
  disabled?: boolean;
  isSkipBtn?: boolean;
  skipStatus?: boolean;
  hideAlways?: boolean;
  isWorkFlowMore?: boolean;
  isSpread?: boolean;
  customType?: string;
  customValue?: any;
  dateFormatType?: DateFormatType;
  config?: ButtonConfigData;
  isFilter?: boolean;
  isEdit?: boolean;
}

export interface ValueType {
  enabled: boolean;
  alignType?: AlignType;
  btns: DiyBtn[];
}

export interface OperationBtns {
  topOperatBtns: ValueType;
  bottomOperatBtns: ValueType;
}