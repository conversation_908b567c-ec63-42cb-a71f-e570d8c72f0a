import React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import { appInfo, getLocale } from '@weapp/utils';

import './style/index.less';
import RouterMain from './routes/pc/Main';
import reportWebVitals from './reportWebVitals';

const info = appInfo('@weapp/ui-props-design');

/* 根由根路径，可能有多级路径 */
export const root = info.publicUrl;

/* pc端根路由 */
export const uiRoot = `${root}/ui-props-design`;

getLocale('@weapp/ui-props-design').then(() => {
  ReactDOM.render(
    <React.StrictMode weId={`_woaecy`}>
      <Router weId={`_gavr60`}>
        <RouterMain weId={`_yeocqj`} />
      </Router>
    </React.StrictMode>,
    document.getElementById('root')
  );
});

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
