@uiPropsDesignClsPrefix: weapp-ui-props-design;

@ebcomClsPrefix: ebcom;

/* components-share */
@menuDesignClsPrefix: @{uiPropsDesignClsPrefix}-menu-design;

@conditionClsPrefix: @{uiPropsDesignClsPrefix}-condition;

@commonSettingClsPrefix: @{uiPropsDesignClsPrefix}-common-setting;

@iconSelectionConfigClsPrefix: @{uiPropsDesignClsPrefix}-icon-selection-config;

@pageSizeConfigClsPrefix: @{uiPropsDesignClsPrefix}-page-size-config;

@alertCommonClsPrefix: @{uiPropsDesignClsPrefix}-alert-common;

@mReminderTimeClsPrefix: @{uiPropsDesignClsPrefix}-m-reminder-time;

/* demo */
@uiPropsDesignDemoClsPrefix: ui-props-design-demo;

/* components */
@browserClsPrefix: @{uiPropsDesignClsPrefix}-browser;
@emptyClsPrefix: @{uiPropsDesignClsPrefix}-empty;
@breadCrumbClsPrefix: @{uiPropsDesignClsPrefix}-bread-crumb;
@alertClsPrefix: @{uiPropsDesignClsPrefix}-alert;
@matterAssociationClsPrefix: @{uiPropsDesignClsPrefix}-matter-association;
@commentClsPrefix: @{uiPropsDesignClsPrefix}-comment;
@ebcomCommentClsPrefix: @{ebcomClsPrefix}-comment;
@fastCreateClsPrefix: @{uiPropsDesignClsPrefix}-fast-create;
@reminderMethodClsPrefix: @{uiPropsDesignClsPrefix}-reminder-method;
@repeatFrequencyClsPrefix: @{uiPropsDesignClsPrefix}-repeat-frequency;
@mRepeatFrequencyClsPrefix: @{uiPropsDesignClsPrefix}-repeat-frequency-m;
