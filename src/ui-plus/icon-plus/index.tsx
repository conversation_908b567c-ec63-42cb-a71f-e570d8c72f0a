import { Icon } from "@weapp/ui";
import { useCallback, MouseEvent } from "react";
import { IconPlusProps } from "./types";

const IconPlus = <T,>(props: IconPlusProps<T>) => {

  const { weId, data, onClick, ...resProps } = props;

  const handleClick = useCallback((e: MouseEvent<HTMLSpanElement>) => {
    onClick?.(e, data);
  }, [data, onClick])

  return (
    <Icon
      weId={`${props.weId || ''}_yauccs`}
      onClick={handleClick}
      {...resProps}
    />
  )
}

export default IconPlus;