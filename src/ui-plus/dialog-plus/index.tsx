import { Button, Dialog } from "@weapp/ui";
import { useCallback } from "react";
import { DialogPlusProps } from "./types";
import { getLabel } from "@weapp/utils";

const DialogPlus = (props: DialogPlusProps) => {

  const { weId, onClose, onOk, children, okText, cancelText, ...resProps } = props;

  const handleClose = useCallback(() => {
    onClose?.();
  }, [onClose])

  const footer = (
    <>
      <Button
        weId={`${props.weId || ''}_k9cjdr`}
        type="primary"
        onClick={onOk}
      >
        {okText || getLabel("223962", "确认")}
      </Button>
      <Button
        weId={`${props.weId || ''}_5bsxzo`}
        onClick={handleClose}
      >
        {cancelText || getLabel("223917", "取消")}
      </Button>
    </>
  )

  return (
    <Dialog
      weId={`${props.weId || ''}_yauccs`}
      onClose={handleClose}
      footer={footer}
      {...resProps}
    >
      {children}
    </Dialog>
  )
}

export default DialogPlus;