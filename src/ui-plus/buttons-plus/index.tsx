import { Button, Icon } from "@weapp/ui";
import { use<PERSON><PERSON>back, MouseEvent } from "react";
import { ButtonPlusProps } from "./types";

const ButtonPlus = <T,>(props: ButtonPlusProps<T>) => {

  const { weId, disabled, loading, data, loadingIcon, onClick, children, ...resProps } = props;

  const handleClick = useCallback((e: MouseEvent<HTMLButtonElement>) => {
    onClick?.(e, data);
  }, [data, onClick])

  return (
    <Button
      weId={`${props.weId || ''}_yauccs`}
      disabled={disabled || loading}
      onClick={handleClick}
      {...resProps}
    >
      {loading && <Icon weId={`${props.weId || ''}_4owu6k`} name={loadingIcon || "Icon-handle"} spin/>}
      {children}
    </Button>
  )
}

export default ButtonPlus;