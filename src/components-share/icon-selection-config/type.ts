import { AnyObj, FormDatas } from "@weapp/ui";
import { ComponentType } from "react";
import { CommonConfigCustomProps } from "../../types/common";

export interface IconSelectionConfigProps extends CommonConfigCustomProps {
  iconSelectionConfigId: string;
  iconSelectionConfigType: 'name' | 'component';
  canClear?: boolean,
  triggerProps?: AnyObj;
  customParams?: AnyObj;
  onChange?: (changes: FormDatas, customParams?: AnyObj) => void; //触发组件配置改变的回调
}

export type IconSelectionConfigType = ComponentType<IconSelectionConfigProps>;