import { iconSelectionConfigClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { IconSelectionConfigType } from "./type";

// 等UI库IconSelection支持返回Icon等需求后替换
const IconSelectionConfig = Loadable({
  name: 'IconSelectionConfig', loader: () => import(
    /* webpackChunkName: "icon-selection-config" */
    './IconSelectionConfig'
  )
}) as IconSelectionConfigType;

IconSelectionConfig.defaultProps = {
  prefixCls: iconSelectionConfigClsPrefix,
  canClear: true,
}

export default IconSelectionConfig;

export type { IconSelectionConfigType, IconSelectionConfigProps } from './type';
