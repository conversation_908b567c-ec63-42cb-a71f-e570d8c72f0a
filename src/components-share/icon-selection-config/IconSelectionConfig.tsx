import React, { ReactText } from "react";
import { classUseMemo } from "@weapp/utils";
import { AnyObj, IconSelection, Icon } from "@weapp/ui";
import IconMap from './iconMap.json';
import { IconSelectionConfigProps } from "./type";

const datas = IconMap.filter(val => typeof val.key === 'number');

class IconSelectionConfig extends React.Component<IconSelectionConfigProps> {
  onChange = (customId: ReactText, option?: AnyObj) => {
    const { iconSelectionConfigType, iconSelectionConfigId: propsKey, customParams, onChange, onFormChange } = this.props;
    const value = option?.icon_line ? iconSelectionConfigType === 'name' ? option?.icon_line : (
      <Icon weId={`${this.props.weId || ''}_r0b255`} name={option?.icon_line} />
    ) : '';
    customParams ?   onChange?.({
      [propsKey]: value,
    }, customParams) : onFormChange?.({
      [propsKey]: value,
    });
  }
  getDefaultValue = () => {
    const { iconSelectionConfigType, config, iconSelectionConfigId: propsKey } = this.props;
    let key: ReactText = '';
    const iconName = iconSelectionConfigType === 'name' ? config?.[propsKey] : config?.[propsKey]?.props?.name;
    if (iconName) {
      key = datas?.find((da) => da.icon_line === iconName)?.key || '';
    }
    return key;
  }
  render() {
    const defaultValue = this.getDefaultValue();
    const { triggerProps: _triggerProps, ...resProps } = this.props;
    const triggerProps: AnyObj = classUseMemo('triggerProps', this, () => ({ ..._triggerProps, popupPlacement: 'bottomRight' }), [_triggerProps]);
    return (
      <IconSelection weId={`${this.props.weId || ''}_qe9v2u`}
        {...resProps}
        bindTriggerContainer={false}
        triggerProps={triggerProps}
        onChange={this.onChange}
        defaultValue={defaultValue}
      />
    )
  }
}

export default IconSelectionConfig;