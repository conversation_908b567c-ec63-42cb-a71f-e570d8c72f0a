import { mReminderTimeClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { MReminderTimeType } from "./types";


const MReminderTime = Loadable({
  name: 'MReminderTime', loader: () => import(
    /* webpackChunkName: "m-reminder-time" */
    './MReminderTime'
  )
}) as MReminderTimeType;

MReminderTime.defaultProps = {
  prefixCls: mReminderTimeClsPrefix,
}

export default MReminderTime;

export type { MReminderTimeType, MReminderTimeProps, MReminderTimeSingleProps } from './types';
