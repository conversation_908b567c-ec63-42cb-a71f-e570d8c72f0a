import { forwardRef, useCallback, useState } from "react";
import { MCheckbox, FormItem, MInput } from "@weapp/ui";
import { MReminderTimeMultipleProps } from "./types";
import { withRouter } from "react-router-dom";
import { getLabel } from "@weapp/utils";

const { MInputNumber } = MInput;
const MReminderTimeMultiple = withRouter(forwardRef<{}, MReminderTimeMultipleProps>(
  (props) => {

    const { options, prefixCls, value, onChange } = props;

    const [inputValue, updateInputValue] = useState<number>(value?.remindCountCircle);
    const onChangeCheckbox = useCallback((checkboxValue: any) => {
      if (checkboxValue.pop) {
        onChange?.({
          ...value,
          remindTimeCircle: checkboxValue.pop() || '0',
        })
      }
    }, [onChange, value]);

    const onInputChange = useCallback((value: any) => { updateInputValue(value); }, []);
    const onInputBlur = useCallback((inputValue: any) => {
      if (inputValue > 5) {
        inputValue = 5;
      }
      if (inputValue < 1) {
        inputValue = 1;
      }
      updateInputValue(inputValue);
      onChange?.({
        ...value,
        remindCountCircle: inputValue,
      })
    }, [onChange, value]);

    return (
      <div>
        <MCheckbox
          weId={`${props.weId || ''}_m9sqsm`}
          data={options}
          isHoldRight
          value={value?.remindTimeCircle}
          onChange={onChangeCheckbox}
        />
        <FormItem weId={`${props.weId || ''}_e5o7t0`} 
          label={getLabel('261634','循环次数')}
          labelSpan={8}
          isMobile
          className={`${prefixCls}-single-time-formItem`}
        >
          <MInputNumber weId={`${props.weId || ''}_mte1t6`}
            className={`${prefixCls}-single-time-circle-count`}
            placeholder=""
            value={inputValue}
            allowClear
            onChange={onInputChange}
            onBlur={onInputBlur}
          />
        </FormItem>
      </div>
    )
  } 
));

export default MReminderTimeMultiple;