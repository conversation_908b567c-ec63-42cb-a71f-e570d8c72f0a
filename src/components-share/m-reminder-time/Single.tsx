import { forwardRef, useCallback, useMemo, useState } from "react";
import { FormItem, MDialog, MCheckbox,  FooterProps, Button, MPickerView, AnyObj } from "@weapp/ui";
import { MReminderTimeSingleProps } from "./types";
import { withRouter } from "react-router-dom";
import { getLabel } from "@weapp/utils";

const { Item } = FormItem;
const footer: FooterProps[] | undefined = [];
const MReminderTimeSingle = withRouter(forwardRef<{}, MReminderTimeSingleProps>(
  (props) => {
    const { options, prefixCls, value, onChange } = props;
    const [time, updateTime] = useState<AnyObj>({day: value?.remindTimeDaySingle || 0, hour: value?.remindTimeHourSingle || 0, minute: value?.remindTimeMinSingle || 0});
    const [visible, updateVisible] = useState<boolean>(false);

    const optionGroups = useMemo(() => {
      return {
        day: Array.from(Array(31), (v, k) => {
          const t: any = k;
          return {
            value: t,
            label: `${t}${getLabel('261601','天') }`
          };
        }),
        hour: Array.from(Array(24), (v, k) => {
          return {
            value: k,
            label: `${k}${getLabel('261564','小时')}`
          };
        }),
        minute: Array.from(Array(60), (v, k) => {
          return {
            value: k,
            label: `${k}${getLabel('261565','分钟')}`
          };
        }),
      }
    }, []);

    const onChangeCheckbox = useCallback((checkboxValue: any) => {
      if (checkboxValue.pop) {
        onChange?.({ ...value, remindTimeSingle: checkboxValue.pop() || '0',
        remindTimeDaySingle: time?.day || 0,
        remindTimeDaySingle_desc: getLabel('261601','天'),
        remindTimeHourSingle: time?.hour || 0,
        remindTimeHourSingle_desc: getLabel('261564','小时'),
        remindTimeMinSingle: time?.minute || 0,
        remindTimeMinSingle_desc: getLabel('261565','分钟'), })
      }
    }, [onChange, time?.day, time?.hour, time?.minute, value]);

    const openPickView = useCallback(() => { updateVisible(true); }, []);
    const closePickView = useCallback(() => { updateVisible(false); }, []);
    const onChangeTime = useCallback((value: AnyObj) => {
      updateTime(value);
    }, []);
    const changeTime = useCallback(() => {
      onChange?.({ ...value,
        remindTimeDaySingle: time?.day || 0,
        remindTimeDaySingle_desc: getLabel('261601','天'),
        remindTimeHourSingle: time?.hour || 0,
        remindTimeHourSingle_desc: getLabel('261564','小时'),
        remindTimeMinSingle: time?.minute || 0,
        remindTimeMinSingle_desc: getLabel('261565','分钟'),
      })
      closePickView();
    }, [closePickView, onChange, value, time]);

    const getTitle = useCallback(() => {
      return (
        <div className={`${prefixCls}-single-nav`} >
          <div className={`${prefixCls}-single-nav-left`}>
            <Button weId={`${props.weId || ''}_l9ck2h`}  type="link" className={`cancel`} onClick={closePickView}>{getLabel('223917','取消')}</Button>
          </div>
          <div className={`${prefixCls}-single-nav-right`}>
            <Button weId={`${props.weId || ''}_e0xsgv`}  type="link" className={`confirm`} onClick={changeTime}>{getLabel('261599','确定')}</Button>
          </div>
        </div>
      )
    }, [changeTime, closePickView, prefixCls, props.weId]);

    return (
      <div>
        <MCheckbox
          weId={`${props.weId || ''}_m9sqsm`}
          data={options}
          isHoldRight
          value={value?.remindTimeSingle}
          onChange={onChangeCheckbox}
        />
        {
          value?.remindTimeSingle?.includes('9') && <>
            <FormItem weId={`${props.weId || ''}_e5o7t0`} 
              label={getLabel('261635','自定义时间')}
              labelSpan={8}
              isMobile
              className={`${prefixCls}-single-time-formItem`}
            >
              <Item weId={`${props.weId || ''}_2g0wwr`}
                isHoldRight
                onClick={openPickView}
              >
                <div className={`${prefixCls}-single-time-desc`}>
                  {time?.day || 0}{getLabel('261601','天')}{time?.hour || 0}{getLabel('261564','小时')}{time?.minute || 0}{getLabel('261565','分钟')}
                </div>
              </Item>
            </FormItem>
          </>
        }
        {
          visible && (
            <MDialog weId={`${props.weId || ''}_3k56nh`}
              visible={visible}
              mask
              maskClosable
              onClose={closePickView}
              placement="bottom"
              footer={footer}
              getTitle={getTitle}
              className={`${prefixCls}-single-dialog`}
            >
              <MPickerView weId={`${props.weId || ''}_13qyai`}
                optionGroups={optionGroups}
                value={time}
                onChange={onChangeTime}
              />
            </MDialog>
          )
        }
      </div>
    )
  } 
));

export default MReminderTimeSingle;