.@{mReminderTimeClsPrefix} {
  width: 100%;
  &-container-content {
    height: 100%;
    padding-bottom: var(--m-dialog-button-group-height);
    background-color: var(--m-bg-base);
    overflow: auto;
    --webkit-overflow-scrolling: touch;
    
    .ui-m-checkbox-wrapper {
      padding: 0 var(--h-spacing-lg);
    }
  }
  &-single-nav {
    display: flex;
    height: calc(var(--hd)* 50);
    justify-content: space-between;
    align-items: center;
    padding: 0 calc(var(--hd)* 30);
    &-left, &-right {
      .ui-btn {
        font-size: var(--font-size-16);
      }
    }
  }
  &-single-dialog {
    .ui-m-dialog-top {
      padding: 0;
    }
    .ui-m-dialog-content {
      border-top-left-radius: var(--m-dialog-circle-radius);
      border-top-right-radius: var(--m-dialog-circle-radius);
    }
  }
  &-single-time-formItem {
    margin: calc(10 * var(--hd)) 0;

    .ui-formItem-wrapper {
      justify-content: flex-end;
    }
    .ui-formItem-item {
      width: 100%;
    }
  }
  &-single-time-desc {
    color: var(--main-fc);
  }
  &-single-time-circle-count.ui-m-input-number input {
    text-align: right;
  }

  &.readOnly {
    .ui-formItem-item-icon {
      display: none;
    }
  }
}