import { AnyObj, CheckboxDataType } from "@weapp/ui";
import { ComponentType } from "react";
import { RouteComponentProps } from "react-router-dom";

export interface MReminderTimeProps extends RouteComponentProps {
  prefixCls?: string;
  value?: AnyObj;
  onChange?: (value: AnyObj) => void;
  weId?: any;
  type?: 'single' | 'multiple';
  options?: CheckboxDataType;
  readOnly?: boolean;
}

export type MReminderTimeType = ComponentType<MReminderTimeProps>;

export interface MReminderTimeSingleProps extends RouteComponentProps {
  prefixCls?: string;
  value?: AnyObj;
  onChange?: (value: AnyObj) => void;
  weId?: any;
  options?: CheckboxDataType;
}

export interface MReminderTimeMultipleProps extends RouteComponentProps {
  prefixCls?: string;
  value?: any;
  onChange?: (value: any) => void;
  weId?: any;
  options?: CheckboxDataType;
}