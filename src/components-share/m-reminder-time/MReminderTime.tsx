import { forwardRef, useCallback, useEffect, useMemo, useState } from "react";
import { AnyObj, FormItem, MDialog, utils } from "@weapp/ui";
import { MReminderTimeProps } from "./types";
import { withRouter } from "react-router-dom";
import MReminderTimeSingle from "./Single";
import MReminderTimeMultiple from "./Multiple";
import { classnames, getLabel } from "@weapp/utils";

const { Item } = FormItem;
const { formatParentPath, getRandom, isNil } = utils;
const { ButtonGroup } = MDialog;

const MReminderTime = withRouter(forwardRef<{}, MReminderTimeProps>(
  (props) => {

    const { prefixCls, history, value, onChange, type = 'single', options, readOnly } = props;

    const [mReminderTimeSingleId] = useState<string>(`mReminderTimeSingle_dialog_${getRandom()}`);
    const [val, updateVal] = useState<AnyObj | undefined>(value);
    let parentPath = formatParentPath(props as any);
    const path = `${parentPath}/reminder/${mReminderTimeSingleId}`;
    const title = type === 'single' ? getLabel('261632','提醒时间-单次') : getLabel('261633','提醒时间-循环');
    
    useEffect(() => { updateVal(value); }, [value]);

    const open = useCallback(() => { !readOnly && history.push(path) }, [history, path, readOnly]);
    const onSave = useCallback(() => {
      val && onChange?.(val);
      history.go(-1);
    }, [history, onChange, val]);

    const btns = useMemo(() => [
      {
        id: 'sure',
        content: getLabel('261599','确定'),
        onClick: onSave,
        type: 'primary',
      }
    ] as any[], [onSave]);
    
    let tip = '';
    if (type === 'single') {
      const remindTimeSingle = value?.remindTimeSingle || '0';
      tip = options?.find((da) => da.id === remindTimeSingle)?.content || '';
    } else {
      const remindTimeCircle = value?.remindTimeCircle || '0';
      const remindCountCircle = value?.remindCountCircle;
      tip = options?.find((da) => da.id === remindTimeCircle)?.content || '';
      if (!isNil(remindCountCircle)) {
        tip += `${getLabel('261570','循环提醒')}${remindCountCircle}${getLabel('261602','次')}`;
      }
    }
    const cls = classnames(`${prefixCls}`, {
      readOnly,
    })
    return (
      <div className={cls}>
        <Item weId={`${props.weId || ''}_507nah`} placeholder={getLabel('261563','请选择')} onClick={open} isHoldRight>
          <div className={`${prefixCls}-single-time-desc`}>
            {tip}
          </div>
        </Item>
        <MDialog
          weId={`${props.weId || ''}_ce159q`}
          isRouteLayout
          path={path}
          className={`${prefixCls}-container`}
          title={title}
        >
          <div className={`${prefixCls}-container-content`}>
            {
              type === 'single' ? (
                <MReminderTimeSingle weId={`${props.weId || ''}_4qzcuo`} options={options} prefixCls={prefixCls} value={val} onChange={updateVal} />
              ) : <MReminderTimeMultiple weId={`${props.weId || ''}_dwykyn`} options={options} prefixCls={prefixCls} value={val} onChange={updateVal} />
            }
          </div>
          <ButtonGroup weId={`${props.weId || ''}_cjcwem`} datas={btns} />
        </MDialog>
      </div>
    )
  }
));

export default MReminderTime;