.@{menuDesignClsPrefix} {
  font-size: var(--font-size-12);

  &-browser-input {
    position: relative;

    &:hover &-del-icon {
      display: inline-block;
    }

    .ui-input-suffix {
      padding-left: var(--v-spacing-lg);
    }

    &-del-icon {
      display: none;
      position: absolute;
      padding: 5px 2px;
      right: 22px;
      color: var(--invalid-fc);
      cursor: pointer;

      &:hover {
        color: var(--secondary-fc);
      }
    }
  }

  &-top {
    padding: var(--v-spacing-md) var(--h-spacing-lg);
    font-size: var(--font-size-12);
  }

  &-addBtn {
    color: var(--primary);
    padding: var(--v-spacing-md) var(--h-spacing-lg);
    display: flex;
    align-items: center;
    cursor: pointer;

    &-title {
      vertical-align: middle;
      padding: 0 var(--h-spacing-xs);
      line-height: calc(var(--hd)*16);
    }
  }

  &-list-item:not(:last-child) {
    border-bottom: calc(var(--hd)*1) solid var(--diviling-line-color);
  }

  &-list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    color: var(--regular-fc);
    min-height: calc(var(--hd)*40);

    &.disabled {
      cursor: not-allowed;
      color: var(--invalid)
    }

    &-left {
      padding-left: var(--h-spacing-lg);

      &-title {
        vertical-align: middle;
        padding-left: var(--h-spacing-md);
      }

      .my-handle {
        cursor: pointer;
      }
    }


  }

  /* 事项筛选 */
  &-matterFilter {
    cursor: pointer;
    color: var(--secondary-fc);

    .ui-icon {
      padding-right: var(--h-spacing-md);
    }
  }

  &-condition {
    &-content {
      background-color: var(--white-base);
      margin-top: calc(var(--hd)*34);
    }
  }

  /* 自定义组件样式调整 */
  &-customContainer {
    .ui-formItem-module {
      border-bottom: var(--form-item-border-module);

      &:last-child {
        border-bottom: 0;
      }
    }

    .ui-formItem-module.ui-formItem-noLabel {
      padding: 0;
    }
  }

  &-iframe {
    width: 100%;
    height: 100%;
    border: 0;
  }

  .ui-empty-title {
    font-size: var(--font-size-12);
  }

  &-filter-table {
    margin-top: calc(-36*var(--hd));

    .ebcomponents-config-filter {
      width: 100%;
    }

    .ui-select {
      width: 100%;
    }

    .ui-scope-wrap {
      .ui-input-number {
        width: 55px;
      }
    }
  }
}