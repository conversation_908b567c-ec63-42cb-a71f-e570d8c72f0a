import { menuDesignClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { MenuDesignType } from "./type";

const MenuDesign = Loadable({
  name: 'MenuDesign', loader: () => import(
    /* webpackChunkName: "common_menu_design" */
    './main'
  )
}) as MenuDesignType;

MenuDesign.defaultProps = {
  prefixCls: menuDesignClsPrefix,
}

export default MenuDesign;

export type { MenuDesignType, MenuDesignProps, MenuDesignDataType } from './type';

export { default as menuDesignVisual } from './visual';