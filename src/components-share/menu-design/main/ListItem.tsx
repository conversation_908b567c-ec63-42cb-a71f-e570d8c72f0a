import { Icon, Menu } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import React, { memo, useCallback, useMemo } from "react";
import { ListItemProps } from "../type";

const ListItem = memo(React.forwardRef<{}, ListItemProps>(
  (props) => {
    const { prefixCls, data, onMenuChange, defaultEditable = true } = props;
    const menuData = useMemo(() => {
      const btn = data.disabled ? { id: 'enable', content: getLabel('207004','启用') } : { id: 'disable', content: getLabel('213326','停用') };
      return data.menuContentConfig ? [
        { id: 'edit', content: getLabel('207020','编辑') },
        btn,
        { id: 'delete', content: getLabel('207017','删除') }
      ] : defaultEditable ? [
        { id: 'rename', content: getLabel('213327','重命名') },
        btn,
      ] : [btn]
    }, [data.disabled, data.menuContentConfig]);

    const triggerProps = useMemo(() => {
      return {
        popupPlacement: 'bottomRight',
      }
    }, []);

    const doMenuChange = useCallback((value: string) => {
      onMenuChange?.(value, data)
    }, [onMenuChange, data]);
    return (
      <div className={`${prefixCls} ${data.disabled && 'disabled'}`}>
        <div className={`${prefixCls}-left`}>
          <Icon weId={`${props.weId || ''}_95n1w9`} name="Icon-move" className="my-handle" />
          <span className={`${prefixCls}-left-title`}>{data.content}</span>
        </div>
        <div className={`${prefixCls}-right`}>
          <Menu weId={`${props.weId || ''}_jolhcx`}
            data={menuData}
            type="select"
            selectIcon="Icon-more-o"
            selectType="iconOverlay"
            triggerProps={triggerProps}
            onChange={doMenuChange}
          />
        </div>
      </div>
    )
  }
))

export default ListItem;