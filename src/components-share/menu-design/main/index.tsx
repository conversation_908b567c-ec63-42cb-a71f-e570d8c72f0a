import React, { memo, useCallback, useState, useMemo, useEffect } from 'react';
import { classnames, getLabel, request } from '@weapp/utils';
import { List, ListData, Icon, utils, Dialog, AnyObj, Empty } from '@weapp/ui';
import { isObservableArray, toJS } from 'mobx'
import Setting from '../setting';
import { MenuDesignDataType, MenuDesignProps, MenuDesignSingleDataType, SettingType, DesignPropsType, MenuDesignVisualDataType } from '../type';
import ListItem from './ListItem';
import { MenuDesignContext } from '../utils/index';
import { doRequest } from '../apis';
import { cacheDatas } from '../visual/cacheDatas';

const { getRandom, isString } = utils;
const { confirm, message } = Dialog;

const isArray = (arr: any) => Array.isArray(arr) || isObservableArray(arr)

const getRealValue = (rawValue: any) => {
  if (isArray(rawValue)) {
    const _rawValue = rawValue.map((x: any) => (x?.id || x))
    if (_rawValue.length === 1) return _rawValue[0]
    return _rawValue
  }
  return rawValue?.id || rawValue
}

const checkSingleAuth = (prop: any, sign: string = 'eq', value: any) => {
  const { isEqual, isEmpty } = window.weappUi.utils;
  const b = getRealValue(value)
  // 过滤富文本字段
  const a = /<\/?\w*>/.test(prop) ? prop.replace(/<\/?\w*>/g, '') : prop
  if (isArray(b)) {
    if (sign === 'eq') return b.indexOf(a) > -1;
    if (sign === 'neq') return b.indexOf(a) === -1;
    if (sign === 'startWith') return b.find((_b: any) => (isString(a) && isString(_b) && a.indexOf(_b) === 0));
    if (sign === 'endWith') return b.find((_b: any) => (isString(a) && isString(_b) && a.lastIndexOf(_b) === a.length - _b.length));
    if (sign === 'contains') return (isString(a) || isArray(a)) && b.find((_b: any) => (a.indexOf(_b) >= 0));
    if (sign === 'uncontains') return (!isString(a) && !isArray(a)) || b.every((_b: any) => (a.indexOf(_b) < 0));
    if (sign === 'in') return (isString(a) || isArray(a)) && b.find((_b: any) => (a.indexOf(_b) >= 0));
    if (sign === 'nin') return (!isString(a) && !isArray(a)) || b.every((_b: any) => (a.indexOf(_b) < 0));
  }
  if (sign === 'eq') return isArray(a) ? a.indexOf(b) > -1 : isEqual(a, b);
  if (sign === 'neq') return isArray(a) ? a.indexOf(b) === -1 : !isEqual(a, b);
  if (sign === 'gt') return a > b;
  if (sign === 'lt') return a < b;
  if (sign === 'gte') return a >= b;
  if (sign === 'lte') return a <= b;
  if (sign === 'startWith') return isString(a) && isString(b) && a.indexOf(b) === 0;
  if (sign === 'endWith') return isString(a) && isString(b) && a.lastIndexOf(b) === a.length - b.length;
  if (sign === 'contains') return (isString(a) || isArray(a)) && a.indexOf(b) >= 0;
  if (sign === 'uncontains') return (!isString(a) && !isArray(a)) || a.indexOf(b) < 0;
  if (sign === 'in') return (isString(a) || isArray(a)) && a.indexOf(b) >= 0;
  if (sign === 'nin') return (!isString(a) && !isArray(a)) || a.indexOf(b) < 0;
  if (sign === 'empty') return isEmpty(a);
  if (sign === 'nempty') return !isEmpty(a);
  if (sign === 'range') return !isEmpty(a) && !isEmpty(b) && Number(a) >= Number(b.min || 0) && Number(a) <= Number(b.max || 0);

  return true;
}

const filterVisibility = (result: ListData, designProps: DesignPropsType = {} as any) => {
  if (!designProps) return result
  const visibleResult = result.filter((item: any) => {
    if (item.hidden) {
      return false
    }
    const { settingType, matterFilter } = item?.menuContentConfig?.auth ?? {}
    if (settingType === 'normal' && matterFilter?.length) {
      for (let i = 0; i < matterFilter.length; i++) {
        const key = matterFilter[i].condition;
        const compareKey = key + "_compares";
        const checked = checkSingleAuth(designProps[key], matterFilter[i][compareKey], matterFilter[i][key]);
        // 如果有一项校验没通过就不展示
        if (!checked) {
          return false
        }
      }
      return true;
    }
    return true
  })
  return { value: visibleResult, visualData: result }
}

const getMultilangLabel = (data: MenuDesignVisualDataType) => {
  const targetIds = data.filter((item: any) => {
    return typeof item.content !== 'string' && !!item?.localContent?.targetId
  }).map((item: any) => item?.localContent?.targetId)

  return request?.({
    url: '/api/bs/i18n/multilangLabel/getTargetId2labelId',
    method: 'POST',
    data: {
      targetIds: targetIds.join(','),
      tenantKey: window.TEAMS?.currentTenant?.tenantKey,
      langId: window.TEAMS?.locale?.lang,
    },
  }).then((res: any) => {
    const result = res.data
    const _data = data.map((item: any) => {
      if (item.localContent?.targetId) {
        const { targetId, nameAliasLabelId, nameAlias } = item.localContent
        const translatedValue = result[targetId][nameAliasLabelId]
        item.content = translatedValue || nameAlias
        item.localContent.nameAlias = item.content
      }
      return item
    })
    return _data
  });
}

const emptyStyle = { width: 50, height: 50 };
const MenuDesign = memo(React.forwardRef<{}, MenuDesignProps>(
  (props, ref) => {
    const { prefixCls, className, style, value: _datas, onChange, designProps, dialogProps, onAdd, onClose: _onClose } = props;
    const [visible, setVisible] = useState(false);
    const [datas, setDatas] = useState<MenuDesignDataType>([]); // 当前选中页签的数据
    const [updateData, setUpdateData] = useState({}); // Setting设置界面的数据
    const [settingType, setSettingType] = useState<SettingType>('add');
    const [moduleDatas, updateModulesDatas] = useState<AnyObj>({}); // 模块选择条件
    const [matterFilter, updateMatterFilter] = useState<any[]>([]); // 事项筛选条件
    const wrapCls = classnames(prefixCls, className);
    const bindKey = designProps?.bindKey;

    const getModulesCondition = useCallback((val: string, otherParams?: AnyObj) => {
      updateModulesDatas({ modules: moduleDatas?.modules, condition: [] });
      doRequest('getFormFields', val, { ...otherParams, params: { needVariableProps: '1', ...otherParams?.params } }).then((res) => {
        res.status && updateModulesDatas({ modules: moduleDatas?.modules, condition: res.data || [] });
      })
    }, [moduleDatas?.modules])

    useEffect(() => {
      let actualData = toJS(_datas) as MenuDesignVisualDataType
      if (typeof actualData?.visualData !== 'undefined') {
        actualData = actualData.visualData
      }

      getMultilangLabel(actualData).then((withLabelData) => {
        setDatas(withLabelData);
      })

      // 只有排序后才走排序逻辑存在问题，先排序在刷新页面编辑排序功能消失，进入初始化排序数据
      bindKey && cacheDatas.setSortableIds(bindKey, actualData);
      // 缓存存在的问题，撤销重做存在异常，拖拽排序取消后任然生效，所以去掉缓存
      // 缓存过数据，以缓存的为准
      // const data = bindKey ? cacheDatas.getData(bindKey) : null;
      // if (data) {
      //   setDatas([...data]); // 转业务mobx数据，解决拖拽排序报错问题
      // } else {
      //   setDatas([..._datas]);
      // }
    }, [_datas, bindKey]);

    useEffect(() => {
      return () => {
        console.error('卸载MenuDesign');
      }
    }, [])
    useEffect(() => {
      doRequest('getModules').then((result) => {
        if (result.status) {
          const modules = result.data || [];
          updateModulesDatas({ modules });
        }
      });
      designProps?.module && doRequest('getFormFields', designProps?.module, { params: { needVariableProps: '0' } }).then((res) => {
        res.status && updateMatterFilter(res.data || []);
      })
    }, [designProps]);

    const openSettingDialog = useCallback((type: SettingType, data?: ListData) => {
      setSettingType(type);
      setVisible(true);
      if (type === 'add') { // 获取满足条件
        // 模块处理报错todo by juno先注视掉
        const defaultVal = '' // moduleDatas?.modules?.[0]?.route || '';
        updateModulesDatas({ ...moduleDatas, condition: [] })
        defaultVal && getModulesCondition(defaultVal);
      } else if (type === 'edit' && data?.menuContentConfig?.type === 'moduleData') {
        // 模块数据 获取对应满足条件
        const id = data?.menuContentConfig?.moduleDataSetting?.moduleData?.[0]?.condition || '';
        if (id) {
          const defaultVal = moduleDatas?.modules?.find((da: { id: any; }) => da.id === id);
          updateModulesDatas({ ...moduleDatas, condition: [] })
          defaultVal && defaultVal.route && getModulesCondition(defaultVal.route);
        }
      }
    }, [moduleDatas, getModulesCondition,])

    const add = useCallback(() => {
      setUpdateData({});
      openSettingDialog('add');
      onAdd?.();
    }, [openSettingDialog, onAdd]);

    const onClose = useCallback(() => {
      setVisible(false);
      _onClose?.();
    }, [_onClose]);

    // 更新数据
    const doUpdateChange = useCallback((result) => {
      if (bindKey) {
        cacheDatas.setData(bindKey, result);
      }
      setDatas([...result]); // 转业务mobx数据，解决拖拽排序报错问题
      // 过滤实时渲染通用事项不符合条件的数据
      const visibleResult = filterVisibility(result, designProps) as MenuDesignDataType
      onChange?.(toJS(visibleResult));
    }, [bindKey, onChange]);

    const onSave = useCallback((formDatas: ListData, oldData: ListData) => {
      let result = datas;
      if (settingType === 'add') { // 新增页签
        const _data = datas?.map?.(item => ({ ...item, isNew: false }))
        result = [...(_data || []), { id: getRandom(), ...formDatas, isNew: true }];
      } else if (oldData?.id) { // 更新页签配置
        result = datas?.map((data) => {
          if (data.id === oldData?.id) {
            return {
              ...data,
              ...formDatas,
              isNew: false
            }
          }
          return { ...data, isNew: false };
        })
      }
      doUpdateChange(result);
      setVisible(false);
    }, [datas, settingType, doUpdateChange]);

    const onSortEnd = useCallback((data: any) => {
      doUpdateChange(data.map((d: any) => ({ ...d, isNew: false })));
      bindKey && cacheDatas.setSortableIds(bindKey, data);
    }, [doUpdateChange, bindKey]);

    const onMenuChange = useCallback((value: string, data: ListData) => {
      if (!isString(data.content)) {
        message({
          type: 'info',
          content: getLabel('213263','该项页签暂不支持配置')
        })
        return;
      }
      // 重命名/编辑
      setUpdateData(data);
      if (['rename', 'edit'].indexOf(value) >= 0) {
        // if (!isString(data.content)) {
        //   message({
        //     type: 'info',
        //     content: '非String类型的页签名称暂不支持重命名'
        //   })
        //   return;
        // }
        openSettingDialog(value as SettingType, data);
        return;
      }
      // 停用/删除
      if (data?.id) {
        if (value === 'delete') {
          confirm({
            content: getLabel('213264','确定删除吗？'),
            onOk: () => {
              const result = datas
                .filter((tempData: MenuDesignSingleDataType) => tempData.id !== data?.id)
                .map((tempData) => ({ ...tempData, isNew: false }));
              doUpdateChange(result);
            }
          })
          return;
        }
        const result = datas.map((tempData: MenuDesignSingleDataType) => {
          if (tempData.id === data?.id) {
            const _data = {
              ...tempData,
              disabled: value === 'disable',
              hide: value === 'disable',
              custom: true,
              isNew: false
            }
            if (!tempData.__content) {
              _data.__content = tempData.content
            }
            return _data
          }
          return { ...tempData, isNew: false };
        })
        doUpdateChange(result);
      }
    }, [openSettingDialog, datas, doUpdateChange]);

    const customRenderItem = useCallback((item: ListData) => {
      if (item.hidden) return <div style={{ display: 'none' }}>{item.content}</div>;
      return (
        <ListItem
          key={item.id}
          weId={`${props.weId || ''}_pk46ir`}
          data={item}
          prefixCls={`${prefixCls}-list-item`}
          onMenuChange={onMenuChange}
          defaultEditable={designProps?.defaultEditable}
        />
      )
    }, [props.weId, prefixCls, onMenuChange]);

    const sortableOptions = useMemo(() => { return { handle: '.my-handle' } }, []);

    const providerValue = useMemo(() => { 
      return { prefixCls, labelSpan: 4, designProps, moduleDatas, getModulesCondition, matterFilter }
    }, [prefixCls, designProps, moduleDatas, getModulesCondition, matterFilter]);

    const showData = useMemo(() => {
      return datas.filter(_item => !_item.hidden)
    }, [datas])
    return (
      <div className={`${wrapCls} inspect-disabled`} style={style}>
        <MenuDesignContext.Provider weId={`${props.weId || ''}_hlq1q9`} value={providerValue}>
          {
            showData && showData?.length > 0 ? (
              <List weId={`${props.weId || ''}_ml2yjx`}
                sortable={true}
                style={style}
                direction="column"
                sortableOptions={sortableOptions}
                data={datas}
                customRenderItem={customRenderItem}
                bordered={true}
                isStopMouseDown={true}
                onSortEnd={onSortEnd}
              />
            ) : (
              <Empty weId={`${props.weId || ''}_dptx1g`}
                title={getLabel('213265','暂无数据')}
                image={<Icon weId={`${props.weId || ''}_u6eswh`} name={'Icon-empty-file'} style={emptyStyle} />}
              />
            )
          }

          <div className={`${prefixCls}-addBtn`} onClick={add}>
            <Icon weId={`${props.weId || ''}_qn7l2d`} name="Icon-add-to01" />
            <span className={`${prefixCls}-addBtn-title`}>{getLabel('213266','添加页签')}</span>
          </div>
          <Setting weId={`${props.weId || ''}_hect88`}
            visible={visible}
            onClose={onClose}
            onSave={onSave}
            data={updateData}
            settingType={settingType}
            dialogProps={dialogProps}
            labelSpan={4}
            prefixCls={prefixCls}
          />
        </MenuDesignContext.Provider>
      </div>
    );
  }
))

export default MenuDesign;