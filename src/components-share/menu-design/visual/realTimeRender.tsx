import { ComponentProps, ReactNode } from "react";
import { regOvProps } from "@weapp/utils";
import { AnyObj, <PERSON><PERSON><PERSON><PERSON>po<PERSON>, <PERSON><PERSON><PERSON>, DynamicRenderComponent } from "@weapp/ui";
import { MenuDesignSingleDataType } from "../type";
import { findInstanceByFiber, getModuleCondition } from "./common";
import { menuDesignClsPrefix } from "../../../constants";

const { BrowserPanel } = Browser;
interface Config {
  [id: string]: AnyObj;
}

interface OverwriteProps {
  /* 渲染内容 */
  content: Element | JSX.Element | ReactNode | string | Element;
}
interface SinglePluginType {
  /* 复写内容 */
  overwrite: (data: any, changes: ComponentProps<any>, visualContext: AnyObj) => OverwriteProps;
}
interface PluginType {
  [key: string]: SinglePluginType;
}
class RealTimeRender {
  bindKey: string = '';
  isSingleContent: boolean = false; // 单个MenuContent模式
  id: string = ''; // 当前操作的页签id
  config: Config = {};
  plugins: PluginType = {};
  /**
   * 清空状态
   */
  clear = () => {
    this.bindKey = '';
    this.isSingleContent = false;
    this.id = '';
    this.config = {};
  }
  /**
   * 初始化
   * @param changes 
   * @param visualContext 
   * @returns 
   */
  init = (changes: ComponentProps<any>, visualContext: AnyObj) => {
    if (!changes.bindKey) {
      console.error('Menu未配置bindKey');
      return;
    }
    this.bindKey = changes.bindKey;
    this.isSingleContent = changes?.designProps?.isSingleContent || false;
    // 注册onChange复写事件
    this.regOvMenuProps();
    // 注册MenuContent复写事件
    this.regOvChildren();
    // 过滤changes.data，处理自定义页签对应的MenuContent实时渲染效果
    this.update(changes, visualContext);
  }
  /**
   * 更新配置之后自动选择tab项或过滤disabled项
   * @param tab 
   * @param bindKey
   * @param visualContext 
   */
  autoSelect = (tab: any, bindKey: string, visualContext: AnyObj) => {
    const value = tab?.id
    const menuFibers = visualContext?.findFibers('weappUi.Menu', (props: { bindKey: any; }) => props.bindKey === bindKey);
    const menuContentFibers = visualContext?.findFibers('weappUi.MenuContent', (props: { bindKey: any; }) => props.bindKey === bindKey);
    menuFibers?.forEach((_fiber: any) => {
      const instance = findInstanceByFiber(_fiber);
      _fiber.pendingProps = {
        ..._fiber.memoizedProps,
        value,
      }
      if (_fiber.alternate) {
        _fiber.alternate.pendingProps = _fiber.pendingProps;
      }
      instance?.forceUpdate?.();
    })
    menuContentFibers?.forEach((_fiber: any) => {
      const instance = findInstanceByFiber(_fiber);
      _fiber.pendingProps = {
        ..._fiber.memoizedProps,
        value,
      }
      if (_fiber.alternate) {
        _fiber.alternate.pendingProps = _fiber.pendingProps;
      }
      instance?.forceUpdate?.();
    })
  }
  /**
   * 更新配置
   * @param changes 
   * @param visualContext 
   */
  update = (changes: ComponentProps<any>, visualContext: AnyObj) => {
    changes?.data?.forEach((data: MenuDesignSingleDataType) => {
      if (data.custom && data.menuContentConfig?.type) this.overwrite(data.menuContentConfig?.type, data, changes, visualContext);
    })
    // 自动选中新增menu
    const newTab = changes?.data?.find((d: any) => d.isNew)
    if (newTab) {
      return this.autoSelect(newTab, changes?.bindKey, visualContext)
    }
    // 自动过滤无效menu
    const fibers = visualContext?.findFibers('weappUi.Menu', (props: { bindKey: any; }) => props.bindKey === changes.bindKey);
    const currentValue = fibers?.[0]?.memoizedProps?.value
    const enableTab = changes?.data?.find((d: any) => d.id === currentValue && !d.disabled)
    if (!enableTab) {
      const nextEnableTab = changes?.data?.find((d: any) => !d.disabled)
      return this.autoSelect(nextEnableTab, changes?.bindKey, visualContext)
    }
  }
  /**
   * 更新config配置
   * @param id 页签id
   * @param _config 
   */
  updateConfig = (id: string, _config: AnyObj) => {
    this.id = id;
    this.config[id] = { ...this.config[id], ..._config };
  }

  /**
   * 按照type类型处理复写的内容
   * @param type 
   * @param data 
   * @param changes 
   * @param visualContext 
   */
  overwrite = (type: string, data: any, changes: ComponentProps<any>, visualContext: AnyObj) => {
    const openWay = data.menuContentConfig?.urlSetting?.openWay;
    if (type === 'url' && openWay === 'newPage') {
      this.overwriteUrl(data, changes, visualContext);
      return;
    }
    const plugin = this.plugins[type];
    if (plugin) {
      const pluginContent = plugin.overwrite(data, changes, visualContext) as AnyObj;
      const otherParams: AnyObj = {};
      Object.keys(pluginContent).forEach((key) => {
        otherParams[`${type}_${key}`] = pluginContent[key];
      })
      this.updateConfig(data.id, {
        ...otherParams,
        type,
      });
    }
  }

  /**
   * 网页地址
   * @param data 
   * @param changes 
   * @param fiber 
   */
  overwriteUrl = (data: any, changes: ComponentProps<any>, visualContext: AnyObj) => {
    const fibers = visualContext?.findFibers('weappUi.Menu', (props: { bindKey: any; }) => props.bindKey === changes.bindKey);
    const fiber = fibers?.[0];
    const instance = findInstanceByFiber(fiber);
    if (fiber && instance) {
      const oldChange = fiber.memoizedProps?.onChange;
      const openWay = data.menuContentConfig?.urlSetting?.openWay;
      const url = data.menuContentConfig?.urlSetting?.url;
      const _onChange = (...args: any) => {
        if (args[0] === data.id && openWay === 'newPage') window.open(url);
        else oldChange?.(...args);
      }
      fiber.pendingProps = {
        ...fiber.memoizedProps,
        onChange: _onChange,
      }
      if (fiber.alternate) {
        fiber.alternate.pendingProps = fiber.pendingProps;
      }
      this.updateConfig(data.id, { url, openWay, type: 'url' }); // 同步更新配置
      instance?.forceUpdate?.();
    }
  }
  /**
   * Menu参数复写
   * @param newProps 
   */
  regOvMenuProps = () => {
    regOvProps('weappUi', 'Menu', (props: any) => {
      if (props.bindKey !== this.bindKey) return;
      const oldChange = props.onChange;
      return {
        ...props,
        onChange: (...args: any[]) => {
          const nowId = args[0]; // 获取当前选中的页签id
          const config = this.config[nowId]; // 获取当前自定义设置页签的配置
          this.id = nowId; // 存在自定义配置的页签, 同步更新id标记
          if (config && this.config[this.id].type === 'url') {
            const { url, openWay } = this.config[nowId] || {};
            openWay === 'newPage' && window.open(url);
          }
          oldChange?.(...args);
        }
      };
    });
  }
  /**
   * 复写Children
   */
  regOvChildren = () => {
    regOvProps('weappUi', 'MenuContent', (props: any) => {
      if (props.bindKey !== this.bindKey) return;
      const type = this.config[this.id]?.type;
      const children = this.config[this.id]?.[`${type}_content`];
      if (!this.isSingleContent && props.dataId === 'customMenuContent') {
        const result = children && this.id === props.value ? {
          ...props,
          children,
          alwaysVisible: true,
          value: 'customMenuContent',
        } : {
          ...props,
          alwaysVisible: false,
        }
        return result;
      } else if (this.isSingleContent && children) {
        // 单个MenuContent模式
        return {
          ...props,
          children,
        }
      }
      return props;
    });
  }

  /**
   * 注册插件
   * @param config 
   */
  register = (type: string, config: SinglePluginType) => {
    this.plugins[type] = config;
  }
}

export const realTimeRender = new RealTimeRender();

realTimeRender.register('url', {
  overwrite: (data: any) => {
    const url = data.menuContentConfig?.urlSetting?.url;
    const content = (<iframe src={url} title={url} className={`${menuDesignClsPrefix}-iframe`} />)
    return { content };
  }
})

realTimeRender.register('eBuilder', {
  overwrite: (data: any, changes: ComponentProps<any>) => {
    const appId = data?.menuContentConfig?.eBuilderSetting?.eBuilder?.[0].id || '';
    const conditions = data?.menuContentConfig?.eBuilderSetting?.conditions || null;
    let dataId = changes?.designProps?.dataId || ''
    if (!dataId) {
      const { pathname } = window.location;
      const matched = pathname.match(/[0-9]{19}/g);
      dataId = matched?.pop();
    }
    const content = (
      <CorsComponent weId={`_inas66`}
        app="@weapp/ebdpage"
        compName="PageView"
        key={data?.id}
        pageId={appId}
        pageParam={{
          urlParams: { dataId },
          condition: conditions
        }}
        clientType={"PC"}
        type="EB"
        routeWrapDom={document.getElementById('root')}
      />
    )
    return { content };
  }
})

realTimeRender.register('moduleData', {
  overwrite: (data: any) => {
    const moduleDataSetting = data.menuContentConfig?.moduleDataSetting;
    if (!moduleDataSetting) return { content: '' };
    const { app, compName, conditions } = getModuleCondition(moduleDataSetting);
    if (app && compName) {
      const appName = app.indexOf('@') === 0 ? app : `@${app}`; // 接口数据错误，临时兼容一下
      // const content = (
      //   <CorsComponent weId={`_mvqqym`}
      //     app={appName}
      //     compName={compName}
      //     fixedSearchCondition={conditions}
      //   />
      // )
      const corsComponentProps = {
        app: appName,
        compName: compName,
      };
      const content = (
        <DynamicRenderComponent
          renderType="corsComponent"
          // eslint-disable-next-line weapp/jsx-no-raw-object
          corsComponentProps={corsComponentProps}
          weId={`_6pcucz`}
        />
      )
      return { content };
    }
    return { content: '' };
  }
})

realTimeRender.register('browser', {
  overwrite: (data: any) => {
    const browserSetting = data.menuContentConfig?.browserSetting;
    if (!browserSetting) return { content: '' };
    const { browser_type, module } = browserSetting;
    if (browser_type && module) {
      const content = (
        <BrowserPanel weId={`_xl99jd`} {...browserSetting} type={browser_type} />
      )
      return { content };
    }
    return { content: '' };
  }
})
export default RealTimeRender;