import { MenuProps } from "@weapp/ui";
import { ComponentProps } from "react";
import { VisualDataType } from "../../../types/common";
import getCode from './getCode';
import { realTimeRender } from './realTimeRender';

const visual = {
  customizeCode(code: string, props: Partial<MenuProps>) {
    return getCode(code, props);
  },
  /**
 * 组件props改变后，重新render后的回调, 实时渲染
 */
  componentDidUpdate(preProps: ComponentProps<any>, props: ComponentProps<any>) {
    const changes = { ...preProps, ...props };
    if (!changes.bindKey) {
      console.error('无bindKey数据');
      return;
    }
    // 过滤changes.data，处理自定义页签对应的MenuContent实时渲染效果
    const { bindKey, clear, init, update } = realTimeRender;
    if (bindKey !== changes.bindKey) {
      clear();
      init(changes, this);
    } else {
      update(changes, this);
    }
  }
} as VisualDataType<MenuProps>;

export default visual;
