import { stringify } from "../../../utils/visual";
import { MenuDesignDataType } from "../type";

class CacheDatas {

  /**
   * 页签实时渲染数据缓存
   */
  cacheDatas: Map<string, MenuDesignDataType> = new Map();

  /**
   * 页签排序数据：页签id数据缓存
   */
  sortableIds: Map<string, string[]> = new Map();
  sortableFuncName: string = 'sortData';
  setData = (bindKey: string, data: MenuDesignDataType) => {
    this.cacheDatas.set(bindKey, data);
  }

  getData = (bindKey: string) => this.cacheDatas.get(bindKey);

  setSortableIds = (bindKey: string, data: MenuDesignDataType) => this.sortableIds.set(bindKey, data.map((da) => da.id));

  getSortableIds = (bindKey: string) => this.sortableIds.get(bindKey);
  /**
   * 页签排序方法模板
   * 用于getCode：生成代码
   * 1.生成的复写代码不直接采用props.datas = 固定的页签数据
   *   即 props.datas = [xxx模块标准页签数据]
   *   代码中写死数据，后续业务新增页签或修改页签的名称等功能均将不生效
   * 2.排序方法思路：
   *  2.1 记录sortablesIds, main/index onSortEnd 生成，记录在sortableIds
   *  2.2 sortableIds 格式 { bindKey: [...页签数据id] }
   *  2.3 复写Menu，对比当前data 与 sortableIds 内记录的ids顺序，进行排序(sortableIds 为空则不需要)
   */
  getSortableFuncTemplate = (bindKey: string) => {
    const sortIds = this.sortableIds.get(bindKey);
    if (!sortIds) return '';
    return `
function ${this.sortableFuncName}(datas) {
  if (!(datas && datas.length > 0)) return datas;
  const sortIds = ${stringify(sortIds)};
  return [...datas].sort((a, b) => sortIds.indexOf(a.id) > -1 && sortIds.indexOf(b.id) > -1 ? sortIds.indexOf(a.id) - sortIds.indexOf(b.id) : 0)
}`;
  }
}

export type CacheDatasType = Pick<CacheDatas, keyof CacheDatas>;
export default CacheDatas;

const cacheDatas = new CacheDatas();
export { cacheDatas };