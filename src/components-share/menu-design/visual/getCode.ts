/**
 * 生成代码
 */
import { MenuProps, utils, AnyObj, MenuItemData } from "@weapp/ui";
import { stringify } from "../../../utils/visual";
import { MenuDesignDataType, MenuDesignSingleDataType } from "../type";
import { cacheDatas } from "./cacheDatas";
import { getModuleCondition } from "./common";
import { browserSettingFields } from '../utils';
import isEmpty from "lodash-es/isEmpty";

const { isString, needSync } = utils;

// 生成对应的MenuContent复写代码
// MenuContent 代码的两种模式
const getMenuContentTemplateCode = (code: string, dataId: string, isSingleContent: boolean) => {
  if (isSingleContent) {
    return `
    else if (props.dataId === '${dataId}') {
      return (
        <React.Suspense fallback={noop}>
          ${code}
        </React.Suspense>
      )
    }`
  }
  return `
    else if (props.dataId === 'customMenuContent') {
      return (
        <React.Suspense fallback={noop}>
          <Com ref={ref} {...props} />
          ${code}
        </React.Suspense>
      )
    }`;
}
// ebuilder
const getEbuilderMenuContentCode = (data: MenuDesignSingleDataType, props: Partial<any>) => {
  const appId = data?.menuContentConfig?.eBuilderSetting?.eBuilder?.[0].id || '';
  const conditions = data?.menuContentConfig?.eBuilderSetting?.conditions || null;

  // const code = `
  //     <DynamicRenderComponent
  //       renderType="corsComponent"
  //       corsComponentProps={
  //         app: "@weapp/ebdpage"
  //         compName: "RoutePageView"
  //         pageId: '${appId}'
  //         clientType: 'PC'
  //       }
  //     />`;
  const code =
    `<window.weappUi.CorsComponent
              app="@weapp/ebdpage"
              compName="PageView"
              key="${data?.id}"
              pageId="${appId}"
              pageParam={{
                urlParams: { dataId },
                condition: ${JSON.stringify(conditions)}
              }}
              clientType="PC"
              type="EB"
              routeWrapDom={document.getElementById('root')}
            />
            `;
  if (appId) return code;
  return '';
}
// 模块数据 MenuContent代码
const getModuleDataMenuContentCode = (data: MenuDesignSingleDataType, props: Partial<any>) => {
  const moduleDataSetting = data.menuContentConfig?.moduleDataSetting;
  if (!moduleDataSetting) return '';
  const { app, compName, conditions } = getModuleCondition(moduleDataSetting);
  // const condition = stringify(conditions);
  const code = `
      <DynamicRenderComponent
        renderType="corsComponent"
        corsComponentProps={
          app: "${app}",
          compName: "${compName}",
        }
      />`;
  // const code =
  //   `<window.weappUi.CorsComponent
  //             app={'${app}'}
  //             compName={'${compName}'}
  //             fixedSearchCondition={${condition}}
  //           />
  //           `;
  if (app && compName) return code;
  return '';
}
// 新页面 当前页代码
const getUrlMenuContentCode = (data: MenuDesignSingleDataType, props: Partial<any>) => {
  const urlSetting = data.menuContentConfig?.urlSetting;
  if (!urlSetting) return '';
  const url = urlSetting.url;
  if (url) return `<iframe src="${url}" title="${url}" style={{width: '100%', height: '100%', border: 0}} />`;
  return '';
}
// browser 选项对应的代码
const getBrowserMenuContentCode = (data: MenuDesignSingleDataType, props: Partial<any>) => {
  const browserSetting = data.menuContentConfig?.browserSetting;
  if (!browserSetting) return '';
  let params = '';
  browserSettingFields.forEach((key) => {
    const k = key === 'type' ? 'browser_type' : key; // BrowserPanel type被转成browser_type
    browserSetting[k] && (params +=
      ` ${key}={${stringify(browserSetting[k])}} `)
  })
  const code =
    `<window.weappUi.Browser.BrowserPanel ${params} />`;
  return code;
}
// 生成MenuContent代码
const getMenuContentCode = (data: MenuDesignDataType, props: Partial<any>) => {
  // const getMenuContentCode = (data: MenuDesignDataType, props: Partial<MenuProps>) => {
  if (!(data?.length > 0)) return '';
  /* normalCode: isSingleContent对应的代码 */
  let customContentCode = '', customCode: AnyObj[] = [];
  const isSingleContent = props?.designProps?.isSingleContent;

  data?.forEach((da) => {
    let singleCustomContentCode = ''
    if (da.menuContentConfig?.type === 'eBuilder') singleCustomContentCode = getEbuilderMenuContentCode(da, props);
    else if (da.menuContentConfig?.type === 'moduleData') singleCustomContentCode = getModuleDataMenuContentCode(da, props);
    else if (da.menuContentConfig?.type === 'url') singleCustomContentCode = getUrlMenuContentCode(da, props);
    else if (da.menuContentConfig?.type === 'browser') singleCustomContentCode = getBrowserMenuContentCode(da, props);
    if (singleCustomContentCode && isSingleContent) customContentCode += getMenuContentTemplateCode(singleCustomContentCode, da.id, true);
    else if (singleCustomContentCode) {
      customCode.push({ code: singleCustomContentCode, id: da.id });
    }
  })
  if (customCode.length > 0) {
    let result = '';
    customCode.forEach((_code) => {
      result +=
        `<window.weappUi.Menu.MenuContent
            bindKey={menuBindKey}
            dataId={'${_code.id}'}
            value={props.value}
          >
            ${_code.code}
          </window.weappUi.Menu.MenuContent>`
    })
    customContentCode = getMenuContentTemplateCode(result, '', false);
  }
  return `
import React from 'react';
import { regOvComponent } from '@weapp/utils';

const noop = () => {}
regOvComponent('weappUi', 'MenuContent', (Com) => {
  return React.forwardRef((props, ref) => {
    const canRegOvComponent = props.bindKey === menuBindKey;
    let dataId = props?.designProps?.dataId || ''
    if (!dataId) {
      const { pathname } = window.location;
      const matched = pathname.match(/[0-9]{19}/g);
      dataId = matched?.pop();
    }
    if (!canRegOvComponent) return <Com ref={ref} {...props} />;
    ${customContentCode}
    return <Com ref={ref} {...props} />;
  });
});
  `;
}

// 解析单页跳转
const getFunctionCode = (data: MenuDesignDataType) => {
  let tplCode = '';
  if (data?.length > 0) {
    let functionCode = '';
    data.forEach((da) => {
      if (da.menuContentConfig?.urlSetting?.openWay === 'newPage')
        functionCode += `
    if (arg[0] === '${da.id}') window.open('${da.menuContentConfig?.urlSetting?.url}');
      `;
    })
    tplCode += `
  const oldChange = props.onChange;
  props.onChange = (...arg) => {
    ${functionCode}
    typeof oldChange === 'function' && oldChange(...arg);
  }`
  }
  return tplCode;
}

// 解析 重命名、禁用原页签数据
const getReNameOriginTabCode = (originTabMap: AnyObj) => {
  if (!originTabMap || (originTabMap && Object.keys(originTabMap).length === 0)) return '';
  const originTabMapCode = stringify(originTabMap);
  return `
  const originTabMap = ${originTabMapCode};
  _data = _data.map((da) => {
    if (originTabMap[da.id]) return { ...da, ...originTabMap[da.id] };
    return da;
  })`;
}

// 使用范围
const getRangeCheckCode = (rangeCheckDatas: AnyObj[], designProps?: AnyObj) => {
  if (!(rangeCheckDatas && rangeCheckDatas.length > 0)) return { rangeCheckCode: '', checkRangeFuncCode: '', checkAuthFuncCode: '' };
  const rangeCode = stringify(rangeCheckDatas); // data数组解析
  return {
    checkRangeFuncCode: `
// 使用范围校验
const checkRange = (range) => {
  if (!(range && Object.keys(range).length > 0)) return true; // 未设置使用范围
  if (range.all) return true; // 所有人
  const currentUser = window.TEAMS && window.TEAMS.currentUser;
  if (!currentUser) return false;
  if (range.user && range.user.indexOf(currentUser.userId) >=0) return true;
  if (currentUser.department && range.department && range.department.indexOf(currentUser.department.id) >=0) return true;
  return false;
}`,
    checkAuthFuncCode: `
// 事项筛选权限
const isString = (a) => typeof a === 'string';
const isArray = (a) => Object.prototype.toString.call(a) === '[object Array]';

const getRealValue = (rawValue) => {
  if (Array.isArray(rawValue)) {
    const _rawValue = rawValue.map(x => (x?.id || x))
    if (_rawValue.length === 1) return _rawValue[0]
    return _rawValue
  }
  return rawValue?.id || rawValue
}

const checkSingleAuth = (prop, sign = 'eq', value) => {
  const { isEqual, isEmpty } = window.weappUi.utils;
  const b = getRealValue(value)
  // 过滤富文本字段
  const a = /<\\/?\\w*>/.test(prop) ? prop.replace(/<\\/?\\w*>/g, '') : prop
  if (Array.isArray(b)) {
    if (sign === 'eq') return b.indexOf(a) > -1;
    if (sign === 'neq') return b.indexOf(a) === -1;
    if (sign === 'startWith') return b.find(_b => (isString(a) && isString(_b) && a.indexOf(_b) === 0));
    if (sign === 'endWith') return b.find(_b => (isString(a) && isString(_b) && a.lastIndexOf(_b) === a.length - _b.length));
    if (sign === 'contains') return (isString(a) || isArray(a)) && b.find((_b: any) => (a.indexOf(_b) >= 0));
    if (sign === 'uncontains') return (!isString(a) && !isArray(a)) || b.every((_b: any) => (a.indexOf(_b) < 0));
    if (sign === 'in') return (isString(a) || isArray(a)) && b.find((_b: any) => (a.indexOf(_b) >= 0));
    if (sign === 'nin') return (!isString(a) && !isArray(a)) || b.every((_b: any) => (a.indexOf(_b) < 0));
  }
  if (sign === 'eq') return Array.isArray(a) ? a.indexOf(b) > -1 : isEqual(a, b);
  if (sign === 'neq') return Array.isArray(a) ? a.indexOf(b) === -1 : !isEqual(a, b);
  if (sign === 'gt') return a > b;
  if (sign === 'lt') return a < b;
  if (sign === 'gte') return a >= b;
  if (sign === 'lte') return a <= b;
  if (sign === 'startWith') return isString(a) && isString(b) && a.indexOf(b) === 0;
  if (sign === 'endWith') return isString(a) && isString(b) && a.lastIndexOf(b) === a.length - b.length;
  if (sign === 'contains') return (isString(a) || isArray(a)) && a.indexOf(b) >= 0;
  if (sign === 'uncontains') return (!isString(a) && !isArray(a)) || a.indexOf(b) < 0;
  if (sign === 'in') return (isString(a) || isArray(a)) && a.indexOf(b) >= 0;
  if (sign === 'nin') return (!isString(a) && !isArray(a)) || a.indexOf(b) < 0;
  if (sign === 'empty') return isEmpty(a);
  if (sign === 'nempty') return !isEmpty(a);
  if (sign === 'range') return !isEmpty(a) && !isEmpty(b) && Number(a) >= Number(b.min || 0) && Number(a) <= Number(b.max || 0);

  return true;
}
// 设置类型权限
const checkAuth = (auth, designProps) => {
  if (!auth) return true; // 未设置类型权限
  if (!designProps) return false; // 当前Menu不满足设置的类型权限
  const matterFilter = auth.matterFilter;
  if (auth.settingType === 'normal' && matterFilter && matterFilter.length > 0) {
    for(let i=0; i<matterFilter.length; i++) {
      const key = matterFilter[i].condition;
      const compareKey = key+"_compares";
      const checked = checkSingleAuth(designProps[key], matterFilter[i][compareKey], matterFilter[i][key]);
      // 如果有一项校验没通过就不展示
      if (!checked) return false
    }
    return true;
  }
  return auth.settingType === 'normal' || (auth.settingType === 'singleMatter' && designProps.matterId === auth.matterId);
}`,
    rangeCheckCode: `
  const rangeDatas = ${rangeCode};
  rangeDatas.forEach((data) => {
    const passRange = checkRange(data.range);
    const passAuth = checkAuth(data.auth, props.designProps);
    if (passRange && passAuth) {
      const index = newDatas.findIndex((da) => da.id === data.id);
      // 实时刷新的时候data会包含配置属性
      const found = props.data.find((da) => da.id === data.id)
      if (index !== -1) {
        newDatas[index] = { ...newDatas[index], ...data, hidden: false };
      } else if (!found) {
        newDatas.push({ ...data, hidden: false });
      }
    } else {
      newDatas.push({ ...data, hidden: true });
    }
  })`,
  }
}
const customizeCode = (code: string, props: Partial<MenuProps> & { __data?: MenuItemData[] }) => {
  let data = props.__data || props.data;
  if (!props.__data && data && needSync('value', data)) {
    data = (data as any)?.value as unknown as MenuItemData[];
  }
  if (!(data && props.bindKey)) {
    return code;
  }
  // 修复老数据不存在hide的兼容问题

  data = data?.map((da) => ({...da, hide: da.disabled}));

  // 根据props自定义生成初始化的代码
  // const initialCode = getInitialCode(props);
  const contentDatas: MenuDesignDataType = []; // 自定义MenuContent(e-builder)
  const linkDatas: MenuDesignDataType = []; // 复写onChange，处理单页跳转
  const newDatas: MenuDesignDataType = []; // 新增的页签(使用范围为所有人)
  const rangeCheckDatas: AnyObj[] = []; // 新增的页签(使用范围非所有人)
  const originTabMap: AnyObj = {}; // 原页签, id 和 content的映射关系（标准页签仅支持重命名）
  data.forEach((da: any) => {
    if (typeof da.content !== 'string') {
      // 设置多语言后，content可能被替换成多语言展示组件
      da.content = da.lacalContent?.nameAlias
    }
    if (da.menuContentConfig) { // 新增的页签
      /* --------------------------------- 处理props.data -------------------------------------- */
      const { range, auth, type, urlSetting, eBuilderSetting, browserSetting } = da.menuContentConfig;
      if (!(range && range.length > 0) && !auth) newDatas.push({ ...da, hide: da.disabled }); //(权限为所有人)
      else {
        const res: AnyObj = {};
        range.forEach((ra: { _entityType: string; userid: any; id: any; }) => {
          if (ra._entityType === 'all') {
            res.all = true;
          } else if (ra._entityType === 'user') {
            if (!res.user) res.user = [];
            res.user.push(ra.userid);
          } else {
            if (!res.department) res.department = [];
            res.department.push(ra.id);
          }
        })
        rangeCheckDatas.push({ ...da, range: res, auth, hide: da.disabled });
      }
      /* ---------------------------------------------------------------------------------------- */
      if (type === 'url' && urlSetting?.openWay === 'newPage') {
        linkDatas.push(da); // 页面地址
      } else if (
        (type === "eBuilder" && eBuilderSetting?.eBuilder) ||
        // (type === 'moduleData' && moduleDataSetting?.condition) ||
        (type === 'moduleData') || // 暂不接入权限控制
        (type === 'url' && urlSetting?.openWay === 'currentPage') ||
        (type === 'browser' && browserSetting)
      ) contentDatas.push(da); // 模块数据+ebuilder MenuContent 页面
    } else if (da.custom) { // 仅string类型的content支持重命名
      const changes: Record<string, any> = {}
      if (isString(da.content) && (!da.__content || da.content !== da.__content)) {
        changes.content = da.content
      }
      if (da.disabled) {
        changes.disabled = da.disabled;
        changes.hide = da.hide || da.disabled;
      }
      if (da.localContent) {
        changes.localContent = da.localContent
      }
      if (!isEmpty(changes)) {
        // custom 标识保留，避免下次编辑数据还原
        changes.custom = true
        originTabMap[da.id] = changes;
      }
    }
  })

  const reNameOriginTabCode = getReNameOriginTabCode(originTabMap);

  const dataCode = stringify(newDatas); // data数组解析
  const { rangeCheckCode, checkRangeFuncCode, checkAuthFuncCode } = getRangeCheckCode(rangeCheckDatas, props?.designProps);
  const functionCode = getFunctionCode(linkDatas); // 事件解析，主要onChange
  const ebContentCode = getMenuContentCode(contentDatas, props); // menuContent解析

  /* ------------ 排序 ------------ */
  let sortDataCode = `
  let _data = [...props.data || [], ...newDatas];`, sortDataFuncCode = '';
  if (cacheDatas.getSortableIds(props.bindKey)) {
    sortDataFuncCode = cacheDatas.getSortableFuncTemplate(props.bindKey);
    sortDataCode = `
    let _data = ${cacheDatas.sortableFuncName}([...props.data || [], ...newDatas]);`;
  }
  /* ------------------------------ */

  const regMenuProps = `
regOvProps('weappUi', 'Menu', (props) => {
  if (props.bindKey !== menuBindKey) return;
  const newDatas = ${dataCode};
${rangeCheckCode}
${sortDataCode}
${reNameOriginTabCode}
${functionCode}
  const copyData = JSON.parse(JSON.stringify(_data))
  const actualData = _data.filter(item => {
    const visible = !item.hidden
    if (visible) {
      const { localContent } = item;
      if (localContent?.targetId) {
        const { CorsComponent } = window.weappUi || {}
        item.content = (
          <CorsComponent weId="gjcxg6"
            app="@weapp/designer"
            compName="LocalContent"
            id={item.id}
            dataId={item.id}
            value={localContent}
            data={copyData}
            menuBindKey={menuBindKey}
          />
        )
      }
    }
    return visible
  })
  const newProps = {
    ...props,
    data: actualData,
    __data: _data
  };
  return newProps;
});
  `
  return `
import { regOvProps } from '@weapp/utils';
import { DynamicRenderComponent } from '@weapp/ui';
const menuBindKey = "${props.bindKey}";
${checkRangeFuncCode}
${checkAuthFuncCode}
${sortDataFuncCode}
${regMenuProps}
${ebContentCode}
  `;
}

export default customizeCode;