import { AnyObj } from "@weapp/ui";

// 模块数据 满足条件数据处理
export const getModuleCondition = (moduleDataSetting: AnyObj) => {
  const { moduleData, condition } = moduleDataSetting;
  const app = moduleData?.[0]?.app, compName = moduleData?.[0]?.compName;
  let conditions: AnyObj = {};
  condition?.forEach((data: any) => {
    const key = data.condition;
    if (key) {
      if (!conditions[key]) conditions[key] = [];
      conditions[key].push({
        value: data[key],
        compares: data[`${key}_compares`],
        variables: data[`${key}_variables`],
      })
    }
  })
  return { app, compName, conditions };
}

export const findInstanceByFiber = (fiber: any) => {
  let currFiber: any | null = fiber;

  while (currFiber) {
    if (currFiber.stateNode?.forceUpdate) {
      return currFiber.stateNode;
    }
    currFiber = currFiber.child;
  }
  return null;
}

