import { Dialog, FormStore, FormInitAllDatas, Form, Button, AnyObj, FormStoreType, CorsComponent } from '@weapp/ui';
import { classUseMemo, getLabel } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { commonSettingClsPrefix, dialogConfig } from '../../../constants';
import CommonSetting from '../../common-setting';
import { SettingProps, CustomRenderItemProps, CustomSettingRef } from '../type';
import { browserSettingFields } from '../utils';
import AuthSetting from './auth/AuthSetting';
import EBuilderSetting from './custom/EBuilderSetting';
import ModuleDataSetting from './custom/ModuleDataSetting';
import UrlSetting from './custom/UrlSetting';
import ContentSetting from './custom/ContentSetting';

const getHideConfig = (val: string) => ['eBuilderSetting', 'moduleDataSetting', 'urlSetting', 'browserSetting'].filter((v) => v !== val);
const cascadeRulesOuter: AnyObj = {
  url: { show: ['urlSetting'], hide: getHideConfig('urlSetting') },
  eBuilder: { show: ['eBuilderSetting'], hide: getHideConfig('eBuilderSetting') },
  moduleData: { show: ['moduleDataSetting'], hide: getHideConfig('moduleDataSetting') },
  browser: { show: ['browserSetting'], hide: getHideConfig('browserSetting') },
}

const browserSettingStore = { labelSpan: 4, fields: browserSettingFields };
const switchComponent = (id: string, allProps: any, props: any, ref: any) => {
  switch (id) {
    case 'content': return (<ContentSetting weId={`${props.weId || ''}_udb0gt`} {...allProps} />)
    case 'urlSetting': return (<UrlSetting weId={`${props.weId || ''}_p5l9wp`}  {...allProps} ref={ref} />)
    case 'eBuilderSetting': return (<EBuilderSetting weId={`${props.weId || ''}_slfo5r`} {...allProps} ref={ref} />)
    case 'moduleDataSetting': return (<ModuleDataSetting weId={`${props.weId || ''}_e55r26`} {...allProps} />)
    case 'auth': return (<AuthSetting weId={`${props.weId || ''}_eb2soc`} {...allProps} />)
    case 'browserSetting': return (<CommonSetting
      weId={`${props.weId || ''}_eb2soc`}
      {...allProps}
      compName="BrowserPanel"
      prefixCls={commonSettingClsPrefix}
      store={browserSettingStore}
    />)
  }
  return null;
}

interface SettingState {
  otherDatas: AnyObj;
  // type: string,
}
class Setting extends PureComponent<SettingProps, SettingState> {
  store: FormStoreType;
  customSettingRef = React.createRef<CustomSettingRef>();
  constructor(props: SettingProps) {
    super(props);
    this.store = new FormStore();
    this.state = {
      otherDatas: {},
      // type: 'eBuilder'
    }
  }
  componentDidMount() {
    this.changeVisible();
  }
  getSnapshotBeforeUpdate(prevProps: Readonly<SettingProps>, prevState: Readonly<SettingState>) {
    const { visible: _preVisible } = prevProps;
    const { visible } = this.props;
    if (visible !== _preVisible) this.changeVisible();
  }
  getFormConfig = () => {
    const { settingType, prefixCls, labelSpan, data } = this.props;
    const hide = settingType === 'rename';
    const urlSettingConfig = {
      layout: [
        [{ id: 'urlSetting', label: '', items: ['urlSetting'], wrapClassName: `${prefixCls}-customContainer`, labelSpan: 0, hide: true }], // url对应的设置项
      ],
      items: { urlSetting: { itemType: 'CUSTOM' } },
      datas: { urlSetting: {} },
    }
    const eBuilderSetting = {
      layout: [
        [{ id: 'eBuilderSetting', label: '', items: ['eBuilderSetting'], wrapClassName: `${prefixCls}-customContainer`, labelSpan: 0, hide }],
      ],
      items: { eBuilderSetting: { itemType: 'CUSTOM' } },
      datas: { eBuilderSetting: {} },
    }
    const moduleDataSetting = {
      layout: [
        [{ id: 'moduleDataSetting', label: '', items: ['moduleDataSetting'], wrapClassName: `${prefixCls}-customContainer`, labelSpan: 0, hide: true }],
      ],
      items: { moduleDataSetting: { itemType: 'CUSTOM' } },
      datas: { moduleDataSetting: {} },
    }
    const browserSetting = {
      layout: [
        [{ id: 'browserSetting', label: '', items: ['browserSetting'], wrapClassName: `${prefixCls}-customContainer`, labelSpan: 0, hide: true }]
      ],
      items: { browserSetting: { itemType: 'CUSTOM' } },
      datas: { browserSetting: {} },
    }
    const normalConfig = {
      layout: [
        [{ id: 'content', label: getLabel('207005','显示名称'), items: ['content'], labelSpan, hide: false }],
        [{ d: 'type', label: getLabel('213267','关联类型'), items: ['type'], labelSpan, hide, cascadeRulesOuter: { type: cascadeRulesOuter } }],
        [{ id: 'auth', label: '', labelSpan: 0, items: ['auth'], hide, wrapClassName: `${prefixCls}-customContainer` }],
        [{ id: 'range', label: getLabel('213268','使用范围'), items: ['range'], labelSpan, hide }],
      ],
      items: {
        content: {
          itemType: 'CUSTOM',
          required: true,
          placeholder: getLabel('213269','请输入名称'),
        },
        type: {
          itemType: 'RADIO',
          data: [
            // { id: 'moduleData', content: '模块数据' },
            { id: 'eBuilder', content: 'e-builder' },
            { id: 'url', content: getLabel('213270','网页地址') },
            { id: 'browser', content: getLabel('152395','关联浏览业务数据') },
          ],
        },
        range: {
          itemType: 'TYPESBROWSER',
          typesBrowserBean: {
            multiple: true,
            options: [
              { id: "user", content: getLabel('213271','人员') },
              { id: "dept", content: getLabel('213272','部门') },
              // { id: "group", content: "群组" },
              { id: "all", content: getLabel('213273','所有人') },
            ],
            browsers: {
              user: { module: 'hrm', type: 'resource' },
              dept: { module: 'hrm', type: 'department' },
              group: { module: 'hrm', type: 'channel' },
            },
          },
          required: true
        },
        auth: { itemType: 'CUSTOM' },
      },
      datas: {
        content: data.localContent || data.content,
        type: 'eBuilder',
        range: [
          {
            _entityContent: getLabel('213273','所有人'),
            _entityType: "all",
            _secretLevel: "0-100",
            content: getLabel('213273','所有人'),
            icon: "Icon-all02",
            id: "0",
          }
        ],
        auth: {
          settingType: 'normal',
        }
      },
    }
    return {
      groups: [],
      layout: [
        ...normalConfig.layout,
        ...urlSettingConfig.layout,
        ...eBuilderSetting.layout,
        ...moduleDataSetting.layout,
        ...browserSetting.layout,
        // [{ id: 'setting', label: '使用类型', items: ['setting'], labelSpan, hide: false }],
      ],
      items: {
        ...normalConfig.items,
        ...urlSettingConfig.items,
        ...eBuilderSetting.items,
        ...moduleDataSetting.items,
        ...browserSetting.items,
      },
      data: {
        ...normalConfig.datas,
        ...urlSettingConfig.datas,
        ...eBuilderSetting.datas,
        ...moduleDataSetting.datas,
        ...browserSetting.datas,
      },
    } as FormInitAllDatas;
  }
  changeVisible = () => {
    const { visible, data } = this.props;
    const store = this.store;
    if (visible) {
      store.initForm(this.getFormConfig());
      const result = {
        ...data,
        ...data?.menuContentConfig,
        content: data.localContent || data.content,
      };
      store.updateDatas(result);
      // 字段联动
      const { type } = result;
      const cas = cascadeRulesOuter[type] || {};
      cas?.show?.map((showId: string) => store.setHide(showId, false));
      cas?.hide?.map((showId: string) => store.setHide(showId, true));
    } else {
      // 清空otherDatas避免对下次造成影响
      this.setState({ otherDatas: {} })
      store.resetForm();
      store.clear();
    }
  }
  doSave = () => {
    const { onSave, settingType, data } = this.props;
    const result = { ...this.store.getFormDatas(), ...this.state.otherDatas };
    const normalPass = this.store.validate().then((errors: any) => !(errors && errors.errors && Object.keys(errors.errors).length > 0));
    const customPass = this.customSettingRef?.current?.validate?.() as Promise<boolean>;
    const validatePass: Array<Promise<boolean>> = [normalPass];
    if ((result.type === 'url' || result.type === 'eBuilder') && customPass) validatePass.push(customPass);
    Promise.all(validatePass).then((res) => {
      let ping = true;
      res.forEach((r) => ping = ping && (r || false));
      if (ping) {
        const saveParams: any = settingType === 'rename' ? { custom: true } : { menuContentConfig: result, custom: true };
        if (!data?.__content) {
          // 没有被复写过的情况存下最初的content；
          saveParams.__content = data.content
        }
        if (typeof result.content === 'string') {
          saveParams.content = result.content
        } else {
          const { nameAlias } = result.content || {}
          saveParams.content = nameAlias;
          saveParams.localContent = result.content;
        }
        onSave?.(saveParams, data);
      }
    })
  }

  onCustomChange = (formValue: any) => {
    this.setState({ otherDatas: { ...this.state.otherDatas, ...formValue } });
  }

  customRenderFormSwitch = (id: string, props: CustomRenderItemProps) => {
    const allProps = {
      ...props.props,
      formChange: props.props?.onChange,
      onChange: this.onCustomChange,
      id,
    }
    const ref = id === 'urlSetting' || id === 'eBuilderSetting' ? this.customSettingRef : null;
    return switchComponent(id, allProps, props, ref);
  };

  // handleChange = (value: any) => {
  //   if (!value.type) {
  //     this.store.updateDatas({ type: this.state.type })
  //   } else {
  //     this.setState({ type: value.type })
  //   }
  // }

  render() {
    const { prefixCls, visible, onClose, dialogProps } = this.props;
    const buttons = classUseMemo('buttons', this, () => ([
      <Button weId={`${this.props.weId || ''}_n4ju0c@save`} onClick={this.doSave} type="primary" key="save">{getLabel("207006", "保存")}</Button>
    ]), []);
    return (
      <Dialog
        weId={`${this.props.weId || ''}_j38wig`}
        {...dialogConfig}
        className={`${prefixCls}-setting inspect-disabled`}
        title={getLabel('213274','页签设置')}
        onClose={onClose}
        visible={visible}
        buttons={buttons}
        draggable
        width={600}
        {...dialogProps}
      >
        <Form weId={`${this.props.weId || ''}_8x7bop`} className={`${prefixCls}-setting-form`} store={this.store} customRenderFormSwitch={this.customRenderFormSwitch} />
      </Dialog>
    )

  }
}
export default Setting;