import React, { memo, useMemo, useCallback, useContext, useEffect, useState, Attributes, ReactNode, useImperativeHandle } from "react";
import { AnyObj, FormItemProps, FormItem, FormDatas, CorsComponent, Icon, Input, utils } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import { CustomSettingProps, MenuDesignContextProps, EbuilderPageValue } from "../../type";
import { MenuDesignContext } from "../../utils";
import { doRequest } from '../../apis';
import FilterTable from './FilterTable'
import { useRef } from "react";

const { getRandom, doValidator } = utils

const getDatasetId = (dataset: any) => `${dataset.type}_${dataset.groupId}_${dataset.id}`

const deduplication = (arr: Array<{ id: string }>) => {
  const _map = arr.reduce((obj, item) => {
    obj[item.id] = item
    return obj
  }, {} as Record<string, any>)
  return Object.values(_map)
}

const BrowserInput = (props: Attributes & { value: EbuilderPageValue[], onCancel: (e: any) => void }) => {
  const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
  const { prefixCls } = menuDesignContext;
  const value = props?.value?.[0]?.content

  const onCancel = useCallback((e) => {
    props.onCancel(e);
    e.stopPropagation()
  }, [props.onCancel])

  return (
    <div className={`${prefixCls}-browser-input`}>
      <Input
        weId={`${props.weId || ''}_jhayka`}
        suffix={<Icon weId={`${props.weId || ''}_zqqjwa`} name="Icon-search" />}
        placeholder={getLabel('213403','点击选择页面')}
        value={value}
      />
      <span className={`${prefixCls}-browser-input-del-icon`} onClick={onCancel}>
        {value && <Icon weId={`${props.weId || ''}_4zw2wc`} name="Icon-cancel" />}
      </span>
    </div>
  )
}

const EBuilderSetting = memo(React.forwardRef<{}, CustomSettingProps>(
  (props, ref) => {
    const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
    const { prefixCls, labelSpan } = menuDesignContext;
    const [value, setValue] = useState<AnyObj>({});
    const [datasets, setDatasets] = useState<any[]>([]);
    const [baseFields, setBaseFields] = useState<any[]>([]);
    const [changes, setChanges] = useState<any[]>([]);
    const [errors, setErrors] = useState<any>(null)
    const pageIdRef = useRef('')


    const { value: _valueProps, id, onChange } = props;

    useImperativeHandle(ref, () => {
      return {
        validate: () => new Promise((resolve) => {
          doValidator({
            datas: { eBuilder: value?.eBuilder },
            rules: { eBuilder: 'required' },
          }).then((errors: any) => {
            const flag = !(errors && errors.errors && Object.keys(errors.errors).length > 0);
            if (!flag) {
              setErrors(errors.errors);
            }
            resolve(flag);
          })
        })
      }
    })

    useEffect(() => {
      const result = !_valueProps?.eBuilder ? { eBuilder: [] } : _valueProps;
      setValue(result);
      if (result.conditions) {
        const initFilterData = Object.keys(result.conditions).map((datasetId) => ({
          id: getRandom(),
          dataset: datasetId
        }))
        setChanges(initFilterData)
      }
    }, [_valueProps]);

    const doChange = useCallback((newVal?: FormDatas) => {
      setValue({ ...value, ...newVal });
      onChange?.({ [`${id}`]: { ...value, ...newVal } });
    }, [id, onChange, value]);

    const onCancel = useCallback(() => {
      doChange({ eBuilder: [] })
    }, [doChange]);

    const com = useMemo(() => ['eBuilder'], []);
    const items = useMemo(() => ({
      eBuilder: {
        itemType: 'BROWSER',
        required: true,
        error: errors?.eBuilder,
        browserBean: {
          type: 'center',
          module: 'ebuilder/common',
          dataParams: { terminalType: 'PC', onlyEBPage: 1 },
          browserTreeProps: { async: false },
          browserDialogProps: { 
            className: 'inspect-disabled', 
            title: getLabel('213412','标题'),
          }
        },
        className: 'inspect-disabled',
        value: value?.eBuilder,
        quickSearchPlaceHolder: getLabel('213404','根据项目查询页面'),
        children: (
          <BrowserInput
            weId={`${props.weId || ''}_r8m2v0`}
            value={value?.eBuilder}
            onCancel={onCancel}
          />
        )
      }
    } as FormItemProps), [value?.eBuilder, errors?.eBuilder]);

    const onConditionChange = useCallback((datasetId, condition) => {
      const _conditions = {
        ...(value.conditions ?? {}),
        [datasetId]: condition
      }
      doChange({ conditions: _conditions })
    }, [doChange, value.conditions])

    const getVarFields = useCallback(() => {
      const varFields = baseFields.map(field => {
        const compType = field.id === 'dataId' ? 'Mainline' : field.fieldType
        return {
          ...field,
          compType
        }
      })
      return Promise.resolve(varFields)
    }, [baseFields])

    const customRenderFilter = useCallback((instance: ReactNode, content: any, rowData: any, data: any, comProps: any, pos: any) => {
      const dataset = datasets.find(_dataset => getDatasetId(_dataset) === rowData.dataset)
      return (
        <CorsComponent
          weId={`${props.weId || ''}_vfxoui`}
          app="@weapp/components"
          compName="ConditionSet"
          dataSet={dataset}
          value={rowData.condition}
          // eslint-disable-next-line react/jsx-no-bind
          onChange={onConditionChange.bind(null, rowData.dataset)}
          placeholder={getLabel('213405','设置过滤条件')}
          title={getLabel('213406','查询条件')}
          varFields={getVarFields}
          showFilterIsNull
        />
      )
    }, [datasets, getVarFields, onConditionChange])

    const pageId = value?.eBuilder?.[0]?.id

    useEffect(() => {
      if (pageIdRef.current) {
        setChanges([])
      }
      pageIdRef.current = pageId
      setDatasets([])
      doRequest('getPageDatasets', undefined, { params: { pageId } }).then((res) => {
        if (res?.data && Array.isArray(res.data)) {
          const _datasets = res?.data && Array.isArray(res.data) ? deduplication(res.data) : []
          setDatasets(_datasets)
        }
      })
    }, [pageId])

    useEffect(() => {
      doRequest('getBaseFields', 'ebuilder/common').then((res) => {
        if (res?.data && Array.isArray(res.data)) {
          setBaseFields(res.data)
        }
      })
    }, [])

    const handleFilterChange = useCallback((changes) => {
      setChanges(changes)
      // 删除过滤项目之后同步数据
      const _conditions = changes.reduce((_data: any, item: any) => {
        if (value.conditions?.[item.dataset]) {
          _data[item.dataset] = value.conditions?.[item.dataset]
        }
        return _data
      }, {})
      doChange({ conditions: _conditions })
    }, [value.conditions, doChange])

    const config = useMemo(() => {
      const _datasets = datasets.map(dataset => ({
        ...dataset,
        id: getDatasetId(dataset),
        content: dataset.text,
        disabled: changes.find((_item: any) => _item.dataset === getDatasetId(dataset))
      }))
      const data = {
        dataset: { title: getLabel('213407','页面数据源'), itemType: 'SELECT', data: _datasets, width: '45%' },
        condition: { title: getLabel('213408','数据源过滤条件'), itemType: 'CUSTOM', customRender: customRenderFilter, width: '45%' }
      }
      return { data }
    }, [datasets, customRenderFilter, changes])

    const filterData = useMemo(() => {
      return changes.map((_item: any) => ({
        ..._item,
        condition: value.conditions?.[_item.dataset]
      }))
    }, [changes, value.conditions])
    return (
      <div className={`${prefixCls}-eBuilderSetting`}>
        <FormItem weId={`${props.weId || ''}_y4b00o`} label={getLabel('213409','e-builder页面')} labelSpan={labelSpan} item={items} com={com} onChange={doChange} />
        <FormItem weId={`${props.weId || ''}_3t3fbn`} label={getLabel('213410','页面筛选条件')} labelSpan={24}>
          <FilterTable
            weId={`${props.weId || ''}_cyt2sj`}
            config={config}
            data={filterData}
            onChange={handleFilterChange}
          />
        </FormItem>
      </div>
    )
  }
))
export default EBuilderSetting;