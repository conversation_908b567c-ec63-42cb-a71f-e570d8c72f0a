import { AnyObj, FormDatas, FormItem, FormItemProps, utils } from "@weapp/ui";
import { getLabel } from "@weapp/utils";
import React, { memo, useMemo, useCallback, useContext, useEffect, useState, useImperativeHandle } from "react";
import { CustomSettingProps, CustomSettingRef, MenuDesignContextProps } from "../../type";
import { MenuDesignContext } from "../../utils";

const { doValidator } = utils;

const UrlSetting = memo(React.forwardRef<CustomSettingRef, CustomSettingProps>(
  (props, ref) => {
    const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
    const { prefixCls, labelSpan } = menuDesignContext;
    const [value, setValue] = useState<AnyObj>({});
    const [errors, setErrors] = useState<AnyObj>({});
    const { value: _valueProps, id, onChange } = props;

    useEffect(() => {
      const result = !_valueProps?.openWay ? { openWay: 'currentPage' } : _valueProps;
      setValue(result);
    }, [_valueProps]);

    useImperativeHandle(ref, () => {
      return {
        validate: () => new Promise((resolve) => {
          doValidator({
            datas: { url: value?.url },
            rules: { url: ['required', 'regex:/^(http|https|ftp):\/\/.*/'] },
          }).then((errors: any) => {
            const flag = !(errors && errors.errors && Object.keys(errors.errors).length > 0);
            if (!flag) {
              setErrors(errors.errors);
            }
            resolve(flag);
          })
        })
      }
    })

    const doChange = useCallback((newVal?: FormDatas) => {
      if (newVal && 'openWay' in newVal && !newVal?.openWay) return;
      setValue({ ...value, ...newVal });
      onChange?.({ [`${id}`]: { ...value, ...newVal } });
    }, [id, onChange, value]);

    const openWayItems = useMemo(() => ({
      openWay: {
        itemType: 'RADIO', value: value?.openWay, data: [
          { id: 'currentPage', content: getLabel('219082','当前页') },
          { id: 'newPage', content: getLabel('219083','新窗口') },
        ]
      }
    } as FormItemProps), [value?.openWay]);
    const openWayCom = useMemo(() => ['openWay'], []);

    const urlItems = useMemo(() => ({ url: { itemType: 'INPUT', required: true, value: value?.url, error: errors?.url, errorType: 'popover' } } as FormItemProps), [value?.url, errors?.url]);
    const urlCom = useMemo(() => ['url'], []);

    return <div className={`${prefixCls}-urlSetting`}>
      <FormItem weId={`${props.weId || ''}_jki59m`} label={getLabel('219084','打开方式')} labelSpan={labelSpan} item={openWayItems} com={openWayCom} onChange={doChange} />
      <FormItem weId={`${props.weId || ''}_z5c3ft`} label={getLabel('213270','网页地址')} labelSpan={labelSpan} item={urlItems} com={urlCom} onChange={doChange} />
    </div>
  }
))
export default UrlSetting;