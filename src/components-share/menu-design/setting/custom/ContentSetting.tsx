import React, { useCallback, useState } from "react";
import { CorsComponent } from "@weapp/ui";

const ContentSetting = ({ weId, id, value: initValue, formChange, ...res }: any) => {
    const [value, setValue] = useState(initValue)

    const handleChange = useCallback((value: any) => {
      setValue(value)
      formChange?.(value)
    }, [formChange])

    return (
      <CorsComponent weId={`${weId || ''}_fmo0v9`}
        app="@weapp/ebdcoms"
        compName="LocaleEx"
        value={value}
        onChange={handleChange}
      />
    )
}
  
export default ContentSetting;