import { AnyObj, FormItem } from "@weapp/ui";
import React, { memo, useContext, useEffect, useState, useCallback } from "react";
import Condition, { ConditionValue } from "../../../condition";
import { CustomSettingProps, MenuDesignContextProps } from "../../type";
import { MenuDesignContext } from "../../utils";

const ModuleDataSetting = memo(React.forwardRef<{}, CustomSettingProps>(
  (props) => {
    const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
    const { prefixCls, labelSpan, moduleDatas, getModulesCondition } = menuDesignContext;
    const [value, setValue] = useState<AnyObj>({});
    const { value: _valueProps, id, onChange } = props;

    useEffect(() => {
      setValue(_valueProps);
    }, [_valueProps]);

    const onModuleChange = useCallback((moduleData: ConditionValue) => {
      setValue({ ...value, moduleData, condition: [] });
      onChange?.({ [`${id}`]: { ...value, moduleData, condition: [] } });
      const route = moduleData?.[0]?.condition; // 当前选中的模块选项
      const condition = moduleData?.[0]?.[route]; // 当前选中的模块的联动字段
      const modulesCondition = moduleDatas?.modules || []; // 所有模块选项
      const modulecondition = modulesCondition.find((mod: { id: string; }) => mod.id === route); // 当前选项的配置
      if (modulecondition?.formProps) { // 有联动的字段
        condition && condition.length > 0 && getModulesCondition?.(modulecondition?.route, {
          params: {
            objid: condition.map((con: { id: any; }) => con.id)?.join() || '',
          }
        });
      } else if (modulecondition?.route) {
        getModulesCondition?.(modulecondition?.route);
      }
    }, [value, moduleDatas?.modules, getModulesCondition, id, onChange]);

    const onItemEditBefore = useCallback((newData, editKey, editValue, rowData) => {
      if (editKey === 'condition') {
        const tempData = moduleDatas?.modules?.find((da: { id: string; }) => da.id === editValue);
        return [{ ...newData[0], compName: tempData?.compName, app: tempData?.compPackage }];
      }
      return newData;
    }, [moduleDatas?.modules]);

    const onConditionChange = useCallback((condition: ConditionValue) => {
      setValue({ ...value, condition });
      onChange?.({ [`${id}`]: { ...value, condition } });
    }, [value, id, onChange]);

    return <div className={`${prefixCls}-moduleDataSetting`}>
      <FormItem weId={`${props.weId || ''}_8p1ncj`} label={"模块选择"} labelSpan={labelSpan}>
        <Condition weId={`${props.weId || ''}_ow64na`} datas={moduleDatas?.modules} onChange={onModuleChange} value={value?.moduleData} onItemEditBefore={onItemEditBefore} noLine disabledVariables />
      </FormItem>
      {/* <FormItem weId={`${props.weId || ''}_2m15dw`} label={"满足条件"} labelSpan={labelSpan}>
        <Condition weId={`${props.weId || ''}_kdw2l7`}  datas={moduleDatas?.condition} onChange={onConditionChange} value={value?.condition} multiple noLine />
      </FormItem> */}
    </div>
  }
))
export default ModuleDataSetting;