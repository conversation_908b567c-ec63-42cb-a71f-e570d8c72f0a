import React, { memo, useCallback, useContext, useEffect, useState, useMemo } from "react";
import { EditableTable, Icon, utils } from "@weapp/ui";
import { MenuDesignContextProps } from "../../type";
import { MenuDesignContext } from "../../utils";
import { getLabel } from "@weapp/utils";

const { getRandom } = utils

type FilterTableProps = any

const customRenderActions = (handleDelete: any) => (...args: any) => {
  const rowId = args?.[2]?.id
  const handleClick = () => handleDelete(rowId)
  // eslint-disable-next-line react/jsx-no-bind
  return <Icon weId="rlgshm" name="Icon-delete02" onClick={handleClick} />
}

const formatConfig = (config: any, handleDelete: any) => {
  const columns = []
  const comProps: any = {}
  Object.keys(config).forEach(field => {
    const { title, ...props } = config[field]
    if (title) {
      columns.push({
        title,
        dataIndex: field,
        comKey: field,
      })
    }
    if (props.itemType) {
      comProps[field] = {
        ...props,
        className: `${props.className || ''} inspect-disabled`,
        dropdownClassName: `${props.dropdownClassName || ''} inspect-disabled`,
      }
    }
  })
  columns.push({ title: '', dataIndex: 'actions', comKey: 'actions', width: '10%' })
  comProps.actions = { itemType: 'CUSTOM', customRender: customRenderActions(handleDelete) }

  return { columns, comProps }
}

const FilterTable = memo(React.forwardRef<{}, FilterTableProps>(
  (props) => {
    const { withCells, data: configData } = props?.config ?? {}
    const [data, setData] = useState([{ id: getRandom() }])
    const [cells, setCells] = useState({})
    const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
    const { prefixCls } = menuDesignContext;

    useEffect(() => {
      if (!withCells) return;
      const _cells: any = {}
      data?.forEach?.((row: any) => {
        let valueField = row.condition || 'value'
        const compares = row[`${row.condition}_compares`]
        if (['empty', 'nempty'].includes(compares)) {
          valueField = 'empty'
        }
        if (compares === 'range') {
          valueField = 'range'
        }
        _cells[row.id] = {
          value: [valueField],
          compare: [row.condition ? `${row.condition}_compares` : 'compare']
        }
      })
      setCells(_cells)
    }, [data, setCells, withCells])

    useEffect(() => {
      setData(props.data || [])
    }, [props.data])

    const handleChange = useCallback((_data) => {
      setData(_data)
      props?.onChange?.(_data)
    }, [props?.onChange])

    const handleDelete = useCallback((rowId) => {
      const _data = data.filter((row: any) => row.id !== rowId)
      setData(_data)
      props?.onChange?.(_data)
    }, [data, props?.onChange])

    const { columns, comProps } = useMemo(() => formatConfig(configData, handleDelete), [configData, handleDelete])

    const topTools = [
      {
        type: 'batchDelete',
        icon: 'Icon-Batch-delete',
        title: getLabel('213309','批量删除'),
      },
      {
        type: 'add',
        icon: 'Icon-add-to03',
        title: getLabel('213310','新增'),
      },
    ];

    return (
      <div className={`${prefixCls}-filter-table`}>
        <EditableTable
          weId={`${props.weId || ''}_t8e00j`}
          columns={columns}
          comProps={comProps}
          data={data}
          onChange={handleChange}
          topTools={topTools}
          cells={cells}
        />
      </div>
    )
  }
))
export default FilterTable;