import React, { memo, useCallback, useContext, useEffect, useMemo, useState, ReactNode } from "react";
import { AnyObj, Dialog, Button, Alert, Select } from "@weapp/ui";
import { MenuDesignContext } from "../../utils";
import { ConditonProps, MenuDesignContextProps } from "../../type";
import { dialogConfig } from "../../../../constants";
import FilterTable from '../custom/FilterTable'
import { getLabel } from "@weapp/utils";

const Conditon = memo(React.forwardRef<{}, ConditonProps>(
  (props) => {
    const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
    const { prefixCls, matterFilter = [] } = menuDesignContext;
    const [value, setValue] = useState<AnyObj[]>([]);
    const { value: _valueProps, onSave: _onSave, onClose: _onClose, visible } = props;

    useEffect(() => {
      if (visible) {
        setValue(_valueProps || []);
      }
    }, [_valueProps, visible, setValue]);

    const onClose = useCallback(() => {
      _onClose?.();
    }, [_onClose]);

    const onSave = useCallback(() => {
      setValue(value);
      const formatValue: AnyObj[] = value
        ?.filter?.((d) => {
          const compares = d[`${d.condition}_compares`]
          const isRangeValue = compares === 'range' && d.range
          return d.condition && (d[d.condition] || ['empty', 'nempty'].includes(compares) || isRangeValue)
        })
        .map((d) => {
          const compares = d[`${d.condition}_compares`]
          const isRange = compares === 'range'
          const _d: any = {
            id: d.id,
            condition: d.condition,
            [d.condition]: isRange ? d.range : d[d.condition]
          }
          if (compares) {
            _d[`${d.condition}_compares`] = compares
          }
          return _d
        })
      _onSave?.(formatValue);
      onClose();
    }, [_onSave, onClose, value]);

    const onConditionChange = useCallback((value: AnyObj[]) => {
      setValue(value);
    }, []);

    const buttons = useMemo(() => ([
      <Button weId={`${props.weId || ''}_nbkcnv@save`} onClick={onSave} type="primary" key="save">{getLabel('207006','保存')}</Button>
    ]), [props.weId, onSave]);

    const config = useMemo(() => {
      const fields: any = {}
      const options = matterFilter?.map?.((item: any) => {
        const formProps = item.formProps || {}
        if (formProps.itemType === 'BROWSER') {
          formProps.browserBean = {
            ...(formProps.browserBean || {}),
            browserDialogProps: {
              ...(formProps?.browserBean?.browserDialogProps ?? {}),
              className: 'inspect-disabled'
            }
          }
        }
        fields[item.id] = { ...formProps }
        fields[`${item.id}_compares`] = item?.compares?.length ? {
          itemType: 'SELECT',
          data: item.compares.map((d: any) => ({
            id: d.id,
            content: d.text
          })),
        } : {
          itemType: 'CUSTOM',
          customRender: () => <>{getLabel('213304','等于')}</>,
        }
        return {
          id: item.id,
          content: item.text,
        }
      })

      const data = {
        condition: { title: getLabel('213305','字段名称'), itemType: 'SELECT', data: options, width: '35%' },
        compare: { title: getLabel('213306','条件类型'), itemType: 'SELECT', data: [], width: '20%' },
        value: { title: getLabel('213307','字段值'), itemType: 'INPUT', width: '35%' },
        empty: { itemType: 'INPUT', disabled: true },
        range: { itemType: 'SCOPE', type: 'INPUTNUMBER' },
        ...fields,
      }
      return { data, withCells: true }
    }, [matterFilter])
    return (
      <Dialog weId={`${props.weId || ''}_i8jsgh`}
        {...dialogConfig}
        className={`${prefixCls}-condition inspect-disabled`}
        title={getLabel('213302','事项筛选')}
        onClose={onClose}
        visible={visible}
        buttons={buttons}
        width={600}
      >
        <Alert weId={`${props.weId || ''}_ugll84`}
          message={getLabel('213308','满足以下条件的事项，才会显示该页签')}
          type="info"
        />
        <div className={`${prefixCls}-condition-content`}>
          <FilterTable weId={`${props.weId || ''}_fn1p0i`}
            config={config}
            data={value}
            onChange={onConditionChange}
          />
        </div>
      </Dialog>
    )
  }
))
export default Conditon;