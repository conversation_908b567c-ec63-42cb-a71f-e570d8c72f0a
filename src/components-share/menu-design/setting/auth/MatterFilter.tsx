import React, { memo, useCallback, useContext, useState } from "react";
import { Icon, AnyObj } from "@weapp/ui";
import { MenuDesignContext } from "../../utils";
import { MatterFilterProps, MenuDesignContextProps } from "../../type";
import Condition from "./Condition";

const MatterFilter = memo(React.forwardRef<{}, MatterFilterProps>(
  (props) => {
    const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
    const { prefixCls } = menuDesignContext;
    const { value: _valueProps, onChange } = props;
    const [visible, setVisible] = useState(false);

    const onClose = useCallback(() => {
      setVisible(false);
    }, []);

    const onSave = useCallback((value: AnyObj[]) => {
      onChange?.(value);
      onClose();
    }, [onChange, onClose]);

    const onClick = useCallback(() => {
      setVisible(true);
    }, []);

    return <div className={`${prefixCls}-matterFilter`}>
      <Icon weId={`${props.weId || ''}_2q0mqs`} name='Icon-set-up-o' size="sm" onClick={onClick} />
      {
        _valueProps?.length > 0 && <Icon weId={`${props.weId || ''}_2kb7kp`} name="Icon-correct01" />
      }
      <Condition weId={`${props.weId || ''}_39ee88`} onSave={onSave} visible={visible} value={_valueProps} onClose={onClose} />
    </div>
  }
))
export default MatterFilter;