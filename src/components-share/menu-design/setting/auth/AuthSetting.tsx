import React, { memo, useMemo, useCallback, useContext, useEffect, useState } from "react";
import { AnyObj, FormItemProps, FormItem, FormDatas } from "@weapp/ui";
import { CustomSettingProps, MenuDesignContextProps } from "../../type";
import { MenuDesignContext } from "../../utils";
import MatterFilter from "./MatterFilter";
import { getLabel } from "@weapp/utils";

const AuthSetting = memo(React.forwardRef<{}, CustomSettingProps>(
  (props) => {
    const menuDesignContext = useContext<MenuDesignContextProps>(MenuDesignContext);
    const { prefixCls, labelSpan, designProps } = menuDesignContext;
    const [value, setValue] = useState<AnyObj>({});
    const { value: _valueProps, id, onChange } = props;

    useEffect(() => {
      setValue(_valueProps);
    }, [_valueProps]);

    const settingTypeCom = useMemo(() => ['settingType'], []);
    const settingTypeItems = useMemo(() => ({
      settingType: {
        itemType: 'RADIO', value: value?.settingType, data: designProps?.matterId ? [
          { id: 'normal', content: getLabel('213298','通用事项') },
          { id: 'singleMatter', content: getLabel('213300','单个事项') },
        ] : [
          { id: 'normal', content: getLabel('213298','通用事项') },
        ],
      }
    } as FormItemProps), [value?.settingType, designProps?.matterId]);

    const onChangeSettingType = useCallback((newVal?: FormDatas) => {
      if (newVal && 'settingType' in newVal && !newVal?.settingType) return;
      const _value = { ...value, ...newVal }
      if (_value.settingType === 'singleMatter') {
        _value.matterId = designProps?.matterId
      }
      setValue(_value);
      onChange?.({ [`${id}`]: _value });
    }, [onChange, id, value, designProps?.matterId]);

    const doChange = useCallback((newVal: any) => {
      setValue({ ...value, matterFilter: newVal });
      onChange?.({ [`${id}`]: { ...value, matterFilter: newVal } });
    }, [id, onChange, value]);

    return <div className={`${prefixCls}-authSetting`}>
      <FormItem weId={`${props.weId || ''}_9dnydr`} label={getLabel('213301','设置类型')} labelSpan={labelSpan} item={settingTypeItems} com={settingTypeCom} onChange={onChangeSettingType} />
      {
        value?.settingType === 'normal' && (
          <FormItem weId={`${props.weId || ''}_9dnydr`} label={getLabel('213302','事项筛选')} labelSpan={labelSpan} helpTip={getLabel('213303','设置显示页签的满足条件')} helpTipPostion="afterLabel">
            <MatterFilter weId={`${props.weId || ''}_0iv055`} value={value?.matterFilter} onChange={doChange} />
          </FormItem>
        )
      }
    </div>
  }
))
export default AuthSetting;