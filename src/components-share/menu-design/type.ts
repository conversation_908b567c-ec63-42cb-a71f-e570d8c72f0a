import { ListData, FormSwitchProps, AnyObj, MenuItemData, DialogProps } from "@weapp/ui";
import { ComponentType } from "react";
import { CommonDesignProps, CommonProps } from "../../types/common";
import { ConditionDatas } from "../condition";

export interface DesignPropsType extends CommonDesignProps {
  /* Menu对应的MenuContent仅一项 */
  isSingleContent?: boolean;
  bindKey?: string;
  [key: string]: any;
}

export interface MenuDesignCommonProps extends CommonProps {
  /* 设计器固定参数 */
  designProps?: DesignPropsType;
}

export type MenuDesignSingleDataType = MenuItemData & {
  /* 页签自定义设置标记 */
  custom?: boolean;
  /* 页签自定义设置数据集合 */
  menuContentConfig?: AnyObj;
  hidden?: boolean;
  __content?: any;
}
export type MenuDesignDataType = MenuDesignSingleDataType[];

export type MenuDesignVisualDataType = MenuDesignDataType & {
  value?: MenuDesignDataType;
  visualData?: MenuDesignDataType;
};

export interface MenuDesignProps extends MenuDesignCommonProps {
  value: MenuDesignDataType;
  onChange?: (value: MenuDesignDataType) => void;
  dialogProps?: DialogProps;
  /* 添加页签回调 */
  onAdd?: () => void;
  /* 关闭页签弹框回调 */
  onClose?: () => void;
}

export type MenuDesignType = ComponentType<MenuDesignProps>;

export type SettingType = 'add' | 'edit' | 'rename';
export interface SettingProps extends MenuDesignCommonProps {
  visible: boolean;
  onClose: () => void;
  onSave: (datas: ListData, oldData: ListData) => void;
  settingType: SettingType;
  data: ListData;
  dialogProps?: DialogProps;
  labelSpan?: number;
}

export interface ListItemProps extends MenuDesignCommonProps {
  data: ListData;
  onMenuChange?: (value: string, data: ListData) => void;
  defaultEditable?: boolean;
}

export interface CustomRenderItemProps extends MenuDesignCommonProps {
  id?: string,
  props?: FormSwitchProps,
}

export interface MenuDesignContextProps extends MenuDesignCommonProps {
  labelSpan?: number,
  /* 模块数据 */
  moduleDatas?: AnyObj;
  /* 更新模块数据满足条件 */
  getModulesCondition?: (value: string, otherParams?: AnyObj) => void;
  /* 事项筛选数据 */
  matterFilter?: ConditionDatas;
}

export interface ConditionProps extends MenuDesignCommonProps {
  visible: boolean;
  onClose: () => void;
  onSave: (datas: any) => void;
}

export interface CustomSettingProps extends MenuDesignCommonProps, FormSwitchProps {

}

export interface CustomSettingRef {
  validate?: () => Promise<boolean>;
}

export interface MatterFilterProps extends MenuDesignCommonProps {
  value: AnyObj[];
  onChange: (value: AnyObj[]) => void;
}

export interface ConditonProps extends MenuDesignCommonProps {
  value?: AnyObj[];
  onSave?: (value: AnyObj[]) => void;
  onClose?: () => void;
  visible?: boolean;
}

export type EbuilderPageValue = {
  appId: string;
  content: string;
  id: string;
  [key: string]: any;
}
