import { AnyObj } from "@weapp/ui";
import { request, RequestOptionConfig } from "@weapp/utils";



export const getOptions = (type: ApiType, apiModule?: string): RequestOptionConfig => {
  const options = {
    getModules: {
      url: '/api/ebuilder/common/ebvisual/search/modules',
      method: 'GET',
    },
    getFormFields: {
      url: `/api/${apiModule}/ebvisual/search/formFields`,
      method: 'GET',
    },
    getPageDatasets: {
      url: `/api/bs/ebuilder/designer/datasource/listByPage`,
      method: 'GET',
    },
    getBaseFields: {
      url: `/api/${apiModule}/ebvisual/search/baseFields`,
      method: 'GET',
    }
  };
  return options[type] as RequestOptionConfig;
}

export type ApiType = 'getModules' | 'getFormFields' | 'getPageDatasets' | 'getBaseFields';

export const doRequest = (type: ApiType, apiModule?: string, otherParams?: AnyObj) => {
  const opt = getOptions(type, apiModule);

  // const testOrigin = {
  //   host: '127.0.0.1',
  //   port: 10010,
  // }
  // opt.proxy = testOrigin;

  return request({
    ...opt,
    ...otherParams,
    // url: `http://${testOrigin.host}:${testOrigin.port}${opt.url}`,
  });
}
