import React, { memo, ReactNode, useCallback, useEffect, useMemo, useState } from "react";
import { AnyObj, EditableTable, FormItemProps, FormValue, Icon, utils } from "@weapp/ui";
import { ConditionDatas, ConditionProps, ConditionValue, OperateIconProps } from "./type";
import { classnames } from "@weapp/utils";

const { getRandom, isObject } = utils;

const getCascadeRules = (datas: ConditionDatas, disabledVariables?: boolean) => {
  let cascadeRules = {};
  const result = datas?.map((data) => {
    const cascadeRule: string[] = [];
    const formProps = data?.formProps;
    if (data?.compares) cascadeRule.push(`${data.id}_compares`);
    if (data?.variables && !disabledVariables) cascadeRule.push(`${data.id}_variables`);
    if (isObject(formProps)) {
      cascadeRule.push(`${data.id}`);
    } else {
      formProps?.forEach((item: any, index: number) => {
        cascadeRule.push(`${data.id}_${index}`);
      })
    }
    cascadeRule.length > 0 && (cascadeRules = {
      ...cascadeRules,
      [`${data.id}`]: cascadeRule,
    })
    return {
      ...data,
      content: data.text,
      variables: data.variables?.map((da) => ({ ...da, content: da.text })) || [],
      compares: data.compares?.map((da) => ({ ...da, content: da.text })) || [],
    }
  })
  return { cascadeRules, datas: result };
}
const getValue = (value: ConditionValue, cascadeRules: AnyObj, multiple?: boolean) => {
  let cells = {};
  value?.forEach((val) => {
    cascadeRules?.[val.condition] ? (
      cells = {
        ...cells,
        [`${val.id}`]: { condition: multiple ? ['condition', ...cascadeRules?.[val.condition], 'operate'] : ['condition', ...cascadeRules?.[val.condition]] },
      }
    ) : multiple && (
      cells = {
        ...cells,
        [`${val.id}`]: { condition: ['condition', 'operate'] },
      }
    )
  });
  return { values: value, cells };
}

const OperateIcon = memo(React.forwardRef<{}, OperateIconProps>(
  (props) => {
    const { prefixCls, id, onDeleteById } = props;
    const doDelete = useCallback(() => {
      onDeleteById?.(id);
    }, [onDeleteById, id])
    return <div className={`${prefixCls}-operate`} onClick={doDelete}>
      <Icon weId={`${props.weId || ''}_tj70k5`} name="Icon-error01" />
    </div>
  }
))

const Condition = memo(React.forwardRef<{}, ConditionProps>(
  (props, ref) => {
    const { prefixCls, datas: _datas, value: _value, onChange: _onChange, multiple, noLine, disabledVariables, onItemEditBefore, placeholder } = props;
    const [cascadeRules, setCascadeRules] = useState<AnyObj>({});
    const [value, setValue] = useState<ConditionValue>([]);
    const [cells, setCells] = useState({});
    const [datas, setDatas] = useState(_datas);

    useEffect(() => {
      const { datas: result, cascadeRules } = getCascadeRules(_datas, disabledVariables);
      setCascadeRules(cascadeRules);
      setDatas(result);
    }, [_datas, disabledVariables]);

    useEffect(() => {
      if (_value) {
        const { values, cells } = getValue(_value, cascadeRules, multiple);
        setValue(values);
        setCells(cells);
      } else if (value && value.length === 0 && !multiple) {
        // 不允许重复添加数据的场景需默认一行数据
        const id = getRandom();
        const condition = datas?.[0].id;
        if (condition) {
          const cell = cascadeRules?.[condition] || [];
          setValue([{ id, condition }]);
          setCells({
            [`${id}`]: { condition: ['condition', ...cell] },
          })
        }
      }
    }, [_value, value, cascadeRules, multiple, datas]);

    const onDeleteById = useCallback((id: string) => {
      const result = value?.filter((val) => val.id !== id);
      setValue(result);
      _onChange?.(result);
    }, [value, _onChange])

    const renderOperate = useCallback((formItemInstance: ReactNode, content: any, rowData: any) => {
      return <OperateIcon weId={`${props.weId || ''}_a3l12n`} id={rowData.id} prefixCls={prefixCls} onDeleteById={onDeleteById} />
    }, [prefixCls, props.weId, onDeleteById])

    const columns = useMemo(() => [
      {
        title: '',
        dataIndex: 'condition',
        comKey: 'condition',
        width: '100%'
      },
    ], []);

    const comProps = useMemo(() => {
      let allItems = {};
      datas?.forEach((data) => {
        const formProps = data?.formProps;
        if (isObject(formProps)) {
          allItems = {
            ...allItems,
            [`${data.id}`]: formProps,
          }
        } else {
          formProps?.forEach((item: any, index: number) => {
            allItems = {
              ...allItems,
              [`${data.id}_${index}`]: item,
            }
          })
        }
        if (data?.compares) {
          allItems = {
            ...allItems,
            [`${data.id}_compares`]: { itemType: 'SELECT', data: data.compares },
          }
        }
        if (data?.variables && !disabledVariables) {
          allItems = {
            ...allItems,
            [`${data.id}_variables`]: { itemType: 'SELECT', data: data.variables },
          }
        }
      })
      return {
        condition: { itemType: 'SELECT', data: datas },
        operate: { itemType: 'CUSTOM', customRender: renderOperate },
        ...allItems,
      } as FormItemProps;
    }, [datas, renderOperate, disabledVariables]);

    const onChange = useCallback((value: any) => {
      _onChange?.(value);
      setValue(value);
    }, [_onChange]);

    const onItemChange = useCallback((editKey: string, editValue: FormValue, rowId: string, rowData: any) => {
      if (editKey === 'condition') {
        const val = editValue as string;
        const cell = cascadeRules[val] || [];
        setCells({
          ...cells,
          [`${rowId}`]: {
            [`${editKey}`]: multiple ? ['condition', ...cell, 'operate'] : ['condition', ...cell],
          }
        })
      }
    }, [cells, cascadeRules, multiple]);

    const onAdd = useCallback(() => {
      const id = getRandom();
      const result = [...value, { id }];
      setValue(result);
      setCells({ ...cells, [`${id}`]: { condition: multiple ? ['condition', 'operate'] : [] } });
      _onChange?.(result);
    }, [value, _onChange, cells, multiple]);

    const wrapCls = classnames(prefixCls, {
      [`${prefixCls}-noLine`]: noLine,
      [`${prefixCls}-isSingle`]: !multiple,
    })

    return <div className={wrapCls}>
      {
        datas?.length > 0 ? (
          <>
            <EditableTable
              weId={`${props.weId || ''}_5v1i28`}
              columns={columns}
              comProps={comProps}
              data={value}
              isShowHead={false}
              showSelection={false}
              onChange={onChange}
              onItemChange={onItemChange}
              cascadeRules={cascadeRules}
              cells={cells}
              onItemEditBefore={onItemEditBefore}
            />
            {
              multiple && (
                <div className={`${prefixCls}-add`} onClick={onAdd}>
                  <Icon weId={`${props.weId || ''}_pw60t5`} name="Icon-add-to01" />
                  <span>添加条件</span>
                </div>
              )
            }
          </>
        ) : (
          <div className={`${prefixCls}-noCondition`}>{placeholder || '未设置条件'}</div>
        )
      }
    </div>
  }
))

export default Condition;