.@{conditionClsPrefix} {
  &-add {
    color: var(--primary);
    font-size: var(--font-size-12);
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    padding: var(--v-spacing-md) 0;
  }

  &-operate {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    border: var(--border-solid);
    width: calc(24 * var(--hd));
    height: calc(24 * var(--hd));
    text-align: center;

    .ui-icon {
      color: #979797;
    }
  }

  .ui-editable-table-align-top .ui-table-grid-td .ui-formItem-wrapper .ui-formItem-singleItem {
    align-self: center;
  }

  .ebcomponents-config-filter-inp {
    border-radius: calc(var(--hd)*3);

    &-search {
      padding-left: calc(var(--hd)*4);
    }
  }
}

.@{conditionClsPrefix}-noLine {

  .ui-editable-table .ui-table-grid,
  .ui-table-grid-td {
    border: 0;
  }

  .ui-editable-table-container .ui-table-grid-td {
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
    padding-bottom: 0;
  }

  .ui-editable-table-container .ui-table-grid-tbody .ui-table-grid-tr:hover,
  .ui-table-grid-tr:hover td {
    background-color: var(--white-base);
  }

  .ui-table-grid-empty {
    border: var(--border-solid);
  }
}

.@{conditionClsPrefix}-noCondition {
  color: var(--regular-fc);
  font-size: var(--font-size-12);
}

.@{conditionClsPrefix}-isSingle {
  .ui-editable-table .ui-table-grid-table-ellipsis .ui-table-grid-tbody .ui-table-grid-nowrap {
    padding: 0;
    height: auto;
  }
}