import { conditionClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { ConditionType } from "./type";

const Condition = Loadable({
  name: 'Condition', loader: () => import(
    /* webpackChunkName: "condition" */
    './Condition'
  )
}) as ConditionType;

Condition.defaultProps = {
  prefixCls: conditionClsPrefix,
}

export default Condition;

export type { ConditionType, ConditionProps, ConditionDatas, ConditionValue } from './type';
