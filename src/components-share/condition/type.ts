import { AnyObj, DataType, FormSingleItemProps, FormValue } from "@weapp/ui";
import { ComponentType } from "react";
import { CommonProps } from "../../types/common";

export type ConditionValue = Array<AnyObj>;

export interface SingleDataType extends DataType {
  /* 选项名称 */
  text?: string;
}
export interface ConditionData extends DataType {
  /* 选项名称 */
  text?: string;
  /* 模块标记 */
  route?: string;
  /* 联动带出的字段配置 */
  formProps?: FormSingleItemProps | Array<FormSingleItemProps>;
  /* 变量/常量选项 */
  variables?: SingleDataType[];
  /* 条件选项 */
  compares?: SingleDataType[];
}

export type ConditionDatas = Array<ConditionData & AnyObj>;
export interface ConditionProps extends CommonProps {
  /* 条件配置 */
  datas: ConditionDatas;
  /* 条件值 */
  value?: ConditionValue;
  /* 条件改变回调 */
  onChange?: (value: ConditionValue) => void;
  /* 允许重复添加数据 */
  multiple?: boolean;
  /* 无边框模式 */
  noLine?: boolean;
  /* 禁止设置变量/常量 */
  disabledVariables?: boolean;
  /* 编辑单元格,data改变之前的回调 */
  onItemEditBefore?: (newData: any, editKey: string, editValue: FormValue, rowData: any) => void;
  placeholder?: string;
}

export type ConditionType = ComponentType<ConditionProps>;

export interface OperateIconProps extends CommonProps {
  id: string;
  onDeleteById: (id: string) => void;
}
