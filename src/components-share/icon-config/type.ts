import { AnyObj } from "@weapp/ui";
import { ComponentType, ReactNode } from "react";
import { CommonConfigCustomProps } from "../../types/common";

export interface IconConfigProps extends CommonConfigCustomProps {
  iconEidtorProps?: AnyObj; // IconEidtor 组件所有参数
}

export type IconConfigType = ComponentType<IconConfigProps> & {
  getIcon: (props: AnyObj, otherProps?: AnyObj) => ReactNode; // 获取Icon组件
};