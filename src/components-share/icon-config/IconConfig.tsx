import { CorsComponent } from "@weapp/ui";
import React, { useCallback } from "react";
import { IconConfigProps, IconConfigType } from "./type";


const IconConfig = React.forwardRef<any, IconConfigProps>(
  (props) => {
    const { iconEidtorProps, config, onFormChange, dataKey = 'icon' } = props;

    const value = config?.[dataKey];
    const onChange = useCallback((value: string) => {
      onFormChange?.({
        [dataKey]: value
      });
    }, [onFormChange, dataKey]);

    return (<CorsComponent weId={`${props.weId || ''}_ux25rn`}
      app="@weapp/ebdcoms"
      compName={"IconEidtor"}
      {...iconEidtorProps}
      value={value}
      onChange={onChange} />);
  }
) as unknown as IconConfigType;

export default IconConfig;