import { AnyObj, CorsComponent } from "@weapp/ui";
import { iconConfigClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { IconConfigType } from "./type";

// 等UI库IconSelection支持返回Icon等需求后替换
const IconConfig = Loadable({
  name: 'IconConfig', loader: () => import(
    /* webpackChunkName: "icon-selection-config" */
    './IconConfig'
  )
}) as IconConfigType;

IconConfig.defaultProps = {
  prefixCls: iconConfigClsPrefix,
}

export const getIcon = (props: AnyObj, otherProps?: AnyObj) => {
  return (
    <CorsComponent weId={`${props.weId || ''}_aylbfw`}
      app="@weapp/ebdcoms"
      compName="AssetsItem"
      {...props}
      {...otherProps}
    />
  )
}

IconConfig.getIcon = getIcon;

export default IconConfig;

export type { IconConfigType, IconConfigProps } from './type';
