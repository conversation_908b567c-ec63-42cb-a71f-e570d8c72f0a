import { pageSizeConfigClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { PageSizeConfigType } from "./type";

const PageSizeConfig = Loadable({
  name: 'PageSizeConfig', loader: () => import(
    /* webpackChunkName: "PageSizeConfig" */
    './PageSizeConfig'
  )
}) as PageSizeConfigType;

PageSizeConfig.defaultProps = {
  prefixCls: pageSizeConfigClsPrefix,
  dataKey: 'pageSize'
}

export default PageSizeConfig;

export type { PageSizeConfigType, PageSizeConfigProps } from './type';
