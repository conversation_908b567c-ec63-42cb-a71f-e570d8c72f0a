import React, { ReactText, useCallback, useMemo, useState } from "react";
import { classnames } from "@weapp/utils";
import { Input, Select, SelectOptionData, utils, SelectValueType } from "@weapp/ui";
import { pageSizeConfigClsPrefix } from "../../constants";
import { PageSizeConfigProps } from "./type";

const { isNil } = utils;
const InputNumber = Input.InputNumber;
const pageSet = new Set(['5', '10', '20', '50', '100'])
const PageSizeConfig = React.forwardRef<{}, PageSizeConfigProps>(
  (props) => {
    const { prefixCls, config, onFormChange, className, dataKey = 'pageSize' } = props;
    const [page, updatePage] = useState<ReactText>(10);

    let content = null, value = config?.[dataKey];
    if (!pageSet.has(value)) {
      content = (
        <div>{config?.[dataKey]}</div>
      )
      value = 'custom';
    }

    const selectClassName = classnames(pageSizeConfigClsPrefix, className, prefixCls);
    const data = useMemo(() => (
      [
        { id: '5', content: '5' },
        { id: '10', content: '10' },
        { id: '20', content: '20' },
        { id: '50', content: '50' },
        { id: '100', content: '100' },
        { id: 'custom', content: '自定义' },
      ]
    ), [])
    
    const onChangePage = useCallback((value: ReactText) => {
      updatePage(value);
    }, []);

    const onBlurPage = useCallback(() => {
      value === 'custom' && !isNil(page) && onFormChange?.({
        [`${dataKey}`]: page,
      })
    }, [onFormChange, page, value, dataKey]);

    const customOptionRender = useCallback((option: SelectOptionData) => {
      if (option.id === 'custom') return (
        <div className={`${pageSizeConfigClsPrefix}-custom`}>
          <span>{option?.content}</span>
          <InputNumber weId={`_mp0o8g`} value={page} onChange={onChangePage} onBlur={onBlurPage} />
        </div>
      )
      return option?.content;
    }, [onBlurPage, onChangePage, page])

    const onChange = useCallback((value: SelectValueType) => {
      onFormChange?.({
        [`${dataKey}`]: value === 'custom' && !isNil(page) ? page : value,
      })
    }, [onFormChange, page, dataKey]);

    return <div className={`${pageSizeConfigClsPrefix}-container`}>
      {value === 'custom' && (
        <div className={`${pageSizeConfigClsPrefix}-custom-content`}>{content}</div>
      )}
      <Select
        weId={`${props.weId || ''}_pku8s7`}
        data={data}
        customOptionRender={customOptionRender}
        className={selectClassName}
        onChange={onChange}
        value={value}
      />
    </div>
  }
)

export default PageSizeConfig;