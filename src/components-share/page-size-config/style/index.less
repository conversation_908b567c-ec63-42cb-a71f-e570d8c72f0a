.@{pageSizeConfigClsPrefix} {
  &-custom {
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    span {
      padding-right: var(--h-spacing-md);
    }
  }
  &-container {
    position: relative;
  }
  &-custom-content {
    position: absolute;
    left: calc(2 * var(--hd));
    top: calc(5 * var(--hd));
    height: calc(20 * var(--hd));
    width: 80%;
    background: var(--base-white);
    z-index: 6;
    line-height: var(--input-line-height);
    padding-left: calc(7 * var(--hd));
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    pointer-events: none;
    font-size: var(--font-size-12);
  }
}