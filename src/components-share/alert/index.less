.@{alertCommonClsPrefix} {
  padding: calc(5 * var(--hd)) calc(10 * var(--hd));
  font-size: var(--font-size-12);
  color: #5d9cec;
  background: #edf5ff;
  border: calc(1 * var(--hd)) solid #bcd7ff;
  border-radius: calc(3 * var(--hd));

  &-title {
    color: #83a4e7;
    display: flex;
    align-items: flex-start;
    font-weight: 700;
    margin-right: calc(3 * var(--hd));

    &-title {
      padding: calc(1 * var(--hd)) 0;
    }

    &:hover~div.@{alertCommonClsPrefix}-content {
      display: block;
    }
  }

  &-content {
    padding-top: calc(3 * var(--hd));
    color: #83a4e7;
    display: none;
  }
}