import React, { memo } from "react";
import { AlertProps } from "./types";
import { Help } from "@weapp/ui";

const Alert = memo(React.forwardRef<{}, AlertProps>(
  (props) => {
    const { title, helpUrl, content, prefixCls } = props;

    return <div className={prefixCls}>
      <div className={`${prefixCls}-title`}>
        <span className={`${prefixCls}-title-title`}>{title}</span>
        {helpUrl && <Help weId={`${props.weId || ''}_b56tut`} helpUrl={helpUrl} />}
      </div>
      <div className={`${prefixCls}-content`}>
        {content}
      </div>
    </div>
  }
));

export default Alert;
