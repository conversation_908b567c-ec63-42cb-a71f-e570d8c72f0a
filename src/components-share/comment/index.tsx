import { commentCommonClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { CommentType } from "./types";

const Comment = Loadable({
  name: 'Comment', loader: () => import(
    /* webpackChunkName: "comment" */
    './Comment'
  )
}) as CommentType;

Comment.defaultProps = {
  prefixCls: commentCommonClsPrefix,
}

export default Comment;

export type { CommentType, EbCommentProps as CommentProps } from './types';