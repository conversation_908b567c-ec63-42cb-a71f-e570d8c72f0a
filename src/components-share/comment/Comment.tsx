import React, { FC, memo, useMemo } from "react";
import { CommentView } from "../../components/comment/view/View";
import { ua } from "@weapp/utils";
import { EbCommentProps } from "./types";
import { commentDefConfig } from "../../components/comment/constant/defConfig";

const Comment: FC<EbCommentProps> = memo(React.forwardRef<any, EbCommentProps>(
  (props, ref) => {
    const { weId, prefixCls, ...resProps } = props;

    const page: any = useMemo(() => ({
      client: ua.device,
    }), [])

    return (
      <CommentView
        config={resProps}
        page={page}
        {...resProps}
        ref={ref}
        weId={`${props.weId || ''}_jfz24j`}
      />
    )
  }
));

Comment.defaultProps = commentDefConfig;

export default Comment;