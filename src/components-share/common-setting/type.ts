import { ComponentType } from "react";
import { AnyObj } from "@weapp/ui";
import { CompDesignStoreType } from "@weapp/designer";
import { CommonProps } from "../../types/common";

export interface CommonSettingProps<Data = any> extends CommonProps {
  /* 组件名 */
  compName: string;
  /* 包名 */
  app?: string;
  value?: Data;
  onChange?: (value: Data, otherParams?: AnyObj) => void;
  /* 过滤配置展示的字段 */
  fields?: string[];
  /* id 唯一标记 */
  id?: string;
  /*
   配置组件类型
    schema：根据注解生存json
    ebConfig：EB组件对应的Config配置
   */
  type?: 'schema' | 'ebConfig';
  /**
   * ebConfig：EB组件对应的Config配置自定义store
   */
  store?: CompDesignStoreType & AnyObj;
}

export interface CommonSettingState<Data = any> extends CommonProps {
  value?: Data;
  onChange?: (value: Data, otherParams?: AnyObj) => void;
}

export interface CommonSettingRef {
}
export type CommonSettingType = ComponentType<CommonSettingProps>;