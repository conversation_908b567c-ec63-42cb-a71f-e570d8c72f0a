import { commonSettingClsPrefix } from "../../constants";
import Loadable from "../../react-loadable";
import { CommonSettingType } from "./type";
import { default as _CommonSetting } from './CommonSetting';

/* 接入EB CoreConfig组件，调用配置页面 */
const CommonSetting = Loadable({
  name: 'CommonSetting',
  component: _CommonSetting,
}) as CommonSettingType;

CommonSetting.defaultProps = {
  prefixCls: commonSettingClsPrefix,
  app: '@weapp/ui-props-design',
  type: 'ebConfig'
}

export default CommonSetting;

export type { CommonSettingType, CommonSettingProps } from './type';