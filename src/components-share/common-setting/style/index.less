.@{commonSettingClsPrefix} {
  width: 100%;
  .weapp-de-form {
    width: 100%;
    .ui-formItem {
      padding: var(--form-item-padding-module);
    }
    .ui-form-row, .ui-form-row .ui-form-col {
      border-left: 0;
      border-right: 0;
      border: 0;
    }
    .ui-form-row:not(:last-child) {
      border-bottom: var(--form-item-border-module);
    }
  }
  .weapp-de-form>.ui-form-row-first {
    padding-top: 0;
  }
  /* ebdcoms Config 样式处理*/
  .ebcoms-config-form>.ui-form-row-first {
    padding-top: 0;
    border: 0;
  }
  .ebcoms-config-form .ui-formItem {
    padding: var(--form-item-padding-module);
  }
  .ui-form .ui-form-row:last-child {
    border-bottom: 0;
  }
  .ui-form .ui-form-row .ui-form-col:last-child {
    border-right: 0;
  }
  .ui-form .ui-form-row .ui-form-col:first-child {
    border-left: 0;
  }
}