import React from "react";
import { observer } from "mobx-react";
import { CorsComponent, FormStore } from "@weapp/ui";
import { classUseMemo } from "@weapp/utils";
import { CommonSettingProps, CommonSettingState } from "./type";

@observer
class CommonSetting extends React.Component<CommonSettingProps, CommonSettingState> {
  store: FormStore;
  constructor(props: CommonSettingProps) {
    super(props);
    this.state = {
      value: {},
    }
    this.store = new FormStore({ afterInitForm: this.afterInitForm })
  }
  componentDidMount() {
    const { value } = this.props;
    if (value) this.setState({ value });
  }
  afterInitForm = () => {
    if (this.store?.isFormInit) {
      const layout = this.store?.layout.map((row) => {
        return row.map((col) => ({ ...col, labelSpan: 4 }))
      })
      this.store?.setLayout(layout);
    }
  }
  doChange = (val: any) => {
    const { value } = this.state;
    const { id, onChange } = this.props;
    const _value = { ...value, ...val };
    this.setState({ value: _value });
    const changeValue = id ? { [`${id}`]: _value } : _value;
    onChange?.(changeValue);
  };
  render() {
    const { value } = this.state;
    const { prefixCls, compName, app, fields, type, store } = this.props;
    const com = classUseMemo('com', this, () => ({ type: compName, package: app }), [compName, app]);
    return (<div className={prefixCls}>
      {
        type === 'schema' ? (
          <CorsComponent
            weId={`${this.props.weId || ''}_g0di02`}
            app="@weapp/designer"
            compName="CoreConfig"
            com={com}
            onChange={this.doChange}
            formDatas={value}
            fields={fields}
            store={this.store}
          />
        ) : (
          <CorsComponent weId={`${this.props.weId || ''}_amy297`}
            app="@weapp/ebdcoms"
            compName="Config"
            type={compName}
            packageName={app}
            onChange={this.doChange}
            store={store}   
            data={value}         
          />
        )
      }
    </div>)
  }
}

export default CommonSetting;