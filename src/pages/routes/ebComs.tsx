import asyncComponent from '../utils/asyncMdComponent'
import { routeType } from '../types'

const impCom = () => {

  const ButtonDemo = asyncComponent(() => import(/* webpackChunkName: "ebDemos_pc_button" */'../ebDemos/button/pc/index.md'))
  const BrowserDemo = asyncComponent(() => import(/* webpackChunkName: "ebDemos_pc_browser" */'../ebDemos/browser/pc/index.md'))
  const HelpDemo = asyncComponent(() => import(/* webpackChunkName: "ebDemos_pc_help" */'../ebDemos/help/pc/index.md'))
  const EmptyDemo = asyncComponent(() => import(/* webpackChunkName: "ebDemos_pc_empty" */'../ebDemos/empty/pc/index.md'))

  const routes: routeType[] = [
    {
      com: ButtonDemo,
      path: '/ebDemos/button',
      comType: 'Basic',
      title: 'Button',
      key: 'Button',
      maintainer: ['张凯伟'],
    },
    {
      com: BrowserDemo,
      path: '/ebDemos/browser',
      comType: 'Basic',
      title: 'Browser',
      key: 'Browser',
      maintainer: ['蒋贝贝'],
    },
    {
      com: HelpDemo,
      path: '/ebDemos/help',
      comType: 'Basic',
      title: 'Help',
      key: 'Help',
      maintainer: ['陈佳敏'],
    },
    {
      com: EmptyDemo,
      path: '/ebDemos/empty',
      comType: 'Basic',
      title: 'Empty',
      key: 'Empty',
      maintainer: ['蒋贝贝'],
    },
    {
      com: EmptyDemo,
      path: '/ebDemos/comment',
      comType: 'Basic',
      title: 'Comment',
      key: 'Comment',
      maintainer: ['冯程'],
    },
  ]
  return routes
}

export default impCom;
