import asyncComponent from '../utils/asyncMdComponent'
import { routeType } from '../types'

const impCom = () => {

  const MenuDesignDemo = asyncComponent(() => import(/* webpackChunkName: "demo_pc_menu_design" */'../demos/menu-design/pc/index.md'))
  const CommonSettingDemo = asyncComponent(() => import(/* webpackChunkName: "demo_pc_common_setting" */'../demos/common-setting/pc/index.md'))

  const routes: routeType[] = [
    {
      title: 'MenuDesign',
      path: '/menu-design',
      com: MenuDesignDemo,
      comType: 'Basic',
      key: 'MenuDesign',
      maintainer: ['蒋贝贝'],
    },
    {
      title: 'CommonSetting',
      path: '/common-setting',
      com: CommonSettingDemo,
      comType: 'Basic',
      key: 'CommonSetting',
      maintainer: ['蒋贝贝'],
    },
  ]
  return routes
}

export default impCom
