.demo_markdown {
  color: #666;
  // font-size: 14Px;
  // line-height: 1.8;

  i {
    font-weight: normal;
  }
}

.markdown-toc-item {
  color: var(--primary);
}

.highlight {
  line-height: 1.5;
}

.demo_markdown img {
  vertical-align: middle;
  max-width: 100%;
}

.demo_markdown h1 {
  color: #404040;
  font-weight: bold;
  line-height: 40px;
  margin-bottom: 24px;
  margin-top: 8px;
  font-family: lato, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;

  .subtitle {
    font-weight: normal;
    font-size: 80%;
    margin-left: 12px;
  }
}

.demo_markdown h2,
.demo_markdown h3,
.demo_markdown h4,
.demo_markdown h5,
.demo_markdown h6 {
  color: #404040;
  margin: 1.6em 0 0.6em;
  font-weight: 500;
  clear: both;
}

.demo_markdown h1 {
  font-size: 28px;
}
.demo_markdown h2 {
  font-size: 22px;
}
.demo_markdown h3 {
  font-size: 18px;
}
.demo_markdown h4 {
  font-size: 16px;
}
.demo_markdown h5 {
  font-size: 14px;
}
.demo_markdown h6 {
  font-size: 12px;
}

.demo_markdown hr {
  height: 1px;
  border: 0;
  background: #e9e9e9;
  margin: 16px 0;
  clear: both;
}

.demo_markdown p,
.demo_markdown pre {
  margin: 1em 0;
}

.demo_markdown ul > li {
  // list-style: circle;
}

.demo_markdown > ul li,
.demo_markdown blockquote ul > li {
  margin-left: 20px;
  padding-left: 4px;
}

.demo_markdown > ul li > p,
.demo_markdown > ol li > p {
  margin: 0.2em 0;
}

.demo_markdown ol > li {
  list-style: decimal;
}

.demo_markdown > ol li,
.demo_markdown blockquote ol > li {
  margin-left: 20px;
  padding-left: 4px;
}

.demo_markdown code {
  margin: 0 3px;
  background: #f7f7f7;
  padding: 1px 6px;
  border-radius: 3px;
  color: #777;
  font-size: 12.8px;
  border: 1px solid #e9e9e9;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  .participants {
    margin-right: 10px;
    &:last-child {
      margin-right: 0;
    }
  }
}

.demo_markdown pre {
  border-radius: 6px;
  background: #f7f7f7;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
}

.demo_markdown pre code {
  border: none;
  background: #f7f7f7;
  margin: 0;
  font-size: 13px;
  color: #666;
  // overflow: auto;
}

.demo_markdown strong,
.demo_markdown b {
  font-weight: 600;
}

.demo_markdown table {
  border-collapse: collapse;
  border-spacing: 0;
  empty-cells: show;
  border: 1px solid #e9e9e9;
  width: 100%;
  margin: 24px 0;
}

.demo_markdown > table th {
  white-space: nowrap;
  color: #5c6b77;
  font-weight: 600;
}
.demo_markdown > table th,
.demo_markdown > table td {
  border: 1px solid #e9e9e9;
  padding: 8px 16px;
  text-align: left;
}

.demo_markdown > table th {
  background: #f7f7f7;
}

.demo_markdown blockquote {
  font-size: 90%;
  color: #999;
  border-left: 4px solid #e9e9e9;
  padding-left: 0.8em;
  margin: 1em 0;
  font-style: italic;
}

.demo_markdown blockquote p {
  margin: 0;
}

.demo_markdown .anchor {
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-left: 8px;
}

.demo_markdown .waiting {
  color: #ccc;
  cursor: not-allowed;
}

.demo_markdown h1:hover .anchor,
.demo_markdown h2:hover .anchor,
.demo_markdown h3:hover .anchor,
.demo_markdown h4:hover .anchor,
.demo_markdown h5:hover .anchor,
.demo_markdown h6:hover .anchor {
  opacity: 1;
  display: inline-block;
}

.demo_markdown > br,
.demo_markdown > p > br {
  clear: both;
}

.demo_markdown.api-container table {
  //font-family: Consolas, Menlo, Courier, monospace;
  //font-size: 13Px;
}

.layout-demo,
[id^="components-layout-demo-"] {
  .demo-row,
  .code-box-demo .demo-row {
    background-image: linear-gradient(
      90deg,
      #f5f5f5 4.16666667%,
      transparent 4.16666667%,
      transparent 8.33333333%,
      #f5f5f5 8.33333333%,
      #f5f5f5 12.5%,
      transparent 12.5%,
      transparent 16.66666667%,
      #f5f5f5 16.66666667%,
      #f5f5f5 20.83333333%,
      transparent 20.83333333%,
      transparent 25%,
      #f5f5f5 25%,
      #f5f5f5 29.16666667%,
      transparent 29.16666667%,
      transparent 33.33333333%,
      #f5f5f5 33.33333333%,
      #f5f5f5 37.5%,
      transparent 37.5%,
      transparent 41.66666667%,
      #f5f5f5 41.66666667%,
      #f5f5f5 45.83333333%,
      transparent 45.83333333%,
      transparent 50%,
      #f5f5f5 50%,
      #f5f5f5 54.16666667%,
      transparent 54.16666667%,
      transparent 58.33333333%,
      #f5f5f5 58.33333333%,
      #f5f5f5 62.5%,
      transparent 62.5%,
      transparent 66.66666667%,
      #f5f5f5 66.66666667%,
      #f5f5f5 70.83333333%,
      transparent 70.83333333%,
      transparent 75%,
      #f5f5f5 75%,
      #f5f5f5 79.16666667%,
      transparent 79.16666667%,
      transparent 83.33333333%,
      #f5f5f5 83.33333333%,
      #f5f5f5 87.5%,
      transparent 87.5%,
      transparent 91.66666667%,
      #f5f5f5 91.66666667%,
      #f5f5f5 95.83333333%,
      transparent 95.83333333%
    );
    overflow: hidden;
  }
  .ant-row-flex,
  .code-box-demo .ant-row-flex {
    background: #f5f5f5;
  }
  .ant-row > div,
  .code-box-demo .ant-row > div,
  .ant-row-flex > div,
  .code-box-demo .ant-row-flex > div {
    padding: 5px 0;
    text-align: center;
    border-radius: 6px;
    min-height: 30px;
    margin-top: 10px;
    margin-bottom: 10px;
    color: #fff;
  }
  .code-box-demo .ant-row > div:not(.gutter-row),
  .code-box-demo .ant-row-flex > div:not(.gutter-row) {
    background: #6ac2f5;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  .ant-row .demo-col,
  .code-box-demo .ant-row .demo-col {
    text-align: center;
    padding: 40px 0;
    color: #fff;
    font-size: 18px;
    border: none;
    margin-top: 0;
    margin-bottom: 0;
  }
  .ant-row .demo-col-1,
  .ant-row .demo-col-1 {
    background: rgba(29, 128, 211, 0.7);
  }
  .ant-row .demo-col-2,
  .code-box-demo .ant-row .demo-col-2 {
    background: rgba(29, 128, 211, 0.5);
  }
  .ant-row .demo-col-3,
  .code-box-demo .ant-row .demo-col-3 {
    background: rgba(255, 255, 255, 0.2);
    color: #999;
  }
  .ant-row .demo-col-4,
  .code-box-demo .ant-row .demo-col-4 {
    background: rgba(29, 128, 211, 0.6);
  }
  .ant-row .demo-col-5,
  .code-box-demo .ant-row .demo-col-5 {
    background: rgba(255, 255, 255, 0.5);
    color: #999;
  }
  .code-box-demo .height-100 {
    height: 100px;
  }
  .code-box-demo .height-50 {
    height: 50px;
  }
  .code-box-demo .height-120 {
    height: 120px;
  }
  .code-box-demo .height-80 {
    height: 80px;
  }
}

// For Changelog
.demo_markdown {
  ul.ant-timeline {
    li.ant-timeline-item {
      list-style: none;
      margin: 0;
      padding: 0 0 30px;
      .ant-timeline-item-content {
        font-size: 14px;
        padding-left: 32px;
        position: relative;
        top: -14px;
        > h2 {
          margin-top: 0;
          padding-top: 0.5px;
        }
      }
    }
    li.ant-timeline-item:first-child {
      margin-top: 40px;
    }
  }
}
