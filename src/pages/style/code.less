.@{codeClsPrefix}{
  .title_box {
    .title {
    font-weight: 400;
    color: #1f2f3d;
    margin: 0;
    font-size: 22px;
    }
    .description {
      font-size: 14px;
      color: #5e6d82;
      line-height: 1.5em;
    }
  }

  .demo_box {
    box-shadow: 0 0 8px 0 rgb(232 237 250 / 60%), 0 2px 4px 0 rgb(232 237 250 / 50%);
    border: 1px solid #ebebeb;
    border-radius: 3px;
    transition: .2s;
    padding:20px;
    margin:20px 0;
    color:#666;
  }

  &-icon-wrap{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #eaeefb;

    & > span {
      & + * {
        margin-left: 6px;
      }
    }

    .ui-trigger {
      margin-right: 20px;
    }
  }
}


// MCode
.@{codeClsPrefix}-mcode{
  .highlight-wrapper{
    user-select: text;
    height: 300px;
    overflow: auto;
  }

  .title_box {

    border-bottom: 1px solid #eaeefb;
    padding-bottom: 10px;


    .title {
    font-weight: 400;
    color: #1f2f3d;
    margin: 10px 0 20px;
    font-size: 22px;
    }
    .description {
      font-size: 14px;
      color: #5e6d82;
      line-height: 1.5em;
    }
  }

  .demo_box {
    box-shadow: 0 0 8px 0 rgb(232 237 250 / 60%), 0 2px 4px 0 rgb(232 237 250 / 50%);
    border: 1px solid #ebebeb;
    border-radius: 3px;
    transition: .2s;
    padding:20px;
    margin:20px 0;
    color:#666;
    cursor: pointer;
  }
  .demo_box_active {
    border: 1px solid rgba(16,142,233,.7);
  }
  &-icon-wrap{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #eaeefb;
    .ui-trigger {
      margin-right: 20px;
    }
  }
}
.qccode_content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.highlight-wrapper{
  user-select: text;
  background: #f7f7f7;
}
