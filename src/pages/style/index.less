.@{uiPropsDesignDemoClsPrefix} {
  height: 100%;
  &-main-layout, &-main-layout-side {
    height: 100%;
  }
  &-header {
    padding: 0 calc(50 * var(--hd));
    height: 100%;
    box-sizing: border-box;
    border: var(--border-solid);
    // box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    min-height: calc(64 * var(--hd));
    &-title {
      color: var(--primary);
      font-size: calc(21 * var(--hd));
    }
    &-ui-lnk {
      font-size: var(--font-size-base);
      color: var(--main-fc);
      display: flex;
      align-items: center;
      cursor: pointer;
      a:hover {
        color: var(--primary);
      }
    }
    &-type {
      display: inline-flex;
      align-items: center;
      margin-right: var(--h-spacing-lg);
      background-color: var(--bg-base);
      padding: var(--v-spacing-md) var(--h-spacing-lg);
      border-radius: var(--border-radius-lg);
      span {
        padding: 0 var(--h-spacing-md);
      }
    }
    &-isEdit {
      display: inline-block;
      width: calc(40 * var(--hd));
      cursor: pointer;
      &:hover {
        color: var(--primary);
      }
    }
  }
  &-LeftMenu {
    padding-top: calc(25 * var(--hd));
    border-right: var(--border-solid);
    height: 100%;
    .ui-menu-list-item-content {
      font-size: var(--font-size-lg);
    }
    .ui-menu-menu {
      width: 100%;
    }
  }
  &-content {
    padding-top: calc(25 * var(--hd) + var(--v-spacing-md));
    padding-left: var(--h-spacing-lg);
    &-demo {
      box-shadow: var(--box-shadow);
      margin: var(--v-spacing-lg) var(--h-spacing-lg);
      padding: var(--v-spacing-lg) var(--h-spacing-lg);
    }
    &-show {
      margin: var(--v-spacing-lg) var(--h-spacing-lg);
      .ui-btn {
        margin-bottom: var(--v-spacing-md);
      }
    }

    &-title {
      &-main {
        font-size: calc(28 * var(--hd));
        color: #404040;
        font-weight: bolder;
      }
      &-maintainer {
        margin: 0 var(--h-spacing-lg);
        background: #f7f7f7;
        padding: 1px 6px;
        border-radius: 3px;
        color: #777;
        font-size: 12.8px;
        border: 1px solid #e9e9e9;
        font-family: Consolas,Monaco,"Andale Mono","Ubuntu Mono",monospace;
      }
    }

    &-md {
      margin-right: calc(50 * var(--hd));
    }
  }
}