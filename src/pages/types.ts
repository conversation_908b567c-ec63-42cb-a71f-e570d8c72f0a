import { AnyObj, BaseProps } from '@weapp/ui';
import { ComponentType, ReactNode } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { CommonDesignProps } from '../types/common';

export interface MainProps extends RouteComponentProps {
}

export interface HeaderProps extends BaseProps {
  
}

export interface LeftMenuProps extends BaseProps, RouteComponentProps {
  data: any[];
}

export interface ContentProps extends RouteComponentProps, BaseProps {
  data: any;
  type?: string;
}

export interface SingleContentProps extends BaseProps {
  data: AnyObj;
  parentPath: string;
}

export interface ContextProps {
  type?: 'EB' | 'Normal',
  onChangeType?: (value: boolean) => void;
  isEdit?: boolean;
  onChangeEdit?: () => void;
}
export interface MainState extends ContextProps {
}

export type comType = 'Basic'

export interface routeType { key: string, title: ReactNode; path: string; design?: string | string[], maintainer?: string | string[], participants?: string | string[], com: ComponentType, comType: comType }

export interface DemoProps extends BaseProps {
  weId?: string;
  datas?: any;
  onChange?: (datas: any) => void;
  designProps?: CommonDesignProps;
}

export interface DemoRef {
  getCode: () => string;
}

export interface EditUiComponentProps extends BaseProps {

}
