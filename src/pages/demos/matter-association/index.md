---
key: MatterAssociation
title: 事项关联
type: com
person: 蒋贝贝
assistant: [蒋贝贝]
design: 
issue: 
demoInfo:
  - title: 基础用法
    des: 基础用法
imports:
  # - './basic.tsx'
---


## API

事项关联涉及业务，暂不支持单独使用

### 一、参数说明：

[EB组件开发文档](https://weapp.eteams.cn/sp/techdoc/view/722818617793552384/726083063580172288)

参数与EB平台规范同步，主要涉及id、config、events等，其中config参数规范及抛出的事件说明如下：


#### 1.config 参数说明
| 参数         | 说明                                  | 类型                                  | 是否必填 | 默认值                                  |
| ------------ | ------------------------------------- | ------------------------------------- | -------- | --------------------------------------- |
| title | 标题 | string | 否 ||
| description | 描述 | string | 否 ||
| matterConfig | 事项范围设置 | AnyObj[] | 否 ||
| classificationSetting | 分类设置 | AnyObj | 否 ||
| mode | 是否平铺模式 | boolean | 否 ||
| saveMethod | 保存方式 | RadioValue | 否 ||
| hideLabel | 隐藏标题 | boolean | 否 ||
| labelPosition | 标题位置 | AnyObj | 否 ||


##### 1.1 特殊类型说明
```
 1.matterConfig
 // 数据组件统一管理
  [
    {
      "id": "calendar", // 事项id
      "name": "关联日程" // 事项名称
    },
  ]
 2.classificationSetting
{
  "id": "910083542763692033", // 事项关联组件分类id -- 涉及到业务，动态生成
  "enableClassification": true, // 是否启用分类
}

 3.saveMethod
 实时保存：realTime
 统一保存：oneTime

 4.labelPosition
 labelPosition = {
  pc: 'tb'
  m: 'lr'
 }
 上下: tb
 左右: lr

```

#### 2.config 继承了 @weapp/ebdcoms 仓库声明的CommonConfigData


### 二、事件说明
使用方式以低代码平台支持的能力为准

| 参数         | 说明                                  | 类型                                  | 是否必填 | 默认值                                  |
| ------------ | ------------------------------------- | ------------------------------------- | -------- | --------------------------------------- |
| save | 保存(一次性保存场景) | () => void | 否 ||