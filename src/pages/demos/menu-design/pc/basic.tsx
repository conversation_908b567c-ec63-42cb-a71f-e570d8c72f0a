import React, { useCallback, useState } from "react";
import { memo } from "react";
import { Layout, Icon, CodeMirror, Button, CorsComponent } from '@weapp/ui';
import { MenuDesignDataType } from '@weapp/ui-props-design';
import { DemoProps } from '../../../types';
import "../style/index.less";
import { corsImport } from "@weapp/utils";

const { Row, Col } = Layout;

const defaultData = [
  {
    "id": "requestRecycle",
    "content": "审批",
    "disable": false,
    "close": false,
    "children": [],
    "openReload": false,
  },
]
const configProps = {
  bindKey: "/project/link/2223824412136514433/5113824412260802669/mineproject_link_menu",
  designProps: {
    bindKey: "/project/link/2223824412136514433/5113824412260802669/mineproject_link_menu",
    /* 模块标记 */
    module: 'project/ebuilder',
    /* 事项id */
    matterId: '1',
    /* 事项筛选数据对应的值（以各业务为准） */
    names: '1',
  }
}
const menuDesignDemoClsPrefix = `ui-props-design-demo-menu-design`;
const MenuDesignDemo = memo(React.forwardRef<any, DemoProps>(
  (props, ref) => {
    const [datas, setDatas] = useState<MenuDesignDataType>(defaultData);
    const { onChange: onDesignChange } = props;
    const [code, setCode] = useState<string>('');

    const onChange = useCallback((value: MenuDesignDataType) => {
      setDatas(value);
      onDesignChange?.(value);
    }, [onDesignChange]);

    const onAdd = useCallback(() => {
      console.log('add');
    }, []);

    const onClose = useCallback(() => {
      console.log('close');
    }, []);

    const getCode = useCallback(() => {
      corsImport("@weapp/ui-props-design").then(({ visual }) => {
        const code = visual['Menu']?.customizeCode?.('', { ...configProps, data: datas });
        setCode(code);
      })
    }, [datas]);

    return (
      <>
        <Button weId={`${props.weId || ''}_ni53td`}>测试可视化</Button>
        <Row weId={`${props.weId || ''}_hrqp0e`} className={menuDesignDemoClsPrefix}>
          <Col weId={`${props.weId || ''}_pa5xzo`} span={12} className={`${menuDesignDemoClsPrefix}-left`}>
            <CorsComponent
              weId={`${props.weId || ''}_6ex2po`}
              app="@weapp/ui-props-design"
              compName="MenuDesign"
              value={datas}
              onChange={onChange}
              onAdd={onAdd}
              onClose={onClose}
              {...configProps}
            />
          </Col>
          <Col weId={`${props.weId || ''}_a1rmdk`} span={12} className={`${menuDesignDemoClsPrefix}-right`}>
            <div className={`${menuDesignDemoClsPrefix}-icon`}>
              <Icon weId={`${props.weId || ''}_ffhlw6`} name="Icon-Right-arrow02" onClick={getCode} size="lg" />
            </div>
            <div className={`${menuDesignDemoClsPrefix}-code-mirror`}>
              <CodeMirror weId={`${props.weId || ''}_2p0w9o`} readOnly value={code} />
            </div>
          </Col>
        </Row>
      </>
    )
  }
))

export default MenuDesignDemo;