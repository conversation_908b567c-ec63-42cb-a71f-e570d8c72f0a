---
key: MenuDesign
title: 菜单配置
type: com
person: 蒋贝贝
design: 
issue: 'http://10.12.101.12/FRONTEND/weapp-ui-props-design/-/issues?state=all&search=[MenuDesign]'
demoInfo:
  - title: 基础用法
    des: 基础用法
imports:
  - './basic.tsx'
---

## API

### 参数说明：

| 参数         | 说明                                  | 类型                                  | 是否必填 | 默认值                                  |
| ------------ | ------------------------------------- | ------------------------------------- | -------- | --------------------------------------- |
| value | 值 | MenuDesignDataType | ||
| onChange | 数据改变回调 | (value: MenuDesignDataType) => void | ||
