.ui-props-design-demo-menu-design {
  &-right {
    display: flex;
    align-items: center;
  }

  &-icon {
    padding: 0 var(--h-spacing-lg);
    cursor: pointer;

    .ui-icon {
      color: var(--base-white);
      background-color: var(--primary);
      border-radius: 50%;
      .ui-icon-svg {
        width: calc(30 * var(--hd));
        height: calc(30 * var(--hd));
      }
    }
  }
  &-code-mirror {
    width: calc(100% - var(--h-spacing-lg) - calc(30 * var(--hd)));
  }
}