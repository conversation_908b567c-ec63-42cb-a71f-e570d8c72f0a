---
key: Comment
title: 评论
type: com
person: 冯程
design: 
issue: 'http://10.12.101.12/FRONTEND/weapp-ui-props-design/-/issues?state=all&search=[Comment]'
demoInfo:
  - title: 已有页面引入
    des: 添加自从已有存在页面上的评论组件，需切换至对应租户才可以看到
  - title: 定义配置组件
    des: 通过View组件通过自定义配置获取评论组件
  - title: 获取上下文对象
    des: 通过onMount对象获取上下文
imports:
  - './ebViewCom.tsx'
  - './view.tsx'
  - './context.tsx'
---

## API

### 组件参数

| 参数            | 说明                                                      | 类型                                    | 是否必填 | 默认值             |
| --------------- | -------------------------------------------------------- | -------------------------------------- | ------- | ----------------- |
| config          | 组件配置                                                   | CommentConfig                         | 是     |                   |

### 组件配置(CommentConfig)参数说明：

| 参数            | 说明                                                      | 类型                                    | 是否必填 | 默认值             |
| --------------- | -------------------------------------------------------- | -------------------------------------- | ------- | ----------------- |
| targetId        | 评论id                                                   | string                                  | 是     |                   |
| actionConfig    | 文本框底部配置                                             | CommonConfigData[]                      | 否     |                   |
| optionConfig    | 回复操作框配置                                             | CommonConfigData[]                      | 否     |                   |
| hasEdit         | 是否展示文本框                                             | "show" \| "hidden"                      | 否     |                   |
| showText        | 输入框默认展开                                             | boolean                                  | 否     |                   |
| showTime        | 显示评论时间                                               | boolean                                  | 否     |                   |
| showEmpty       | 默认展示空状态                                             | boolean                                  | 否     |                   |
| showSource      | 显示评论来源                                               | boolean                                  | 否     |                   |
| showEditAvatar  | 输入框显示头像                                             | boolean                                  | 否     |                   |
| showListAvatar  | 列表显示头像                                               | number                                  | 否     |                   |
| pageSize        | 每页条数                                                   | boolean                                 | 否     |                   |
| printHorizontal | 打印时水平展示                                              | boolean                                 | 否     |                   |
| printHideInput  | 打印时隐藏评论输入框                                         | boolean                                 | 否     |                   |
| showTotal       | 显示总数                                                   | boolean                                 | 否     |                   |
| hasSort         | 启用排序                                                   | boolean                                 | 否     |                   |
| hasEchart       | 启用高级搜索                                                | boolean                                 | 否     |                   |
| hasBlog         | 启用统计                                                   | boolean                                 | 否     |                   |

#### CommonConfigData
| 参数            | 说明                                                      | 类型                                    | 是否必填 | 默认值             |
| --------------- | -------------------------------------------------------- | -------------------------------------- | ------- | ----------------- |
| id              | 按钮id                                                   | string                                  | 是     |                   |
| name            | 操作名称                                                 | string                           | 否     |                   |
| customName      | 自定义名称                                                 | string                         | 否     |                   |
| customMessage   | 自定义提示信息                                             | string                      | 否     |                   |
| iconName        | 按钮名称                                                 | string                                      | 否     |                   |
| enable          | 是否启用                                                 | string                                     | 否     |                   |
| showName        | 是否显示名称                                               | boolean                                  |  否     |                   |

### 上下文对象参数说明
| 属性             | 说明                                                      | 类型                                    |
| --------------- | -------------------------------------------------------- | -------------------------------------- |
| contextValue    | 数据store                                                  | any                                  |
| commentRef      | 评论组件ref对象                                             | string                                 |

### 自定义事件
暂无