import { CorsComponent } from '@weapp/ui';
import React from 'react';

const data = {
  "id": "f99175229f6c4aca87c56551b7eda935",
  "pid": "",
  "refCompId": "f828e871d99548178b42de27f0d34032",
  "type": "Comment",
  "category": "4",
  "refModule": "",
  "tenantKey": "tbgxf1lxbf",
  "applyStyle": "",
  "config": {
      "name": "评论",
      "type": "Comment",
      "layout": {
          "i": "eb4b80ca1eda4be99aea3e4bdfff4926",
          "x": 0,
          "y": 0,
          "w": 12,
          "h": 36,
          "scope": "",
          "moved": false,
          "selected": true,
          "offsetTop": 56
      },
      "commentTitle": "评论",
      "title": "评论",
      "showEmpty": true,
      "hasEdit": "show",
      "showEditAvatar": true,
      "showListAvatar": true,
      "pageSize": "10",
      "showSource": true,
      "showTime": true,
      "moduleValue": "bcw",
      "titleEnabled": true,
      "flow": {},
      "disabled": {},
      "targetId": "911188427422097409"
  },
  "package": "@weapp/ui-props-design"
}

export default class EBComView extends React.PureComponent {

  commentContext = null;

  onMount = (context: any) => {
    console.log(context)
    this.commentContext = context;
  }

  render() {
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_je3eyn`}
        app="@weapp/designer"
        compName="EBComView"
        data={data}
        onMount={this.onMount}
      />
    );
  }
}