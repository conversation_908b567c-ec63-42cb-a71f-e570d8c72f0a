import React, { useCallback } from "react";
import { memo } from "react";
import { DemoProps } from '../../../types';
import { CorsComponent } from "@weapp/ui";

const CommonSettingDemo = memo(React.forwardRef<any, DemoProps>(
  (props, ref) => {

    const customRender = useCallback(({ CommonSetting }) => {
      return (
        <CommonSetting
          weId={`${props.weId || ''}_ibc4g8`}
          compName="BrowserPanel"
        />
      )
    }, [])

    return (
      <>
        <h3>集成EB组件 Config配置，以BrowserPanel为例</h3>
        <CorsComponent
          weId={`${props.weId || ''}_uydep2`}
          customRender={customRender}
          app="@weapp/ui-props-design"
        />
      </>
    )
  }
))

export default CommonSettingDemo;