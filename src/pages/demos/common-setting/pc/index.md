---
key: CommonSetting
title: Config组件集合
type: com
person: 蒋贝贝
design: 
issue: 'http://10.12.101.12/FRONTEND/weapp-ui-props-design/-/issues?state=all&search=[CommonSetting]'
demoInfo:
  - title: EB组件对应的Config
    des: EB组件对应的Config
  - title: 注解生成配置组件
    des: 注解生成配置组件
imports:
  - './basic.tsx'
  - './schema.tsx'
---

## API

### 参数说明：

| 参数         | 说明                                  | 类型                                  | 是否必填 | 默认值                                  |
| ------------ | ------------------------------------- | ------------------------------------- | -------- | --------------------------------------- |
| compName | 组件名 | string | 是 ||
| app | 包名 | string | 否 | @weapp/ui-props-design |
| value | 设置项的值 | AnyObj | 否 |  |
| onChange | 配置改变回调事件 | (value: Data, otherParams?: AnyObj) => void | 否 |  |
| fields | 过滤配置展示的字段(仅type='schema'支持) | string[] | 否 |  |
| type |配置组件类型 | 'schema' \| 'ebConfig' | 否 |  |
| store |EB组件对应的Config配置自定义store(仅type='ebConfig'支持) | CompDesignStoreType & AnyObj | 否 |  |

