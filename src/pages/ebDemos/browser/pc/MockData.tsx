import { request, RequestOptionConfig } from '@weapp/utils';
import { <PERSON><PERSON><PERSON>, BrowserTabsData, BrowserValueType, FormLayoutType, FormItemProps, AnyObj } from '@weapp/ui';
import { Component } from 'react';
import { withRouter } from 'react-router-dom';

// 列表统计总数
export const mockListTotal = 1000;

export const mockCompleteData = ('0,'.repeat(50).split(',')).map((v, i) => ({
  id: `id_${i}`,
  user: `用户_${i}`,
  time: `09-0${i}`,
  content: i % 3 === 0 ? `超长显示的标题超长显示的标题超长显示的标题超长显示的标题超长显示的标题(${i})` : i % 2 === 0 ? `中等长度的标题(${i})` : `选项(${i})`,
  icon: 'Icon-personnel',
  avatar: "{\"name\":\"A吕益\",\"url\":null}",
}));

export function mockListData(current: number, pageSize: number, type?: string, icon: string = '', url: string = '') {
  const newData = [];
  const min = pageSize * (current - 1);
  const max = pageSize * current;
  for (let i = min; i < max && i < mockListTotal; i++) {
    // const data: AnyObj = {
    //   order: i,
    //   id: `id_${i}`,
    //   user: `用户_${i}`,
    //   time: `09-0${i}`,
    //   content: i % 3 === 0 ? `超长显示的标题超长显示的标题超长显示的标题超长显示的标题超长显示的标题(${i})` : i % 2 === 0 ? `中等长度的标题(${i})` : `选项_(${i})`,
    //   linkUrl: '/abc/123?id=',
    // };
    let data: AnyObj = {};
    if (/resource/.test(url)) {  // 人员
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `用户_${i}`
        },
      }
    } else if (/department/.test(url)) {
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-department03"
      }
    } else if (/subcompany/.test(url)) {
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-headquarters02"
      }
    } else if (/role/.test(url)) {
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-role"
      }
    } else if (/channel/.test(url)) {
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `测试_${i}`,
          "isGroup": true
        },
      }
    } else if (/position/.test(url)) {
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-post02"
      }
    } else if (/external/.test(url)) {
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `用户_${i}`
        },
      }
    } else {
      data = {
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `用户_${i}`
        },
      }
    }
    if (icon) {
      if (icon !== 'none') {
        data.icon = icon;
      } else {
        data.children = [
          {
            id: 'condition',
            content: '筛选条件',
            type: 'detail',
            children: [
              { id: '1', content: '筛选条件1' },
              { id: '2', content: '筛选条件2' },
              { id: '3', content: '筛选条件3' },
            ],
          },
          {
            id: 'count',
            content: '自定义统计',
            type: 'detail',
            children: [
              { id: '1', content: '条件1' },
              { id: '2', content: '条件2' },
              { id: '3', content: '条件3' },
            ],
          },
        ];
      }
    } else if (!data?.icon) {
      data.avatar = "{\"name\":\"A吕益\",\"url\":null}";
    }
    if (type) {
      data.type = type;
    }
    newData.push(data);
  }
  return newData;
}

export function mockTreeNodeData(level: number, length: number, allLevel: number, parentId?: string, isStopDept?: boolean) {
  const treeNodes: AnyObj[] = [];
  if (level > allLevel) return undefined;
  const isLeaf = level === allLevel;
  for (let i = 0; i < length; i++) {
    const id = `${parentId || 'root'}_${level}_${i}`;
    const node: AnyObj = {
      id,
      isLeaf,
      disabledCheck: level === 1,
      user: `用户_${i}`,
      time: `09-0${i}`,
      icon: 'Icon-personnel',
      content: `节点${level}-${i}`,
    };
    if (!isLeaf) {
      node.children = mockTreeNodeData(level + 1, length, allLevel, id);
    } else {
      node.avatar = "{\"name\":\"A吕益\",\"url\":null}";
    }
    if (isStopDept && i % 5 === 0) {
      node.isStopDepartment = true;
      node.icon = 'Icon-Display-disabled02';
    }
    treeNodes.push(node);
  }
  return treeNodes;
}

export function mockAsyncTreeData(length: number = 5, parentId?: string) {
  const treeNodes: AnyObj[] = [];
  for (let i = 0; i < length; i++) {
    const id = `${parentId || 'root'}_${i}`;
    const node: AnyObj = {
      id,
      isLeaf: false,
      content: `节点-${i}`,
      icon: 'Icon-personnel',
    };
    treeNodes.push(node);
  }
  return treeNodes;
}

export const mockHistoryCompleteData = mockCompleteData.slice(0, 10);

// ================== 模拟列表的显示模板 ==============
// 基础 JSON 模板
export const mockJSONTemplate = {
  default: [
    // 第一列
    {
      "key": "col1",
      "configs": [
        {
          "key": "col1_row1", // 第一列 > 第一行
          "configs": [
            // 第一列 > 第一行 > 第一个元素 | 优先取值 data.content
            { "key": "content" },

          ],
        },
        {
          "key": "col1_row2", // 第一列 > 第二行
          "configs": [
            // 第一列 > 第二行 > 第一个元素 | 优先取值 data.time
            { "key": "user" },
            // 第一列 > 第二行 > 第二个元素 | 优先取值 data.user
            { "key": "time" },
          ]
        }
      ]
    },
  ]
};

// 前端内置模板
export const mockDefaultTemplate = {
  default: {
    theme: 'col1_row2',
    dataKeys: ['content', 'user', 'time'],
  },
};

// 字符串模板
export const mockStringTemplate = {
  default: "<div style=\"padding: 0 5px\"><div style=\"color: #333; font-size: 14px\">#{content}</div><div style=\"color: #999; font-size: 12px\"><span style=\"color: green; margin-right: 5px\">#{user}</span><span>#{time}</span></div></div>",
};

export const mockTemplate = mockDefaultTemplate;

// 模拟表格的显示列配置
export const mockColumns = [
  {
    title: '标题',
    dataIndex: 'content',
    orderKey: 'content',
    orderType: '',
  }, {
    title: '用户',
    dataIndex: 'user',
    orderKey: 'user',
    orderType: '',
  }, {
    title: '日期',
    dataIndex: 'time',
    orderKey: 'time',
    orderType: '',
  }
];

// 模拟树形是同步还是异步
export const mockTreeAsync = true;

// =============== 模拟高级搜索数据 ===================
export const formGroupData = [
  { id: 'g1', title: '常用条件', custom: false },
  { id: 'g2', title: '其他条件', custom: true },
];

export const formItems: FormItemProps = {
  'input': {
    itemType: "INPUT",
  },
  'inputProp': {
    itemType: "SELECT",
    data: [
      { id: 'contain', content: '包含' },
      { id: 'ncontain', content: '不包含' },
    ],
  },
  'select': {
    itemType: "SELECT",
    data: [
      { id: 'op1', content: '选项一' },
      { id: 'op2', content: '选项二' },
    ]
  },
  'text': {
    itemType: 'TEXTAREA',
  },
  'input1': {
    itemType: "INPUT",
  },
  'input2': {
    itemType: "INPUT",
  },
  'input3': {
    itemType: "INPUT",
  },
}

export const formLayout: Array<FormLayoutType> = [
  [
    { id: '1', label: '单行文本', items: ['inputProp', 'input'], labelSpan: 6, groupId: 'g1', hide: false, },
    { id: '2', label: '下拉选择框', items: ['select'], labelSpan: 6, groupId: 'g1', hide: false, },
  ],
  [
    { id: '3', label: '多行文本', items: ['text'], labelSpan: 3, groupId: 'g1', hide: false, },
  ],
  [
    { id: '4', label: '其他条件1', items: ['input1'], labelSpan: 6, groupId: 'g2', hide: true, },
    { id: '5', label: '其他条件2', items: ['input2'], labelSpan: 6, groupId: 'g2', hide: true, },
    { id: '6', label: '其他条件3', items: ['input3'], labelSpan: 6, groupId: 'g2', hide: true, },
  ],
];

// ============== 模拟浏览按钮页签数据 ===================
export const resourceTabs: BrowserTabsData[] = [
  { id: '1', content: '最近', browserProps: { dataParams: { cmd: 'list' }, canSelectAllUser: true } },
  { id: '2', content: '组织架构', browserProps: { dataParams: { cmd: 'list' }, leftDataParams: { cmd: 'org-tree' }, hasLeftData: true, leftContentLabel: '组织', hasAdvanceSearch: true } },
  { id: '3', content: '常用组', browserProps: { dataParams: { cmd: 'list' }, leftDataParams: { cmd: 'group' }, disabledTabCache: true, hasLeftData: true, hasAdvanceSearch: true } },
  { id: '4', content: '同部门', browserProps: { dataParams: { cmd: 'list' }, canSelectAllUser: true, hasAdvanceSearch: false, disabledTabCache: true } },
  // { id: '5', content: '所有人', browserProps: { dataParams: { cmd: 'all' }, canSelectAllUser: true } }
];

// 人员组织多选
export const orgMultTabs: BrowserTabsData[] = [
  { id: '1', content: '最近', hide: true, icon: 'Icon-Male-head-portrait-o', browserProps: { dataParams: { cmd: 'list' } } },
  { id: '2', content: '通讯录', icon: 'Icon-Male-head-portrait-o', browserProps: { dataParams: { cmd: 'tree' } } },
  { id: '3', content: '组织架构', icon: 'Icon-Male-head-portrait-o', browserProps: { dataParams: { cmd: 'tree' }, } },
  { id: '4', content: '群聊', icon: 'Icon-Male-head-portrait-o', browserProps: { dataParams: { cmd: 'tree' } } },
  { id: '5', content: '外部联系人', icon: 'Icon-Male-head-portrait-o', browserProps: { dataParams: { cmd: 'list' } } },
];

export const resourceAssoProps = { placeholder: '请输入人员姓名' };

export const orgTabs: BrowserTabsData[] = [
  { id: '2', content: '按列表', browserProps: { dataParams: { cmd: 'list' }, showAccount: true, canSelectAllUser: true }, icon: 'Icon-folder' },
  { id: '1', content: '按组织架构', browserProps: { dataParams: { cmd: 'tree' }, browserTreeProps: { async: true } }, icon: 'Icon-folder' },
];

export const taskMenu: BrowserTabsData[] = [
  {
    id: 'myTask', content: '我的任务', children: [
      { id: 'task-1', content: '负责和参与的任务' },
      { id: 'task-2', content: '负责的任务' },
      { id: 'task-3', content: '参与的任务' },
    ]
  },
  { id: 'focusTask', content: '关注的任务' },
  { id: 'subTask', content: '下属任务' },
  { id: 'allTask', content: '全部任务' },
];


export const deptAssoProps = { placeholder: '请输入部门' };

export const deptDialogProps = { title: '选择部门' };

export const singleDialogProps = { width: 420 };

// ============ 模拟不同返回数据类型的请求参数 ================
export const listDataParams = { cmd: 'list' };
export const treeDataParams = { cmd: 'tree', async: mockTreeAsync };
export const tableDataParams = { cmd: 'table' };
export const cardDataParams = { cmd: 'card-list' };
export const tagDataParams = { cmd: 'tag-list' };
export const indexDataParams = { cmd: 'index-list' };
export const multTypeParams = { cmd: 'mult-type' };
export const conditionListDataParams = { cmd: 'condition-list' };
export const groupDataParams = { cmd: 'group' };

export class MockDataBrowserBase extends Component {
  // 模拟生成树形数据

  generateTreeData = (level: number = 3, length: number = 10, stopDept?: boolean) => {
    return mockTreeNodeData(1, length, level, undefined, stopDept);
  }

  mockDataUrl = (options: RequestOptionConfig) => {
    let resolveData: AnyObj | undefined;
    // 模拟请求data接口数据
    const { data: { pageSize, current, cmd, orderKey, orderType, stopDepartment } } = options;
    if (cmd === 'list') {
      const newData = mockListData(current, pageSize, undefined, undefined, options.url);
      if (orderKey) {
        newData.sort((a: any, b: any) => {
          if (orderType === 'asc') {
            return Number(b[orderKey]) - Number(a[orderKey]);
          } else {
            return Number(a[orderKey]) - Number(b[orderKey]);
          }
        });
      }
      resolveData = {
        dataType: cmd,
        data: newData,
        // dropdowns: [{
        //   key: 'companyId', // 下拉框搜索参数
        //   defaultValue: '2', // 默认值
        //   data: [ // 下拉数据
        //     { id: '1', content: '行政组织' },
        //     { id: '2', content: '公文交换纬度' },
        //     { id: '3', content: '客户纬度' },
        //     { id: '4', content: 'PMO纬度' },
        //   ]
        // }],
        // virtualOrg: [
        //   { id: '1', content: '行政组织' },
        //   { id: '2', content: '公文交换纬度' },
        //   { id: '3', content: '客户纬度' },
        //   { id: '4', content: 'PMO纬度' },
        // ],
        // groupData: [
        //   { id: '1', content: '公共组' },
        //   { id: '2', content: '私人组' },
        //   { id: '3', content: '群聊' },
        // ],
        // subordinateData: [
        //   { id: '1', content: '直接下属' },
        //   { id: '2', content: '间接下属' },
        //   { id: '3', content: '所有下属' },
        // ],
      };
      if (cmd === 'list') {
        resolveData.paginationType = 'scrolling';
        resolveData.template = mockTemplate;
        resolveData.columns = mockColumns;
      }
    } else if (cmd === 'tree') {
      const { data: { id, async } } = options;
      if (id === undefined) {
        // 初次加载
        resolveData = {
          dataType: cmd,
          data: [{
            id: 'root',
            content: '根节点',
            disabledCheck: true,
            icon: 'Icon-personnel',
            children: true ? mockAsyncTreeData(10, 'root') : this.generateTreeData(undefined, undefined, stopDepartment)
          }]
        };
      } else {
        resolveData = {
          dataType: cmd,
          data: mockAsyncTreeData(10, id)
        };
      }
    } else if (cmd === 'table') {
      const newData = mockListData(current, pageSize, undefined, undefined, options.url);
      resolveData = {
        dataType: cmd,
        data: newData,
        // total: 0,
        columns: mockColumns,
      };
    } else if (cmd === 'card-list') {
      const newData = [];
      const min = pageSize * (current - 1);
      const max = pageSize * current;
      for (let i = min; i < max && i < mockListTotal; i++) {
        if (i === min + 1) {
          newData.push({
            id: `id_${i}`,
            content: `会议室_${i}`,
            description: `会议室_${i} 09-0${i}`,
            alt: `会议室(${i})`,
            tag: '空闲空闲空闲空闲',
            tagType: '#FFCD50'
          });
        } else {
          newData.push({
            id: `id_${i}`,
            content: `会议室_${i}`,
            description: `会议室_${i} 09-0${i}`,
            alt: `会议室(${i})`,
            tag: '已占用已占用已占用'
          });
        }
      }
      resolveData = {
        dataType: cmd,
        data: newData,
      };
    } else if (cmd === 'tag-list') {
      const newData = [];
      const min = pageSize * (current - 1);
      const max = pageSize * current;
      const colors = ['#5d9cec', '#52c41a', '#faad14', '#ff4d4f', '#68a5ff'];
      for (let i = min; i < max && i < mockListTotal; i++) {
        newData.push({
          id: `id_${i}`,
          content: `标签_${i}`,
          color: colors[i % 6]
        });
      }
      resolveData = {
        dataType: cmd,
        data: newData,
      };
    } else if (cmd === 'index-list') {
      const newData = [];
      const indexList = [
        { id: 'A', content: 'A' },
        { id: 'B', content: 'B' },
        { id: 'C', content: 'C' },
        { id: 'D', content: 'D' },
        { id: 'E', content: 'E' },
        { id: 'F', content: 'F' },
        { id: 'G', content: 'G' },
        { id: 'H', content: 'H' },
        { id: 'I', content: 'I' },
        { id: 'J', content: 'J' },
        { id: 'K', content: 'K' },
        { id: 'L', content: 'L' },
        // { id: 'M', content: 'M' },
        // { id: 'N', content: 'N' },
        // { id: 'O', content: 'O' },
        // { id: 'P', content: 'P' },
        // { id: 'Q', content: 'Q' },
        // { id: 'R', content: 'R' },
        // { id: 'S', content: 'S' },
        // { id: 'T', content: 'T' },
        // { id: 'U', content: 'U' },
        // { id: 'V', content: 'V' },
        // { id: 'W', content: 'W' },
        // { id: 'X', content: 'X' },
        // { id: 'Y', content: 'Y' },
        // { id: 'Z', content: 'Z' },
      ];
      const min = pageSize * (current - 1);
      const max = pageSize * current;
      const currentIndex = current > 5 ? 5 : current;
      const currentType = indexList.slice(2 * (currentIndex - 1), 2 * currentIndex);
      for (let i = min; i < max && i < mockListTotal; i++) {
        newData.push({
          id: `id_${i}`,
          user: `用户_${i}`,
          time: `09-0${i}`,
          content: `选项(${i})`,
          avatar: "{\"name\":\"A吕益\",\"url\":null}",
          indexType: currentType[i % 2].id, // 对应索引的id
        });
      }
      resolveData = {
        dataType: cmd,
        total: 0, // 滚动分页
        data: newData,
        template: mockTemplate,
        indexList: indexList
      };
    } else if (cmd === 'condition-list') {
      const newData = mockListData(current, pageSize, undefined, 'none', options.url);
      resolveData = {
        dataType: cmd,
        data: newData,
        total: 0,
      };
    }
    if (resolveData) {
      return new Promise<any>((resolve, reject) => {
        setTimeout(() => {
          resolve({
            code: 200,
            msg: "接口返回成功",
            status: true,
            data: resolveData
          });
        }, 300);
      });
    }
    return request(options);
  }

  mockDataCountUrl = (options: RequestOptionConfig) => {
    // 模拟请求count接口数据
    return new Promise<any>((resolve, reject) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: {
            total: mockListTotal
          }
        });
      }, 300);
    });
  }

  mockDataConditionUrl = (options: RequestOptionConfig) => {
    return new Promise<any>((resolve, reject) => {
      setTimeout(() => {
        const mockDataCondition: any[] = [
          {
            id: 'condition',
            content: '筛选条件',
            // children: [
            //   { id: '1', content: '筛选条件1' },
            //   { id: '2', content: '筛选条件2' },
            //   { id: '3', content: '筛选条件3' },
            // ],
          },
          {
            id: 'count',
            content: '自定义统计',
            children: [
              { id: '1', content: '条件1' },
              { id: '2', content: '条件2' },
              { id: '3', content: '条件3' },
            ],
          },
        ];
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: {
            data: mockDataCondition
          }
        });
      }, 300);
    });
  }

  mockDestDataUrl = (options: RequestOptionConfig) => {
    const { data: { cmd, types, selectedids } } = options;
    console.log(types, selectedids);
    // 模拟返回已选接口数据
    const resolveData = {
      template: {
        default: {
          theme: 'col1_row2',
          dataKeys: ['content', 'user', 'time'],
        },
      },
      searchKeys: 'content,user,time'
    };
    if (cmd === 'condition-list') {
      resolveData.template = {
        default: {
          theme: 'col1_row2',
          dataKeys: ['content', 'detailContent'],
        },
      }
    }
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: resolveData
        });
      }, 300);
    });
  }

  mockCompleteUrl = (options: RequestOptionConfig) => {
    if (!options.url) {
      return new Promise<any>((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            msg: "接口返回成功",
            status: false,
            data: []
          });
        }, 200);
      });
    };
    // 模拟返回联想搜索接口数据
    const { data: { searchValue, newly } } = options;
    let result: any[] = [];
    if (/resource/.test(options.url)) {  // 人员
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `用户_${i}`
        },
      }));
    } else if (/department/.test(options.url)) {
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-department03"
      }));
    } else if (/subcompany/.test(options.url)) {
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-headquarters02"
      }));
    } else if (/role/.test(options.url)) {
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-role"
      }));
    } else if (/channel/.test(options.url)) {
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `测试_${i}`,
          "isGroup": true
        },
      }));
    } else if (/position/.test(options.url)) {
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        icon: "Icon-post02"
      }));
    } else if (/external/.test(options.url)) {
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `用户_${i}`
        },
      }));
    } else {
      result = ('0,'.repeat(50).split(',')).map((v, i) => ({
        id: `id_${i}`,
        user: `用户_${i}`,
        time: `09-0${i}`,
        content: `选项(${i})`,
        "avatar": {
          "previewPath": "/api/file/preview?type=redirect&fileId=",
          "appPreviewPath": "/api/app/file/preview?type=redirect&fileId=",
          "name": `用户_${i}`
        },
      }));
    }
    if (newly) {
      // 获取最近的十条数据
      result = result.slice(0, 10);
    } else {
      const patt = new RegExp(searchValue, 'i');
      result = result.filter(data => patt.test(data.content));
    }
    const resolveData = {
      data: result,
      // template: mockTemplate
      // template: {
      //   "default": {
      //     "dataKeys": [
      //       "content"
      //     ],
      //     "theme": "col1_row1"
      //   }
      // },
    };
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: resolveData
        });
      }, 300);
    });
  }

  mockAddDataUrl = (options: RequestOptionConfig) => {
    const { data: { addValue } } = options;
    const resolveData = {
      data: {
        id: `id_${addValue}`,
        user: `用户_${addValue}`,
        time: `09-0${addValue}`,
        content: `选项(${addValue})`,
        icon: 'Icon-personnel',
      }
    };
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: resolveData
        });
      }, 200);
    });
  }

  mockConditionUrl = (options: RequestOptionConfig) => {
    // 模拟返回高级搜索接口数据
    const resolveData = {
      conditionId: '123',
      data: {},
      layout: formLayout,
      items: formItems,
      groups: formGroupData
    };
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: resolveData
        });
      }, 300);
    });
  }

  mockLeftDataUrl = (options: RequestOptionConfig) => {
    let resolveData: object | undefined = {};
    const { data: { cmd, pageSize, current } } = options;
    if (cmd === 'org-tree') {
      const { data: { id } } = options;
      if (id === undefined) {
        // 初次加载
        resolveData = {
          dataType: 'tree',
          data: [{
            id: 'root',
            content: '根节点',
            disabled: true,
            icon: 'Icon-personnel',
            children: mockAsyncTreeData(5, 'root')
          }]
        };
      } else {
        resolveData = {
          dataType: 'tree',
          data: mockAsyncTreeData(10, id)
        };
      }
    } else if (cmd === 'group') {
      const newData = [];
      const min = pageSize * (current - 1);
      const max = pageSize * current;
      for (let i = min; i < max && i < mockListTotal; i++) {
        newData.push({
          id: `id_${i}`,
          user: `用户_${i}`,
          time: `09-0${i}`,
          content: `选项(${i})`,
          order: i,
          icon: 'Icon-personnel',
        });
      }
      resolveData = {
        dataType: 'list',
        data: newData,
        template: mockTemplate,
        // dropdowns: [{
        //   key: 'leftCompanyId',
        //   defaultValue: '2',
        //   data: [
        //     { id: '1', content: '行政组织' },
        //     { id: '2', content: '公文交换纬度' },
        //     { id: '3', content: '客户纬度' },
        //     { id: '4', content: 'PMO纬度' },
        //   ]
        // }],
        // leftVirtualOrg: [
        //   { id: '1', content: '行政组织' },
        //   { id: '2', content: '公文交换纬度' },
        //   { id: '3', content: '客户纬度' },
        //   { id: '4', content: 'PMO纬度' },
        // ],
        // leftGroupData: [
        //   { id: '1', content: '公共组' },
        //   { id: '2', content: '私人组' },
        //   { id: '3', content: '群聊' },
        // ],
        // leftSubordinateData: [
        //   { id: '1', content: '直接下属' },
        //   { id: '2', content: '间接下属' },
        //   { id: '3', content: '所有下属' },
        // ],
      };
    }
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: resolveData
        });
      }, 300);
    });
  }

  mockPropDataUrl = (options: RequestOptionConfig) => {
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: {
            dataParams: {
              propDefaultProps: 1,
            },
            hasAdvanceSearch: true,
            browserListProps: {
              pageSize: 20,
            },
            browserDialogProps: {
              title: '测试标题',
              width: 700,
            },
          }
        });
      }, 300);
    });
  }

  mockHistoryDataUrl = (options: RequestOptionConfig) => {
    return new Promise<any>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          msg: "接口返回成功",
          status: true,
          data: {}
        });
      }, 300);
    });
  }

  mockFetchData = (options: RequestOptionConfig) => {
    const url = options.url as string;
    console.log('fetch', url, options);
    if (/\/common\/browser\/data\//.test(url)) {
      return this.mockDataUrl(options);
    } else if (/\/common\/browser\/count/.test(url)) {
      return this.mockDataCountUrl(options);
    } else if (/\/common\/browser\/dataCondition/.test(url)) {
      return this.mockDataConditionUrl(options);
    } else if (/\/common\/browser\/destData/.test(url)) {
      return this.mockDestDataUrl(options);
    } else if (/\/common\/browser\/complete/.test(url)) {
      return this.mockCompleteUrl(options);
    } else if (/\/common\/browser\/addData/.test(url)) {
      return this.mockAddDataUrl(options);
    } else if (/\/common\/browser\/condition/.test(url)) {
      return this.mockConditionUrl(options);
    } else if (/\/common\/browser\/leftData/.test(url)) {
      return this.mockLeftDataUrl(options);
    } else if (/\/common\/browser\/prop/.test(url)) {
      return this.mockPropDataUrl(options);
    } else if (/\/common\/browser\/history/.test(url)) {
      return this.mockHistoryDataUrl(options);
    };
    // 无需处理时直接使用request处理，必须处理否则会影响接口正常执行
    return request(options);
  }
  handleChange = (data: BrowserValueType) => {
    console.log('data change', data);
  }
}

// @ts-ignore
@withRouter
export default class MockDataBrowser extends MockDataBrowserBase {
  render() {
    return (
      <div>
        <h4>单选</h4>
        <Browser weId={`${this.props.weId || ''}_dw6bvw`}
          type="demo"
          module="common"
          hasAdvanceSearch={false}
          dataParams={listDataParams}
          fetchData={this.mockFetchData}
          panelHeight={450}
          browserDialogProps={singleDialogProps}
          onChange={this.handleChange}
        />
        <h4>多选</h4>
        <Browser weId={`${this.props.weId || ''}_qvmz1s`}
          type="demo"
          module="common"
          multiple
          hasAdvanceSearch
          dataParams={listDataParams}
          fetchData={this.mockFetchData}
          onChange={this.handleChange}
        />
      </div>
    )
  }
}