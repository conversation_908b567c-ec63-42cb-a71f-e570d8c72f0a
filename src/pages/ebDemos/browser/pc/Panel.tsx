import { Browser, BrowserValueType } from '@weapp/ui';
import {
  MockDataBrowserBase,
  treeDataParams,
} from './MockData';

const { BrowserPanel } = Browser;

class BrowserDemo extends MockDataBrowserBase {
  state = {
    selectedData: [],
    leftTreeData: [],
  }
  handleChange = (data: BrowserValueType) => {
    this.setState({ selectedData: data });
    console.log('data change', data);
  }
  handleTabChange = (activeKey: string) => {
    console.log('tab change', activeKey);
  }
  handleLeftTreeChange = (data: BrowserValueType) => {
    this.setState({ leftTreeData: data });
  }
  render() {
    const { selectedData } = this.state;
    return (
      <>
        <BrowserPanel
          weId={`${this.props.weId || ''}_8ryusc`}
          type="demo"
          module="common"
          value={selectedData}
          panelHeight={400}
          dataParams={treeDataParams}
          fetchData={this.mockFetchData}
          onChange={this.handleChange}
        >
        </BrowserPanel>
      </>
    );
  }
}

export default BrowserDemo;
