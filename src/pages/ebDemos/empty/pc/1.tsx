import { Component } from "react";
import { Empty, Icon, constants } from "@weapp/ui";

const prefixCls = `${constants.uiPcClsPrefix}-demo-empty`;

const imageStyle = { width: 100, height: 100 };
export default class EmptyDemo extends Component {

  render() {
    return (
      <div className={prefixCls}>
        <Empty weId={`${this.props.weId || ''}_u731c7`}
          title={'暂无待办审批'}
          description='您暂时还没有待办流程，可以主动发起一条试试哦~'
          image={
            <Icon weId={`${this.props.weId || ''}_3g82pq`}
              style={imageStyle}
              name={'Icon-empty-file'
              } />
          }
        />
      </div>
    )
  }
}
