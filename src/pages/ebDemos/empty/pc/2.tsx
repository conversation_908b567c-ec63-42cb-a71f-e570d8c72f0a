import { Component } from "react";
import { Empty, Button, constants } from "@weapp/ui";

const prefixCls = `${constants.uiPcClsPrefix}-demo-empty`;
const imageStyle = { height: 100 };

export default class EmptyDemo extends Component {
  renderFooter = () => <Button weId={`${this.props.weId || ''}_pq31ck`} type={'primary'}>新建审批</Button>

  render() {
    return (
      <div className={prefixCls}>
        <Empty weId={`${this.props.weId || ''}_9c1bph`}
          description={
            <span>
              Customize  <a href="#API">Description</a>
            </span>
          }
          image={'https://img95.699pic.com/photo/50055/5642.jpg_wh300.jpg'}
          imageStyle={imageStyle}
          renderFooter={this.renderFooter}
        />
      </div>
    )
  }
}
