---
key: Empty
title: 空状态
person: 蒋贝贝
demoInfo:
  - title: 基础用法
    des: 引用对应的icon图标,可自行添加标题和描述.
  - title: 自定义图片
    des: 自定义图片链接,图片大小等.
  - title: error类型
    des: 可修改 displayType 来展示 error 类型
imports:
  - "./1.tsx"
  - "./2.tsx"
  - "./3.tsx"
---

## API

### 参数说明如下：

| 参数         | 说明                                           | 类型                                             | 是否必填 | 默认值 |
| ------------ | ---------------------------------------------- | ------------------------------------------------ | -------- | ------ |
| title        | 主要文字描述                                   | ReactNode                                        | 否       |        |
| description  | 次要文字描述                                   | ReactNode                                        | 否       |        |
| image        | 设置显示图片，为 string 时表示自定义图片地址。 | ReactNode                                        | 否       |        |
| imageStyle   | 图片样式                                       | CSSProperties                                    | 否       |        |
| style        | style 样式                                     | CSSProperties                                    | 否       |        |
| renderFooter | 自定义底部内容                                 | ()=>ReactNode                                    | 否       |        |
| displayType  | 展示类型                                       | ’404‘                                            | 否       |        |
| customRender | 自定义渲染                                     | (_: any, ele: React.ReactNode) =>React.ReactNode | 否       |        |

[详情查看Empty组件文档](/ui/empty)
