import React from "react";
import { constants, Help, Layout } from "@weapp/ui";

const { helpClsPrefix } = constants;
const { Row, Col } = Layout;

const prefixCls = `${helpClsPrefix}-demo`;

export default class HelpDemo extends React.PureComponent {
  render() {
    const title = "在这里写入相应的提示信息在这里写入相应的提示信息在这里写入相应的提示信息在这里写入相应的提示信息";
    return (
      <div className={prefixCls}>
        <Row weId={`${this.props.weId || ''}_759ddg`}>
          <Col weId={`${this.props.weId || ''}_a6j7yp`} span={6}>
            <div className={`${prefixCls}-line`}>
              提示位置--上左：
              <Help weId={`${this.props.weId || ''}_7c93q6`} title={title} placement="topLeft" />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--上边：
              <Help weId={`${this.props.weId || ''}_rxmhp2`} title={title} placement="top" />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--上右：
              <Help weId={`${this.props.weId || ''}_0mqp8g`} title={title} placement="topRight" width={150} />
            </div>
          </Col>
          <Col weId={`${this.props.weId || ''}_tqms6r`} span={6}>
            <div className={`${prefixCls}-line`}>
              提示位置--下左：
              <Help weId={`${this.props.weId || ''}_3ahu5p`} title={title} placement="bottomLeft" />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--下边：
              <Help weId={`${this.props.weId || ''}_wusmym`} title={title} placement="bottom" />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--下右：
              <Help weId={`${this.props.weId || ''}_yd6tud`} title={title} placement="bottomRight" />
            </div>
          </Col>
          <Col weId={`${this.props.weId || ''}_ydyzcz`} span={6}>
            <div className={`${prefixCls}-line`}>
              提示位置--左上：
              <Help weId={`${this.props.weId || ''}_tytbi1`} title={title} placement="leftTop" />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--左边：
              <Help weId={`${this.props.weId || ''}_93h5ra`} title={title} placement="left" />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--左下：
              <Help weId={`${this.props.weId || ''}_r9ev0w`} title={title} placement="leftBottom" />
            </div>
          </Col>
          <Col weId={`${this.props.weId || ''}_i4h7jo`} span={6}>
            <div className={`${prefixCls}-line`}>
              提示位置--右上：
              <Help weId={`${this.props.weId || ''}_hmn9q7`} title={title} placement="rightTop" width={150} />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--右边：
              <Help weId={`${this.props.weId || ''}_d2azs9`} title={title} placement="right" width={150} />
            </div>
            <div className={`${prefixCls}-line`}>
              提示位置--右下：
              <Help weId={`${this.props.weId || ''}_ms13k8`} title={title} placement="rightBottom" width={150} />
            </div>
          </Col>
        </Row>
      </div>
    );
  }
}
