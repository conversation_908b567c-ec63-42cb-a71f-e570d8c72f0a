import React from "react";
import { constants, Help } from "@weapp/ui";

const { helpClsPrefix } = constants;

const prefixCls = `${helpClsPrefix}-demo`;

export default class HelpDemo extends React.PureComponent {
  render() {
    const title = "通过popoverProps的action配置触发方式";
    return (
      <div className={prefixCls}>
        <div className={`${prefixCls}-line`}>
          click 方式：
          <Help popoverProps={{ action: ["click"] }} weId={`${this.props.weId || ''}_7c93q6`} title={title} placement="topLeft" />
        </div>
        <div className={`${prefixCls}-line`}>
          hover 方式：
          <Help popoverProps={{ action: ["hover"] }} weId={`${this.props.weId || ''}_7c93q6`} title={title} />
        </div>
      </div>
    );
  }
}
