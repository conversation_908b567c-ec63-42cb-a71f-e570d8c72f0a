---
key: Help
title: 帮助提示
person: 陈佳敏
demoInfo:
  - title: 基础用法
    des: 基本用法，提示的位置具体可参考Popover组件的placement属性
  - title: 提示文字居中
    des: 给help组件添加isCenter设置文字内部居中
  - title: 自定义弹层样式
    des: 通过className复写提示样式
  - title: 自定义内容
    des: 可将问号图标替换为文字或者其他图标
  - title: 自定义触发方式
    des: 通过popoverProps的action配置触发方式，有hover/click两种方式，具体参考Popover组件
imports:
  - "./1.tsx"
  - "./2.tsx"
  - "./customStyle.tsx"
  - "./3.tsx"
  - "./triggerWays.tsx"
---

## API

### 参数说明如下：

| 参数           | 说明              | 类型                   | 是否必填 | 默认值 |
| -------------- | ----------------- | ---------------------- | -------- | ------ |
| title          | 提示文字          | string \| ReactElement |          |        |
| placement      | 气泡框位置        | PopupPlacement         |          | top    |
| width          | 气泡框宽度        | number \| 'auto'       |          |        |
| isCenter       | 内部文字是否居中  | boolean                |          | false  |
| popupClassName | 弹层的 className  | string                 |          |        |
| hasArrow       | 是否有箭头        | booloean               |          | true   |
| popoverProps   | Popver 组件的参数 | PopoverProps           |          |        |
### popoverProps 参数：

[详情查看Popover组件文档](/ui/popover)
