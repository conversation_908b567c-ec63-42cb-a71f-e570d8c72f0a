import React from "react";
import { constants, Help } from "@weapp/ui";

const { helpClsPrefix } = constants;

const prefixCls = `${helpClsPrefix}-demo`;

export default class HelpDemo extends React.PureComponent {
  render() {
    const title = "在这里写入相应的提示信息在这里写入相应的提示信息在这里写入相应的提示信息在这里写入相应的提示信息";
    return (
      <div className={prefixCls}>
        <Help weId={`${this.props.weId || ''}_0jjaye`} popoverProps={{ className: `${prefixCls}-custom-popover` }} hasArrow={false} title={title} placement="right" isCenter ></Help>
      </div>
    );
  }
}
