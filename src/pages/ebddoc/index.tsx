import { CorsComponent } from "@weapp/ui";

const config = [{
  package: "@weapp/ui-props-design",
  component: "Comment",
  name: "评论",
  id: "ui-props-design_Comment",
  type: "basis", // 组件分类
  typeName: "基础类", // 分类名称
}]

const EbdDoc = (props: any) => {
  return (
    <CorsComponent
      weId={`${props.weId || ''}_8j848s`}
      app="@weapp/ebddoc"
      compName="LayoutCom"
      config={config}
    />
  )
}

export default EbdDoc;