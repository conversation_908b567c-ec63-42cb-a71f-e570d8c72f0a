import React from "react";
import { Spin } from '@weapp/ui';
import parse from './parse';
import Content from "../main/Content";

interface AsyncComponentState {
  Component: any;
}

let spinStyle = {
  width: '100%',
  height: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center'
}

export default function asyncComponent(getComponent: any): any {
  class AsyncComponent extends React.Component<{}, AsyncComponentState> {
    constructor(props: any) {
      super(props);
      this.state = {
        Component: null
      };
    }

    async componentDidMount() {
      const module = await getComponent();
      const _data = parse(module.default);
      this.setState({
        Component: (
          <Content weId={`${this.props.weId || ''}_3av05t`} data={_data} />
        )
      });
    }

    render() {
      const C = this.state.Component;
      return C ? C : <Spin weId={`${this.props.weId || ''}_w9qrnw`} style={spinStyle} size="large" />
    }
  }

  return AsyncComponent;
}