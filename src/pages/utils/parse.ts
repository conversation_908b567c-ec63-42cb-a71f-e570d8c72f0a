
// component parse
export default function parse(mod: any) {
  const { meta, content, demos, demoContents } = mod;
  const { key, person, title, type, demoInfo } = meta;

  let _data = {
    ...meta,
    type,
    content,
    title: key,
    subtitle: title,
    ...(person ? { maintainer: person.replace(/\/.+$/g, '') } : {}),
    demos: []
  };

  demos.forEach((demo: Element, index: number) => {
    _data.demos.push({
      orider: index,
      demoOrder: `demo-${index}`,
      ...demoInfo[index],
      content: demoContents[index],
      demo
    })
  });

  return _data;
}
