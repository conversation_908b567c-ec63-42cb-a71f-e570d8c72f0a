
import JsonML from 'jsonml.js/lib/utils';
import toReactElement from 'jsonml-to-react-element';

import definitionVars from '../docs/definition.md';
import { AnyObj } from '@weapp/ui';

const definition: { [key: string]: string } = {};

export function getCode(node: any) {
  return JsonML.getChildren(JsonML.getChildren(node)[0] || '')[0] || '';
}


/**
 * 根据 content 生成 TOC
 * @param content AST
 * @param route 组件名称（作为锚点前缀）
 * @returns contnet 和 TOC
 */
export const toc = (content: any, route?: any) => {
  let index = 0;
  const contentWithTocID = content.map((c: any) => {
    if (/^h2$/i.test(c[0])) {
      index++;
      const cwt = [...c];
      cwt.splice(1, 0, { id: cwt[1] || `${route}${index}` });
      const props = cwt[1];
      cwt[1] = {
        ...props,
        id: props?.id?.replace(/\s/g, "_")
      }
      return cwt;
    }
    return c;
  })
  

  const navigation: any[] = contentWithTocID.filter((c: any) => /^h2$/i.test(c[0])).map((h2: any) => {
    const id = h2[1].id.replace(/\s/g, "_"), value = h2[2];
    return {
      id,
      link: `#${id}`,
      content: value,
    }
  });

  return {
    content: contentWithTocID,
    navigation,
  }
}

/**
 * 根据传入的 demos 生成 demo 名称（包含锚点）
 * @param demos 组件的 dmeos
 * @returns domo title
 */
export const getDemoTitle = (demos: any) => {

  const demoTitle = demos.map((demo: any) => {
    const id = demo.title, content = demo.title;
    return {
      id,
      link: `#${id.replace(/\s/g, "_")}`,
      content,
    }
  });

  return demoTitle;
}

/**
 * toReactElement 配置
 */
export const converters = [
  [
    function (node: any): any {
      if (node?.type === 'definition') {
        definition[node.identifier] = node.url;
        return true;
      }
    },
    function (node: any) {return null},
  ],
  [
    function (node: any): any { return JsonML.isElement(node) && JsonML.getTagName(node) === 'img'; },
    function (node: any, index: number) {
      const attr = JsonML.getAttributes(node);
      if (attr.src && definition[attr.src]) {
        attr.src = definition[attr.src]
      }
      // eslint-disable-next-line jsx-a11y/alt-text
      return (<img {...attr} />);
    },
  ],
  [
    function (node: any): any {
      return JsonML.isElement(node) && JsonML.getTagName(node) === 'pre';
    },
    function (node: any, index: number) {
      const attr = JsonML.getAttributes(node);
      return (
        <pre key={index} className="line-numbers">
          <code dangerouslySetInnerHTML={{ __html: attr.highlighted || getCode(node) }} />
        </pre>
      );
    },
  ],
  [
    function (node: any): any { return JsonML.isElement(node) && JsonML.getTagName(node) === 'a'; },
    function (node: any, index: number) {
      const attr = JsonML.getAttributes(node);
      const content = JsonML.getChildren(node);
      if (Array.isArray(content)) {
        return (<a {...attr} >{content.map(c => {
          if (JsonML.isElement(c) && JsonML.getTagName(c) === 'img') {
            const src = c?.[1]?.src || '';
            if (src && definition[src]) {
              c[1].src = definition[src];
            }
          }
          return toReactElement(c)
        })}</a>);
      }
      return (<a {...attr} >{content}</a>);
    },
  ],
];

// 默认解析 md 变量
// @ts-ignore
definitionVars.content.forEach(c => toReactElement(c, converters))


/**
 * 组件文档API按字母排序
 * @param apiDatas 文档api JSONML树
 * @returns 
 */
export const getApiSort = (apiDatas: AnyObj[]) => {
  const result = apiDatas.map(apiData => {

    if(apiData[0] !== "table") return apiData;
    return apiData?.map((item: any) => {
      if(!item) return item;
  
      let [ itemName, ...bodyList ] = item || [];

      if(itemName !== "tbody") return item;

      bodyList.sort((tr: any, lastTr: any) => {
        const apiName = tr?.[1]?.[1] || "a";
        const lastApiName = lastTr?.[1]?.[1] || "a";
        return apiName > lastApiName ? "1" : "-1";
      })

      return [
        itemName,
        ...bodyList
      ]
    }) || apiData;
  })

  return result;
}