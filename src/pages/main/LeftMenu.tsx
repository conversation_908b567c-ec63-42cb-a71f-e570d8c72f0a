import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>u, AnyObj } from '@weapp/ui';
import { constants } from '@weapp/ui-props-design';

import { LeftMenuProps } from '../types';
import { classUseMemo } from '@weapp/utils';
import { uiRoot } from '../..';
import { withRouter } from 'react-router-dom';

const { uiPropsDesignDemoClsPrefix } = constants;
class LeftMenu extends React.Component<LeftMenuProps> {
  static defaultProps = {
    prefixCls: `${uiPropsDesignDemoClsPrefix}-LeftMenu`,
  }
  onChange = (selectValue: string, item: AnyObj) => {
    this.props.history!.push(`${uiRoot}${item?.path}`);
  }

  render() {
    const { prefixCls, data } = this.props;
    const menuData = classUseMemo('menuData', this, () => {
      return data.map((da) => {
        return {
          ...da,
          id: da.key,
          content: da.title
        }
      })
    }, [data]);

    return (
      <div className={prefixCls}>
        <Scroller weId={`${this.props.weId || ''}_6xoduj`}>
          <Menu weId={`${this.props.weId || ''}_egvpy8`}
            type="menu"
            mode="inline"
            data={menuData}
            onChange={this.onChange}
          />
        </Scroller>
      </div>
    )
  }
}

export default withRouter(LeftMenu);
