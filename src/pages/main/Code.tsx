import React from 'react';
import { Pop<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, AnyObj } from '@weapp/ui';
import { with<PERSON><PERSON><PERSON>, RouteComponentProps } from "react-router-dom";
import toReactElement from 'jsonml-to-react-element';
import { converters } from '../utils/converter';

const { message } = Dialog;

const codeClsPrefix: string = "code";
const pointStyle = { cursor: 'pointer' };
const demoStyle = { maxWidth: '1200px', margin: '50px auto' };
interface CodeProps extends RouteComponentProps {
  isPC?: boolean,
  data: any,
  fileName: string,
  folderName: string,
  entryCom: string,
}

interface CodeState {
}

class Code extends React.Component<CodeProps, CodeState>{

  state = {
    activeKey: '0',
    bg: true,
    accordion: false,
    visible: false,
    tabVisible: false,
    menuValue: 'tab1'
  };

  handleChange = (activeKey: any) => {
    this.setState({
      activeKey,
    });
  };

  onMenuChange = (key: any) => {
    this.setState({ menuValue: key });
  }

  showTabDialog = () => {
    this.setState({ tabVisible: true });
  }

  closeTabDialog = () => {
    this.setState({ tabVisible: false });
  }

  handleClick = (codeString: string) => {
    return () => {
      var oTextarea = document.createElement('textarea');
      oTextarea.value = codeString;
      document.body.appendChild(oTextarea);
      oTextarea.select(); // 选择对象
      document.execCommand("Copy"); // 执行浏览器复制命令
      oTextarea.className = 'oInput';
      oTextarea.style.display = 'none';
      message({
        type: 'success', // type: 'info', 'error', 'success'
        content: '复制成功'
      });
    }
  }

  editOnLine = () => {
    const { isPC, fileName, folderName, entryCom } = this.props;
    let url = '/ecode/playground/ui'
    let params: AnyObj = {};
    params.jsCodePath = `src/pages/demos/${folderName}/${isPC ? 'pc' : 'mb'}/${fileName}`  // 读取代码的文件
    params.cssCodePath = `src/pages/demos/${folderName}/style/index.less`;  // 读取样式的文件
    // params.cssCodePath = 'index.css'  // 样式文件
    // params.ref = 'master'  // 分支
    if (entryCom) params.entryCom = entryCom;
    let _parmas = ''
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        if (_parmas !== '') {
          _parmas += '&' + key + '=' + params[key]
        } else {
          _parmas += key + '=' + params[key]
        }
      }
    }
    const _url = url + '?' + _parmas;
    window.open(_url)
  }

  render() {
    const { tabVisible, menuValue } = this.state;
    const { data } = this.props;
    const code = data.content.filter((c: any) => c[0] === 'pre');
    let codeString: string = code[0][2][1];

    let des = [...data.content];
    const indexZh = des.findIndex(c => c[0] === 'h2' && c[1] === 'zh-CN');
    const indexEn = des.findIndex(c => c[0] === 'h2' && c[1] === 'en-US');
    if (indexZh > 0 && indexEn > 0) {
      if (indexZh < indexEn) {
        des = des.slice(indexZh + 1, indexEn);
      } else {
        des = des.slice(indexZh + 1);
      }
    }
    des = des.filter((c, i) => c[0] !== 'pre' && c !== 'article' && c[0] !== 'h2');
    const codeCom = () => (
      <Scroller weId={`${this.props.weId || ''}_act0p8`}
        className="highlight-wrapper"
        key="code"
        direction={'both'}
      >
        {code.map((c: any) => toReactElement(c, converters))}
      </Scroller>
    );
    const DemoPc = data.demo.com || data.demo;

    const demoId = data.title.replace(/\s/g, "_");

    return (
      <div id={`${demoId}`} className={`${codeClsPrefix}`} style={demoStyle}>
        {/* 对应的 title */}
        <div className={'title_box'}>
          <h3 className={`title`}>{data.title}</h3>
          <div className={`description`}>{data.des}</div>
        </div>
        <div className={'demo_box'}>
          <DemoPc weId={`${this.props.weId || ''}_hq4t9e`} />
          <div className={`${codeClsPrefix}-icon-wrap`}>
            <Popover weId={`${this.props.weId || ''}_p6dnv8`}
              popoverType='tooltip' placement={'top'} popup={'在线编辑'}>
              <span><Icon weId={`${this.props.weId || ''}_swvlyk`} name="Icon-Attachment-editor" onClick={this.editOnLine} size='lg' style={pointStyle} /></span>
            </Popover>
            <Popover weId={`${this.props.weId || ''}_5md3l0`}
              popoverType='tooltip' placement={'top'} popup={'复制代码'}>
              <span><Icon weId={`${this.props.weId || ''}_7ctb0p`} name="Icon-copy" onClick={this.handleClick(codeString)} size='lg' style={pointStyle} /></span>
            </Popover>
            <Popover
              weId={`${this.props.weId || ''}_pftslm`}
              popoverType='tooltip' placement={'top'} popup={'显示代码'} >
              <span> <Icon weId={`${this.props.weId || ''}_5w6frl`} name="Icon-enlarge" onClick={this.showTabDialog} size='lg' style={pointStyle} /></span>
            </Popover>
          </div>
        </div>

        <Dialog weId={`${this.props.weId || ''}_z6abrd`}
          className={`demo_code_dialog`}
          visible={tabVisible}
          onClose={this.closeTabDialog}
          title="代码"
          closable
          destroyOnClose
          scale
          mask
          maskClosable
          placement="right"
          menuValue={menuValue}
          onMenuChange={this.onMenuChange}
          icon="Icon-Advanced-search"
        >
          {/* 对应的 react 代码 */}
          {codeCom()}
        </Dialog>
      </div>
    );
  }
}


export default withRouter(Code);