import { PureComponent } from "react";
import { RouteComponentProps, with<PERSON><PERSON><PERSON> } from "react-router-dom";
import { BaseProps, CorsComponent } from "@weapp/ui";
import { uiPropsDesignDemoClsPrefix } from "../../constants";

export interface MainProps extends BaseProps, RouteComponentProps {
  
}
class Main extends PureComponent<MainProps>{
  render() {
    const {
      match: { params },
    } = this.props as any;

    return (
      <div className={`${uiPropsDesignDemoClsPrefix}`}>
        <CorsComponent weId={`${this.props.weId || ''}_qoyb1k`}
          app="@weapp/ebddesigner"
          compName="Design"
          pageId={params.pageId || ''}
        />
      </div>
    )
  }
}

export default withRouter(Main);