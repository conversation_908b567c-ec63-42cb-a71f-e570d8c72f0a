import React from 'react';
import { withRouter } from 'react-router-dom';
import { constants } from '@weapp/ui-props-design';
import toReactElement from 'jsonml-to-react-element';

import { ContentProps } from '..//types';
import { getDemoTitle, toc, converters } from '../utils/converter';
import Code from './Code';

const { uiPropsDesignDemoClsPrefix } = constants;
class Content extends React.Component<ContentProps> {
  static defaultProps = {
    prefixCls: `${uiPropsDesignDemoClsPrefix}-content`,
  }

  // data 处理
  getComponentData = () => {
    const { data } = this.props;
    const { content, demos } = data;
    const _content = Array.isArray(content) ? [
      ['h2', '代码演示'],
      ...content,
    ] : [];
    let { content: cwt, navigation } = toc(_content, data.key);
    Array.isArray(cwt) && cwt.length > 0 && cwt.shift();
    const demoList = getDemoTitle(demos);
    navigation = navigation.map(item => {
      const { id } = item;
      if (id === "代码演示") {
        return {
          ...item,
          children: demoList
        }
      }
      return item;
    })
    let resData = {
      ...data,
      navigation,
      content: cwt,
    };
    return resData;
  }

  // pc com 的显示内容
  getDemo = () => {
    const _data = this.getComponentData();
    let demos = _data?.demos.map((d: any, i: number) => {
      let filepath = this.props.data.imports[i];
      let folderName = '';
      let code: string = d.content[1][2][1];
      let re = /export default class (\w+)/;
      let entryCom = re.exec(code)?.[0].replace('export default class ', '') || '';
      if (entryCom === '') {
        re = /export default (\w+)/;
        entryCom = re.exec(code)?.[0].replace('export default ', '') || '';
      }
      if (entryCom === 'withRouter') {
        re = /withRouter\((\w+)\)/;
        entryCom = re.exec(code)?.[1] || '';
      }

      return <Code weId={`${this.props.weId || ''}_r7xp4d@${i}`} key={i} isPC={true} data={d}
        fileName={filepath.slice(2, filepath.lenght)}
        folderName={folderName}
        entryCom={entryCom}
      />
    });
    return (
      <div className="content-pc">
        {demos}
      </div>
    );
  }

  // 文档显示内容
  getDoc = () => {
    const { data, type } = this.props;
    if (data) {
      let { content } = data;
      if (content[0] === 'article') {
        content = content.slice(1);
      }
      if (type === 'CHANGELOG') {
        return {
          content: toReactElement(data.content, converters)
        };
      }
      const { content: cwt, navigation } = toc(content, type);
      content = cwt;
      return {
        content: content.map((c: any) => {

          if(c[0].match(/^h2/) && typeof c[1] === "object") {
            const props = c[1] || {};
            c[1] = {
              ...props,
              // id中空格需要用下划线代替，否则锚点不生效
              id: props?.id?.replace(/\s/g, "_")
            }
          }

          return toReactElement(c, converters)
        }),
        navigation,
      };
    }
    return {};
  }

  getMD = () => {
    const doc = this.getDoc();
    return (
      <div className={`demo_markdown demo_content_doc demo_content_navigation`}>
        {doc.content}
      </div>
    )
  }

  render() {
    const { prefixCls, data } = this.props;
    const { title, subtitle, maintainer, issue } = data;
    return (
      <div className={prefixCls}>
        <div className={`${prefixCls}-title`}>
          <span className={`${prefixCls}-title-main`}>{title}{subtitle}</span>
          <span className={`${prefixCls}-title-maintainer`}>维护者：{maintainer}</span>
          {issue && <a className={`${prefixCls}-title-issue`} href={issue}>issue</a>}
        </div>
        <h3 id="代码演示">代码演示</h3>
        {this.getDemo()}
        <div className={`${prefixCls}-md`}>
          {this.getMD()}
        </div>
      </div>
    )
  }
}

export default withRouter(Content);
