import { Switch, Icon } from '@weapp/ui';
import React from 'react';
import { constants } from '@weapp/ui-props-design';

import { HeaderProps } from '../types';
import { DemoContext } from './utils';
const { uiPropsDesignDemoClsPrefix } = constants;
class Header extends React.Component<HeaderProps> {
  static defaultProps = {
    prefixCls: `${uiPropsDesignDemoClsPrefix}-header`,
  }
  render() {
    const { prefixCls } = this.props;
    return (
      <div className={prefixCls}>
        <div className={`${prefixCls}-title`}>
          @weapp/ui-props-design
        </div>
        <div className={`${prefixCls}-ui-lnk`}>
          <DemoContext.Consumer weId={`${this.props.weId || ''}_zjwt1v`}>
            {
              ({ type, onChangeType, isEdit, onChangeEdit }) => {
                const value = type === 'Normal';
                return (
                  <div>
                    <div className={`${prefixCls}-type`}>
                      <span>EB组件</span>
                      <Switch weId={`${this.props.weId || ''}_09om09`} size="md" value={value} onChange={onChangeType} />
                      <span>公共组件</span>
                    </div>
                    <div className={`${prefixCls}-isEdit`}>
                      {
                        !value && (
                          <Icon weId={`${this.props.weId || ''}_mko34g`} name={isEdit ? "Icon-addcomponent" : "Icon-edit-1"} size="lg" onClick={onChangeEdit}
                            title={isEdit ? '新增EB组件' : '修改标准组件'} />
                        )
                      }
                    </div>
                  </div>
                )
              }
            }
          </DemoContext.Consumer>
          <a href={`${origin}/ui`} target='_blank' rel="noreferrer">组件库</a>
        </div>
      </div>
    )
  }
}

export default Header;
