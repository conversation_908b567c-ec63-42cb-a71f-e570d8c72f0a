import React, { Suspense } from 'react';
import { withRouter, Switch, Route } from 'react-router-dom';
import { classUseMemo, corsImport } from '@weapp/utils';
import { CorsComponent, Layout, utils } from '@weapp/ui';
import { constants } from '@weapp/ui-props-design';

import { DemoContext } from './utils';
import { MainProps, MainState } from '../types';

import Header from './Header';
import LeftMenu from './LeftMenu';

import impComPc from '../routes/pc';
import impComEb from '../routes/ebComs';

const routesPc = impComPc();
const routesEb = impComEb();
const { formatParentPath } = utils;
const noop = () => null;
const { uiPropsDesignDemoClsPrefix } = constants;
class Main extends React.Component<MainProps, MainState> {
  constructor(props: MainProps) {
    super(props);
    this.state = {
      type: 'Normal',
      isEdit: false,
    }
  }
  onChangeType = (value: boolean) => this.setState({ type: value ? 'Normal' : 'EB' }, () => {
    this.props.history.push('/ui-props-design');
  });
  onChangeEdit = () => {
    this.setState({ isEdit: !this.state.isEdit }, () => {
      corsImport('@weapp/designer').then((r) => { 
        const inspectorDesigner = r.inspectorDesigner;
        if (this.state.isEdit) {
          inspectorDesigner?.init?.();
          this.props.history.push('/ui-props-design/ebDemos');
        } else {
          inspectorDesigner?.exit?.();
          const dom = document.getElementById('root');
          dom && (dom.style.padding = 'unset');
          this.props.history.push('/ui-props-design');
        }
      })
    });
  }
  render() {
    const { type, isEdit } = this.state;
    const value = classUseMemo('contextValue', this, () => ({ type: type, onChangeType: this.onChangeType, isEdit, onChangeEdit: this.onChangeEdit }), [type, isEdit]);
    const parentPath: string = formatParentPath(this.props);
    const routes = classUseMemo('routes', this, () => type === 'Normal' ? routesPc : routesEb, [type])
    return (
      <Suspense weId={`${this.props.weId || ''}_de3z3i`} fallback={noop}>
        <div className={uiPropsDesignDemoClsPrefix}>
          <DemoContext.Provider weId={`${this.props.weId || ''}_pprna5`} value={value}>
            <Layout weId={`${this.props.weId || ''}_beo7wt`} className={`${uiPropsDesignDemoClsPrefix}-main-layout`}>
              <Layout.Box weId={`${this.props.weId || ''}_knxz7x`} type="header">
                <Header weId={`${this.props.weId || ''}_daikjq`} />
              </Layout.Box>
              {
                (type === 'Normal' || isEdit) && (
                  <Layout.Box weId={`${this.props.weId || ''}_knxz7x`} type="side" className={`${uiPropsDesignDemoClsPrefix}-main-layout-side`} allowHidden>
                    <LeftMenu weId={`${this.props.weId || ''}_daikjq`} data={routes} />
                  </Layout.Box>
                )
              }
              <Layout.Box weId={`${this.props.weId || ''}_knxz7x`} type="content">
                {
                  type === 'Normal' || isEdit ? (
                    <Switch weId={`${this.props.weId || ''}_m70ccu`} >
                      {routes.map((route: any, i: number) => {
                        return <Route weId={`${this.props.weId || ''}_4j91an@${route.path}`}
                          key={route.path}
                          path={`${parentPath}${route.path}`}
                          component={route.com}
                        />
                      })}
                    </Switch>
                  ) : (
                    <CorsComponent weId={`${this.props.weId || ''}_qoyb1k`}
                      app="@weapp/designer-demo"
                      // compName="FlowDesignerDemo"
                      compName="DesignerDemo"
                      layoutType="GRID"
                    />
                  )
                }
              </Layout.Box>
            </Layout>
          </DemoContext.Provider>
        </div>
      </Suspense>

    )
  }
}

export default withRouter(Main);
