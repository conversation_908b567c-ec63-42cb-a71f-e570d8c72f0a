import { action } from 'mobx';

export type TypeOf<T, K extends keyof T = keyof T> = {
  [P in K]?: T[P];
}

export interface BaseStoreType<T = any> {
  setMobxState: (params: TypeOf<T>) => void;
}

export type StoreType<S, T = Omit<S, "setMobxState">, P = Pick<T, keyof T>> = BaseStoreType<P> & P;

export default class BaseStore<T extends BaseStore = any> {

  /**
   * 修改store内容
   * @param params 需要修改的键值对 
   */
  @action
  setMobxState = <K extends keyof T = keyof T>(params: TypeOf<T> = {}) => {
    Object.keys(params).forEach((key: string) => {
      (this as any)[key] = params[key as K];
    })
  }
}