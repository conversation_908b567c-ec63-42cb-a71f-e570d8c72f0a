import React, { Suspense, ComponentType, ForwardRefRenderFunction, ReactNode } from 'react';
import { AnyObj } from '@weapp/ui';

const noop = () => null;

export type LoaderType<P extends AnyObj = any> = () => Promise<{
  default: React.ComponentType<P>;
}>;

export type LoadingType<P extends AnyObj = any> = ReactNode | ((props: P) => ReactNode);

export interface LoadableComponentProps<P extends AnyObj = any> {
  name: string;
  component?: React.ComponentType<P>;
  loader?: LoaderType<P>;
  loading?: LoadingType<P>;
};

const components: string[] = [];

function Loadable<P extends AnyObj = any> (opts: LoadableComponentProps<P>): ComponentType<P> {
  const { name = 'Test', loader, component, loading } = opts;

  if (components.includes(name)) {
    throw new Error(`[React Loadable]: name 字段已经使用过 '${name}'，请检查重复！`);
  } else if (name.includes('.')) {
    throw new Error(`[React Loadable]: name 字段 '${name}' 中禁止使用'.'`);
  }

  components.push(name);

  if (loader) {
    const Com = React.lazy(loader);

    const LoadableComponent: ForwardRefRenderFunction<unknown, any> = (props, ref) => {
      const loadingEle = (typeof loading === 'function' ? loading(props) : loading) || noop;
      return (
        <Suspense weId={`${props.weId || ''}_6l2sf2`} fallback={loadingEle}>
          <Com weId={`${props.weId || ''}_2cmfh8`} {...props} ref={ref} wrappedComponentRef={props.wrappedComponentRef || ref} />
        </Suspense>
      );
    };
  
    LoadableComponent.displayName = name;
    return LoadableComponent;
  } else if (component) {
    component.displayName = name;
    return component;
  } else {
    throw new Error('[React Loadable]: 必须设置 loader 或 component ！');
  }
}

export default Loadable;
