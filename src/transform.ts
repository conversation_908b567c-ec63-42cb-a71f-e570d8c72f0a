import { AnyObj } from '@weapp/ui';
import { transProps } from './utils/transform';
import { EbButton } from './components/button/transform';
import { HelpTip } from './components/help/transform';

import { browser_trans_config, browserPanel_trans_func } from './components/browser';
// import { alert_trans_config } from './components/alert';
import { empty_trans_func } from './components/empty/transform';
import { alert_trans_func } from './components/alert/transform';
import { bread_crumb_trans_func } from './components/bread-crumb/transform';

/* 配置TransForm 映射关系主入口 */
/* 方法名对标 ui库配置的compName  */

export const EBTransformMap: AnyObj = {
  Browser: browser_trans_config,
  // BrowserPanel: browserPanel_trans_config,
  // Alert: alert_trans_config,
}

let EBTransform: AnyObj = {
  EbButton,
  HelpTip,
  BrowserPanel: browserPanel_trans_func,
  Empty: empty_trans_func,
  Alert: alert_trans_func,
  BreadCrumb: bread_crumb_trans_func,
};

/* 按照配置生成EBTransform */
const initEBTransform = () => {
  let result: AnyObj = {};
  Object.keys(EBTransformMap).forEach((key: string) => {
    result[key] = (props: any) => transProps(props, EBTransformMap[key]);
  })
  EBTransform = Object.assign(EBTransform, result);
}

initEBTransform();

export default EBTransform;
