import { AnyObj } from "@weapp/ui";
import { ErrorBoundary } from "@weapp/utils";
import { DesignOptions } from "@weapp/designer";
import React, { ReactNode } from "react";
import { withRouter } from "react-router-dom";
import { uiPropsDesignClsPrefix } from "./constants";
import EBTransform, { EBTransformMap } from "./transform";
import { revertProps as _revertProps } from './utils/transform';
/**
 * 过滤 eb特殊数据
 */
export function excludeEBProps(props: AnyObj) {
  const excludeEBKeys = ['type', 'layout', 'flow', 'name'];
  const newProps = { ...props };
  excludeEBKeys.forEach((key) => delete newProps[key]);
  return newProps;
}

/**
 * 转换UI EB组件对应的特殊数据，对照transform
 */
export function revertProps(props: AnyObj, uiType: string, allProps: AnyObj) {
  const map = EBTransformMap[uiType];
  const func = EBTransform[uiType];
  let result = map ? _revertProps(props, map) : props;
  return func?.(result, allProps) || result;
}

export type IReactComponent<P = any> = |
  React.ClassicComponentClass<P> |
  React.FunctionComponent<P> |
  React.ForwardRefExoticComponent<P> |
  React.ComponentClass<P>;

export type IEBComponent<P = any> = IReactComponent<P> & {
  defaultOpts?: DesignOptions;
  renderTitle?: (self: any, nextProps: P) => ReactNode;
}

const ebMiddleware = (name: string, type: 'View' | 'Design', uiType: string, Com: IEBComponent<any>, otherParams?: AnyObj): IEBComponent<any> => {
  let Wrap: IEBComponent<any> = React.forwardRef(
    (props, ref) => {
      const { config: _config } = props;
      const config = revertProps(excludeEBProps(_config), uiType, props);
      const notEmpty = type === 'View' || (config && Object.keys(config).length > 0);  // Design 统一处理空设置提示
      return <ErrorBoundary weId={`${props.weId || ''}_339bc1`}>
        {
          notEmpty ? type === 'Design' ? (
            <div>
              <Com weId={`${props.weId || ''}_i7atts`} ref={ref} {...props} config={config} />
            </div>
          ) : (
            <Com weId={`${props.weId || ''}_i7atts`} ref={ref} {...props} config={config} />
          ) : (
            <div className={`${uiPropsDesignClsPrefix}-eb-middleware-empty`}>请设置内容</div>
          )
        }
      </ErrorBoundary>
    }
  )
  otherParams?.needRouter && (Wrap = withRouter(Wrap));
  // 赋值组件的静态方法
  Object.keys(Com).forEach(key => {
    //@ts-ignore
    Wrap[key] = Com[key]
  })
  Wrap.displayName = name;
  return Wrap;
}

export default ebMiddleware;
