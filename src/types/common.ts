import { AnyObj, BaseProps, FormDatas, FormStoreType } from "@weapp/ui";
import { ComponentProps } from "react";
import { IComData, CompDesignStoreType, PageModuleType } from "@weapp/designer";

export type CommonDesignProps = {
  /* 模块标记 */
  module: string;
  /* 事项id */
  matterId?: string;
} & AnyObj;
export interface CommonProps extends BaseProps {
  weId?: any;
  /* 设计器固定参数 */
  designProps?: CommonDesignProps;
}

 /** 组件属性可视化扩展对象 */
 export type VisualDataType<T> = {
  /** 
   * @params 默认生成的组件变化的相关ecode代码
   * @params changedProps 可视化过程中组件发生改变的props
   */
  customizeCode: (code: string, changedProps: Partial<T>) => string;
  componentDidUpdate: (changes: ComponentProps<any>) => void;
}

export type VisualType = Record<string, VisualDataType<any>>;

/* config 自定义组件公共类型声明 */
export interface CommonConfigCustomProps extends BaseProps {
  weId?: any;
  onFormChange?: (changes: FormDatas) => void; //触发组件配置改变的回调
  form?: FormStoreType; // 表单的FormStore实例
  config?: AnyObj; // 组件配置的数据
  coms?: IComData[]; //组件详情数据
  childComs?: IComData[]; //子组件详情数据
  onChange?: (value: any) => void; // 组件值改变的回调
  value?: any; // 当前字段的默认值
  dataKey?: string; // 字段的key

  /** Config 自定义组件中使用，参数均为props继承下来的内容 */
  pageId?: string;
  appid?: string;
  page?: AnyObj;
  store?: CompDesignStoreType;
  onDatasetChange?: (datasetVal?: any, type?: string) => void;
  clientType?: 'PC' | 'MOBILE';
  layoutInfo?: AnyObj;
  pageScope?: PageModuleType; 
}

export type Client = "PC" | "MOBILE";
