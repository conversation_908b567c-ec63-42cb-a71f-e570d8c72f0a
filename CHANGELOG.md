## [0.1.11](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.10...v0.1.11) (2023-11-01)



## [0.1.10](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.9...v0.1.10) (2023-10-18)


### Bug Fixes

* **comment:** no.2515645 解决部分情况下标题无法展示的问题 ([f451b9e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f451b9e1f09270e90ce1bd0a0aea28ffaeee6816))
* no.2212512 修复删除评论数据总数不刷新的问题 ([d382784](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d382784a35d83602f969f320add7cc33627b3b38))
* no.2212512 修复删除评论数据总数不刷新的问题 ([a9cea4a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a9cea4adcbbaf11fe0faeecee0b1be8785f5ebc0))
* no.2212512 修复评论pc端新增评论后总数不刷新的问题 ([d70a3c7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d70a3c7c45866084ff4d70d9ad9a8161f409a202))
* no.2261931 修复yarn lib 报错问题 ([c5d7972](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c5d7972e197d37ff76b233c7457de45d9e33e9f9))
* no.2261931 修复事项关联事项范围屏蔽的字段也展示出来了的问题 ([20c19ee](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/20c19ee905f74af39002121aa6e5e8f3c056fed4))
* no.2261931 修复事项关联分类设置关联新增分类失败的问题 ([4591293](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4591293add5725a634b787aadd1d59ad2facebeb))
* no.2261931 修复事项关联分类设置分类删除传参错误的问题 ([149ec38](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/149ec38fa1154d01eda62a0be64510f43060e9f4))
* no.2261931 修复事项关联分类设置禁用设置项判断错误的问题 ([11a6332](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/11a6332eba9c88a5528af1812fffed18c1af3e1a))
* no.2261931 修复事项关联分类部分字段不展示的问题 ([903255d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/903255dcd890e43df0d6562cc6c98ace8f553363))
* no.2261931 修复事项关联平铺模式分类禁用失效的问题 ([2f3ab9b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2f3ab9bf26b046993f337507e94c91428e31228c))
* no.2261931 修复事项分类多语言失效问题 ([fbf955b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/fbf955b62ae07e25a05dd57ee0b96b7e0a2eb98e))
* no.2261931 修复事项范围和分类数据非首次配置未同步的问题 ([5be29d0](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5be29d07cec4eb5cb8fa6840ad45ee863b6c6243))
* no.2261931 修复事项范围和实际效果配置未同步的问题(平铺模式) ([86fe1c6](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/86fe1c663dbdf21e9d309219c872f7c22104a532))
* no.2261931 修复分类多语言接口使用错误问题 ([7165269](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7165269beac0ea9888c3779dabe06e52fb771ce4))
* no.2261931 修复分类多语言接口使用错误问题 ([17c2e10](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/17c2e104140e89f64d495f050984f8533ac18b8f))
* no.2261931 修复分类多语言接口使用错误问题 ([3e8d50f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3e8d50ffa23b8061eb35d83bf753c8c339b2ffb8))
* no.2261931 修复表单设计器接入事项关联，分类id错误的问题 ([fc2c1be](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/fc2c1be02f95dc51c7bafd802374c2a3c535681c))
* no.2261931 修复表单设计器未开发平铺模式的问题 ([8f476a0](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8f476a0d8fdd798ca3f85eeca6b1320a3961d6ed))
* no.2261931 修复非平铺模式事项关联背景色问题 ([5d8a7f2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5d8a7f244e2257efed68c36d61d62a01732c8f3a))
* no.2261931 修复页面设计器，展示标题，事项关联组件样式问题 ([ffaab49](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ffaab49ef0e4e0b814b531859ce590a72875e561))
* no.2261931 修复页面设计器分类id获取不准确的问题 ([903ab7d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/903ab7d14b7c2f106fee5645f3c9403ffca228b8))
* no.2302827 修复【发包后】【crm】【仿真】菜单上数字传递到可视化后不显示问题 ([ff4fa1f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ff4fa1f0a0d2d77fde62a66d2d3063be1d7b1481))
* no.2507857 解决构建报错问题 ([dae3aa2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/dae3aa232c9f4b4575dbb4e31736083d98f4adc0))


### Features

* **comment:** no.2515645 新增评论与eb评论对接标准高级搜索、排序，搜索 ([293ec2f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/293ec2fdf50b928a03f12d39c2f8f7aa81e75924))
* **comment:** no.2515645 新增评论与eb评论对接标准高级搜索、排序，搜索 ([9e0f3fb](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9e0f3fb3db77a381be12aa26fdbb552c6a331667))
* **fast-create:** no.2560053 新增eb快速新建组件开发的功能 ([b4ebaaf](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b4ebaafdb7838683145a3d5a8b3b6e2e589b6c78))
* **fast-create:** no.2560053 新增eb快速新建组件开发的功能 ([ca418f8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ca418f8edec6f2aea0176138b7a31d42eef2724e))
* no.202307130216 新增可视化编辑器配置支持多语言 ([79fda93](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/79fda93e711b74b382f768d3dc2064fadb8ff3e8))
* no.202307130216 新增可视化编辑器配置支持多语言 ([72d2bac](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/72d2bacf561df1671d79d65417b56d363f32f887))
* no.202307130216 新增可视化编辑器配置支持多语言 ([a50b270](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a50b2703a73d672fb7c0e362836d6227b630870f))
* no.202307130216 新增可视化编辑器配置支持多语言 ([e0d75fd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e0d75fdf8d7715749551d45c27040171ba0bedef))
* no.202307130216 新增可视化编辑器配置支持多语言 ([e8be153](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e8be1532cbfaa60ec0ace98f3066a3e527b88c78))
* no.202307130216 新增可视化编辑器配置支持多语言 ([5b9d2fd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5b9d2fd0be61bd296bbe7d2f455e1582335995a0))
* no.202307130216 新增可视化编辑器配置支持多语言 ([884b829](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/884b8297937c20ca31a5337da987b96fb5702444))
* no.202307130216 新增可视化编辑器配置支持多语言 ([374a70b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/374a70b5948a7e2b91417feb70657602a5965d0f))
* no.202307130216 新增可视化编辑器配置支持多语言 ([f14aef9](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f14aef97774a64ae56c8258dbef4f0225db03812))
* no.202307130216 新增可视化编辑器配置支持多语言 ([4a3da07](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4a3da075872316e98ba150ade3d58b1684d7fd21))
* no.202307130216 新增可视化编辑器配置支持多语言 ([f315ce3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f315ce37529c5864a2d15c1a9f911929e4fc84d5))
* no.202307130216 新增可视化编辑器配置支持多语言 ([f81e550](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f81e550fb4cf95db12fef131b5fad9fd4530c032))
* no.202307130216 新增可视化编辑器配置支持多语言 ([0a66a0d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0a66a0d86bc1e2a0c31166c494e678bbd56813fd))
* no.2261931 新增事项关联EB组件分类设置项 ([8fc81f3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8fc81f3fae48a5a2db793c6490faf4e58ec22ca0))
* no.2261931 新增事项关联EB组件分类设置项 ([bef217e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/bef217ed802a3d23463f836a3a1aa30700526a53))
* no.2261931 新增事项关联EB组件分类设置项 ([b39e7b3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b39e7b3d6117632800dc972640fc2784ffbb552c))
* no.2261931 新增事项关联EB组件分类设置项 ([3f5eadf](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3f5eadf52f78312317a627afaa1fbda900f67d03))
* no.2261931 新增事项关联EB组件分类设置项 ([7f72c35](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7f72c351ead5a2bb93092f2c2f8e7bcd66f55e82))
* no.2261931 新增事项关联EB组件分类设置项 ([8914593](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8914593819fe3595492af8f013d5405625bdb7dc))
* no.2261931 新增事项关联EB组件分类设置项 ([00f2c52](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/00f2c5210ef6f1687e52a427f7c41b9bd00d4f4c))
* no.2261931 新增事项关联EB组件分类设置项 ([30cf8ba](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/30cf8ba69fc1c83ebd13c67ffece473bc001afbe))
* no.2261931 新增事项关联EB组件分类设置项 ([0a8ecea](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0a8ecea299887b8f2c4d8d2f2b2485177ae44ffc))
* no.2261931 新增事项关联EB组件分类设置项 ([8cc1cf7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8cc1cf706a6b02bf746240aa5e53bcba98087d2e))
* no.2261931 新增事项关联EB组件分类设置项 ([9b9e4c4](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9b9e4c412160f3f3b29d79be90d8bce368f999cd))
* no.2261931 新增事项关联EB组件分类设置项(Browser) ([742d32d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/742d32dda2049b52714f89a239c6b2b1e445df19))
* no.2261931 新增事项关联EB组件分类设置项(数据mock验证) ([9bb1ff3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9bb1ff3813448dea4ff73901edd392e712c4cd05))
* no.2261931 新增事项关联EB组件支持分类显示的功能 ([34499d6](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/34499d669501d0fb5a15bd8ab3bb402d38d48581))
* no.2261931 新增事项关联EB组件支持分类显示的功能 ([78f7e39](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/78f7e3927e3f4e028e3233aaef5a14db7fe80658))
* no.2261931 新增分类名称判空处理 ([036046c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/036046c2e83eb005af78c805035f4d5a3ebb07e0))
* no.2261931 新增多语言处理 ([dbb6898](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/dbb6898752a4a4d13d69b7d99d50d3a84c7d40f8))
* no.2460685 新增eb文档 ([3cc3fd1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3cc3fd11f4824a932bac03fdce5d812391419f90))
* no.2460685 新增eb文档 ([a6e4dd2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a6e4dd28c92b85b2c309f18fd2de121d863dcdf8))
* no.2460685 新增eb文档 ([bea55f6](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/bea55f6cb8009fcfe1be672bb3f3280cf99b2d1b))
* no.2460685 新增eb文档 ([cbeefca](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/cbeefca9d30bee23b53ad0034ebe30610eacb4aa))
* no.2460685 新增eb文档 ([f044d9a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f044d9a20d02eeb5124c378c251147edd0387ddf))
* no.2460685 新增eb文档 ([1d9906d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1d9906df102bfec3bd683b78e22510a5607e93aa))
* no.2507857 新增事项关联组件文档 ([80f0e50](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/80f0e508c61e8e76026f9a269f4290ec17229a0b))
* no.2562845 新增eb格式文档 ([7c0f61a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7c0f61a8b7be96662d75352db68521a5cc66bf0f))
* no.2562845 新增eb格式文档 ([3e7844d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3e7844d5e72fcaa20dfaacff7bdf21097c5de2f3))
* no.2562845 新增eb格式文档 ([67fedd9](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/67fedd93ff594afa2aca82bd079f0aa0437b10f6))


### Performance Improvements

* no.2261931 优化事项关联分类名称校验提示 ([03c3ed3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/03c3ed37c80f37c2ad46c043c5d5a0c8741b1471))
* no.2261931 优化事项关联设置项列表样式 ([4edb836](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4edb836b000efaa0917ae1c7cc02c55306288902))
* no.2261931 优化事项分类平铺模式移动端预览样式 ([6467f65](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6467f65f813bbe1ffa973f47db26e7bd575ac8a7))
* no.2261931 优化事项分类平铺模式预览样式 ([627a76d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/627a76d99ea8ffa1d1fcf39a11cd6823f639d1fc))
* no.2261931 优化事项分类未设置事项范围的展示效果 ([4408a42](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4408a42c3948bc7af403877db550a16935049313))
* no.2261931 优化表单设计器事项关联平铺模式样式 ([595d8b1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/595d8b11e3a9b52838e22080176eb1daddd51d33))



## [0.1.9](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.8...v0.1.9) (2023-09-27)


### Bug Fixes

* no.2261931 修复事项关联EB组件无事项，保存分类报错的问题 ([2b98b8c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2b98b8c07dd9b0f3d537c31a343b376a7baf2a84))
* no.2261931 修复事项关联平铺模式+分类排序功能失效的问题 ([8afbc73](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8afbc731f8a930d21796cacf39fc8c465fb559d4))
* no.2261931 修复事项关联平铺模式+分类排序功能失效的问题 ([bd333a8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/bd333a8d0867f693243ca3683b5b36d8886e8a22))
* no.2261931 修复事项关联平铺模式排序功能失效的问题 ([f55064a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f55064a08572359c3f25e6f44e50b6e0fad8a313))
* no.2261931 修复多语言失效问题 ([2b8806d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2b8806dfff44131a71f15b9a3cae0c1294739c3f))
* no.2261931 修复平铺模块禁用的分类下的事项未隐藏的问题 ([5caf4a1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5caf4a1e54cfab3f82e31c474f762ae66289dfea))
* no.2261931 修复构建报错问题 ([48d80dd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/48d80dda9a3beae845a612de918167b09774c180))
* no.2261931 修复表单设计器，移动端，浏览框模式样式，上下布局问题 ([aa2dd60](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/aa2dd60b978ca75d6807a0e59b5888763650ff37))
* no.2261931 修复表单设计器，移动端，浏览框模式样式，上下布局问题 ([6c4c282](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6c4c2826d2f3c942520f7b9da0b26aab34f89195))
* no.2261931 修复表单设计器，移动端，浏览框模式样式，上下布局问题 ([d790281](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d79028141c38bd1fb60b2c40e3b6b4911e30d6d2))



## [0.1.8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.7...v0.1.8) (2023-08-16)


### Features

* no.202307130216 新增可视化编辑器配置支持多语言 ([1b9063e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1b9063e9cdc850100c25c26d5ff6962c054832d8))
* no.202307130216 新增可视化编辑器配置支持多语言 ([543fb1c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/543fb1c488fed15c1af8c13ea73f16b2321aa1df))
* no.202307130216 新增可视化编辑器配置支持多语言 ([1921a6e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1921a6e04a963a089043bbbafb2eedae4dd5f722))
* no.202307130216 新增可视化编辑器配置支持多语言 ([d16af22](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d16af22140230a12742da0f4fb45108d6073da9a))
* no.202307130216 新增可视化编辑器配置支持多语言 ([fc575e7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/fc575e7c399af8b24171b90e31fc70262c67ba52))
* no.202307130216 新增可视化编辑器配置支持多语言 ([1032711](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1032711ee7147f21eae61493027976c3d99ee6a9))
* no.202307130216 新增可视化编辑器配置支持多语言 ([7c82607](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7c8260764e20573fddef2fda998e1d1cba1955d3))
* no.202307130216 新增可视化编辑器配置支持多语言 ([acf3f56](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/acf3f5642dc50eb9ef589330bbc1b7c5481ed887))
* no.202307130216 新增可视化编辑器配置支持多语言 ([ba4fc5c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ba4fc5cfdc0828ee92cff4b4d97f523c70dd62b2))
* no.202307130216 新增可视化编辑器配置支持多语言 ([4a5c62e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4a5c62e85599c0d158d8d0d8108a732b91107e25))
* no.202307130216 新增可视化编辑器配置支持多语言 ([74c3583](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/74c358314a847c9dc17e465e4cca1b6d0c53c55f))
* no.202307130216 新增可视化编辑器配置支持多语言 ([1f23951](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1f23951ac925e395dc14605b3671918c9ee6aeec))
* no.202307130216 新增可视化编辑器配置支持多语言 ([7b2eda4](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7b2eda45b3f84d7dbbdf41b532b995762f8092c4))
* no.202307130216 新增可视化编辑器配置支持多语言 ([ecd722e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ecd722e28e0909e536ea6484de2db876a888151f))



## [0.1.7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.6...v0.1.7) (2023-06-21)



## [0.1.6](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.5...v0.1.6) (2023-06-15)


### Bug Fixes

* no.2157826 修复 调整移动端事项关联布局，默认为上下布局 ([9f503a3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9f503a396798ac16edd8abdac5ab9f0c2277ae86))
* no.2212512 修复删除评论数据总数不刷新的问题 ([d8fe3e4](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d8fe3e4581f8a5eb858a86076876ddb8d5a23c45))
* no.2212512 修复删除评论数据总数不刷新的问题 ([f1505f2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f1505f225bb052dbf3f07c8a5cbcf298928c742c))
* no.2212512 修复评论pc端新增评论后总数不刷新的问题 ([433cccf](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/433cccf141d401433388156ed59e2e8fd939ba62))


### Features

* no.2212512 新增eb评论支持二期需求 ([66c2e86](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/66c2e8638cc20764180d926e4d74832f3a944395))
* no.2212512 新增eb评论支持二期需求 ([7bb12fd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7bb12fd56e3822819f0206380027dd63177b957c))
* no.2212512 新增eb评论支持二期需求 ([72ddc9a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/72ddc9addfda5eee6795807fc43ea0b77a3bd8c5))
* no.2212512 新增eb评论支持二期需求 ([eaca33a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/eaca33ab4957029b82fd0eb11ccd85e57073daed))
* no.2212512 新增eb评论支持二期需求 ([560f2f7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/560f2f7d675ad538aa5a7c7f02c32224e79e9e8f))



## [0.1.5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.4...v0.1.5) (2023-06-02)


### Bug Fixes

*  no.2157826 修复defaultOpt设置失效的问题 ([5de4aff](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5de4afff3342f08d7a39a34adb3aaf0d22f7b1a5))
*  no.2157826 修复事项关联组件死循环问题 ([0cb9982](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0cb99820154317243167b7ad3a5423302e66625c))
*  no.2157826 修复没设置关联类型的预览还是可以选到数据，但是下拉框无法下拉选择数据的问题 ([aede6e1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/aede6e1f61fd4d5ed43892a6128fe716272ec69b))
*  no.2157826 修复页面卡顿问题 ([a9bbd1b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a9bbd1baadcbbaf2d9a8172b1461f64b2f895552))
*  no.2157826 修复页面设计器事项关联设计视图组件默认高度问题 ([8cef450](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8cef4503b548bb7cda923c7d6b69aca4d2627189))
* no.1876105 修复Alert 配置图标报错的问题 ([7e43524](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7e43524ed6caa40b81ceab9308571cba5b32ddfb))
* no.1876105 修复关联浏览数据内容无法展示的问题 ([4628366](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4628366d38be26a6ab13323fd2f369c17c067ae7))
* no.2094138 修复关联公共浏览组件配置未分组的问题 ([4d2f81a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4d2f81afe0c7f301d6c9685ca4331d2524288f58))
* no.2157826 修复事项关联构建报错问题 ([b179945](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b17994595259d86be57b8d8badb655cb733210b0))
* no.2157826 修复事项关联浏览框数据重复的问题 ([884841f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/884841f0b1b372e1791f0d1fc765bded8b75b6a7))
* no.2157826 修复事项关联组件兼容性问题 ([54080d3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/54080d314da037860d07f45f8655fab56f05ce91))
* no.2157826 修复事项关联组件在网格布局高度未自适应撑开的问题 ([8af522f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8af522fb813ce751a2aed1a4a08d2ccadbe86109))
* no.2157826 修复事项关联组件多次重复请求数据回显接口的问题 ([a972cc7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a972cc79c32cf0147022b4b7d9e31728c8442697))
* no.2157826 修复多语言未翻译的问题 ([93590f5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/93590f55fd6efc86161346f077136766c23f1eed))
* no.2157826 修复审批id取错的问题 ([faad6b8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/faad6b8397b1bda619b357667dd81de0c2371132))
* no.2157826 修复移动端事项关联组件显示错误的问题 ([d8bb5ad](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d8bb5ad76aaf4a64e970f42e5237f234dac14c82))
* no.2157826 修复移动端排序无效问题(改为接口传参) ([d81fbab](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d81fbab6537877c5f681548cfe4dfccd4e63f672))
* no.2157826 修复移动端表单设计器关联事项组件样式问题 ([ad31fce](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ad31fcebddaf8f2715b2c0e6bf8d14a59d8446b4))
* no.2157826 修复移动端表单设计器关联事项组件样式问题 ([2c7d89d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2c7d89d1eebf4af1322301e5c57053279300b1a0))
* no.2157826 修复表单设计器事项关联id获取错误的问题 ([33460f4](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/33460f4f3c5474de4b17d176fc4b2a9a5997913c))
* no.2157826 修复表单设计器事项关联设计视图容器间距问题 ([32d62b2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/32d62b277259413bf2c122fc96923ba8ae27d6dd))
* no.2157826 修复表单设计器新增表单数据事项关联数据错误的问题 ([9eb8042](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9eb8042eacc16652d639573f8fb85bb66ec7c569))
* no.2157826 修复表单设计器新增表单数据事项关联数据错误的问题 ([d860010](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d86001073d1f9083e4e68c67f1e419f5ccd94718))
* no.2171401 修复外部页面中点击操作按钮无反应的问题 ([4a6e684](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4a6e6844ee79d68842a8cd2a1a71048040fc344c))
* no.2171401 修复外部页面中点击操作按钮无反应的问题 ([fd754bd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/fd754bdaad5be7e2df34efcbbb215b6a313f1309))
* no.2171401 修复外部页面中点击操作按钮无反应的问题 ([6813c91](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6813c9128b42e4c26657fd9bd0432ffb71cee5bc))
* no.2171401 修复外部页面中点击操作按钮无反应的问题 ([a5f7fbb](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a5f7fbbb98721c2ce08349efdf47180237610c5e))
* no.2194705 修复可视化英文下未翻译问题 ([d3e8ddb](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d3e8ddb3805b47cc746ff9a920ed52b87df45cde))
* no.2194705 修复可视化英文下未翻译问题 ([d05bec7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d05bec7f45528bb9e83af5614d7534eed06c908c))
* no.2194705 修复可视化英文下未翻译问题 ([5824a28](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5824a28ba9645cb441e470bc90ef32e8ada666bb))
* no.2194705 修复可视化英文下未翻译问题 ([6c1541e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6c1541e65d788592011ce45d68a85f6d62e07262))
* no.2194705 修复可视化英文下未翻译问题 ([ed8b0cc](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ed8b0cc0e5ecff7dfbe419c89725b2d2fb5342e7))
* no.2194705 修复可视化英文下未翻译问题 ([79c38a7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/79c38a77d29b02b8a44cbe955612ce5fd3624dd1))
* no.2194705 修复可视化英文下未翻译问题 ([eb4de23](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/eb4de239e0e32f0247e1edd7c86479416efcb09f))
* no.2194705 修复可视化英文下未翻译问题 ([7e715c7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7e715c777ca4fe419e1da185df5166084c3aa240))
* no.2194705 修复可视化英文下未翻译问题 ([66903ba](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/66903ba0726c79dcad5793413fe5ac86631d3642))
* no.2194705 修复可视化英文下未翻译问题 ([00b2177](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/00b2177c256133b4ac7dcd18c5a69f25608de024))
* no.2194705 修复可视化英文下未翻译问题 ([9afd43a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9afd43aca85eef82d9e5895cab24a9d55c430142))


### Features

* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：新增Alert EB组件 ([2dbad41](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2dbad41be9ee21e7484b2cad45211fbab72ca712))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：新增Alert EB组件 ([d54c92e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d54c92ef2ce38ad2ef1ad9d0e19d3c6907d932d5))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：新增BreadCrumb EB组件 ([0a70ea7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0a70ea7a01df2c9583de2cedbe5c79061a9b8579))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：新增BreadCrumb组件 ([de0a2c0](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/de0a2c0b204a65e28496599093aac3b2948477d5))
* **202208317466:** release 可 视化问题修复 ([42fe359](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/42fe359bae6007b79a1141262f229e48079383db))
* **202208317466:** release 可视化问题修复 ([616b453](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/616b453bd3063410e85f7d261357a0db4a326e0f))
* **202208317466:** release 可视化问题修复 ([efca0d8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/efca0d8902760543e654842527a7cd46ec65d7ac))
* **202208317466:** 标准页面可视化设计器 bug fix ([7b62daf](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7b62dafeee431ff721bdd2cd47a50fd3b64bfdfb))
* **202208317466:** 标准页面可视化设计器 bug fix ([a72cc4f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a72cc4fe4b2fb7e5664a2c459bcdc727be4947bb))
* **202208317466:** 标准页面可视化设计器 clear otherDatas ([568a48a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/568a48a2adacdb5012c4fb78e5c02b41d51e39c8))
* **202208317466:** 标准页面可视化设计器 clear otherDatas ([d753166](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d753166b45ae024a21d48a162c4494884fe02c4c))
* **202208317466:** 标准页面可视化设计器 default visual data ([3818a44](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3818a449430cf33b23ccc8ddbda0952a6cc8e3c5))
* **202208317466:** 标准页面可视化设计器 fix delete page filter ([3f7071a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3f7071a0c0d79b97f5a893fa29a7f6a4ce836785))
* **202208317466:** 标准页面可视化设计器 fix sort null node ([5d16131](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5d16131359abd2904cb443d5759059e3b934b2cc))
* **202208317466:** 标准页面可视化设计器 fix sort null node ([f731bd1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f731bd1a53676c8b050f09fe57fae2f33cc7d3b2))
* no.1876105 新增EB组件图片 ([9eb222b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9eb222b33e271430a89007324fe28555a2392495))
* no.1876105 新增MenuDesign关联浏览业务数据及模块数据页面设置项 ([5d586cc](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5d586cc6b98ec0c2444faadb7c3f31a6a24ecf03))
* no.1876105 新增关联浏览业务数据设置项 ([e58721a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e58721a40b1c9765070e73e11db9439a12d3951d))
* no.1876105 更新yarn.lock ([454048e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/454048e4449a58b5f286246e373413ca3571013e))
* no.2020847 新增可视化crm模块 ([2e25f62](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2e25f620a5d831d115e025eb652be6b8e6811ac2))
* no.2020847 新增可视化crm模块 ([064d3eb](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/064d3eb18e14a24421688bcc74d9a0a2c43ebc07))
* no.2020847 新增可视化crm模块 ([9f96fc0](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9f96fc0634e3090837904d69252a216bbf7fea45))
* no.2020847 新增可视化crm模块 ([3e45dc8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3e45dc852aab4846f259cc4af8668edb25a282af))
* no.202208317466 可视化问题修复 ([73f85f9](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/73f85f9be01026d692a3d3c22b73c5d037c79347))
* no.2094138 新增eb 组件（Alert） ([dff3f78](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/dff3f78ad7e80e71d22bbd433bcefa963516e8ac))
* no.2094138 新增eb 组件（BreadCrumb-接入事件） ([4221d8e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4221d8ed99b12f0ad9bf6d0fc807d3dad4292e50))
* no.2094138 新增eb 组件（BreadCrumb） ([e341942](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e3419428c578065f8b21d57ea7a5a93c54393a49))
* no.2094138 新增eb 组件（BreadCrumb） ([f1c3414](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f1c3414750e37c16ddc6dda68efb9d6c851e01f7))
* no.2094138 新增eb 组件（BrowserPanel) ([4c1da4f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4c1da4f3538641098f8343bb8ec7d2e6e2a960b7))
* no.2094138 新增eb 组件（Empty) ([238d505](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/238d505042dde78d3e81310ccdd84f15582dcf1c))
* no.2094138 新增eb 组件（Empty) ([848c48a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/848c48a93e48792074737b037c6ce129acdd3f14))
* no.2094138 新增eb 组件（Empty) ([650d767](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/650d7676fa6fd81797d7295265ec72a58704ac96))
* no.2094138 新增eb 组件（Empty) ([fad5f7b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/fad5f7b3e20318edb28628ce5428bdf4330c205e))
* no.2094138 新增eb 组件（Empty) ([b893468](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b89346830ed6109b1ca71e65f91ab09e6c9453d8))
* no.2094138 新增eb 组件（Empty) ([e0b7341](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e0b734111c02d65c95c41f385aafe668428bdaa9))
* no.2094138 新增eb 组件（关联浏览业务数据) ([6339cae](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6339cae48ea33eb4b92822f0eae82d8cf65446a8))
* no.2094138 新增eb 组件（关联浏览业务数据) ([ba004ac](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ba004ac1ec8d6f846b4c8f098a7d0906c8be1ee9))
* no.2110936 新增评论支持添加eb组件 ([162865a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/162865a74955130bce57df968a19773e6c16ed13))
* no.2150585 新增移动端事项关联组件 ([aedb730](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/aedb73063bafce32367f242738b2a8820c79aaee))
* no.2157826 新增 适配表单设计器样式调整 ([91e11e2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/91e11e2f17d262a374b331e81411ce05a843bd05))
* no.2157826 新增Alert组件 ([0727002](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0727002fa6d2256e3e4ff5e9ac8c0362b903c7af))
* no.2157826 新增两种保存模式 ([ea54f88](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ea54f8857bef9d4b4e1a6c3f8282792f18fdbbb7))
* no.2157826 新增事项关联EB组件 ([72e0a9f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/72e0a9f43f87fca50a572c1d1b1d4600060e3877))
* no.2157826 新增事项关联EB组件 ([c356fd5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c356fd58ba2931c8dbdcc4e179d90b8e82e1a006))
* no.2157826 新增事项关联EB组件 ([ca3d526](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ca3d526e107270b8723e614515d0bd095023eed3))
* no.2157826 新增事项关联EB组件 ([aa8e2ed](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/aa8e2ed96caba93e77aae74c8b6b8801eca039b7))
* no.2157826 新增事项关联EB组件 ([3d388cc](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3d388cc0ec38f3ee9024828fa79e2ab18f73e6e0))
* no.2157826 新增事项关联EB组件 ([558c016](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/558c016366b0a16d0ca7cc5a9c9b478295fa6e10))
* no.2157826 新增事项关联EB组件 ([03f8d34](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/03f8d34f76a127321bc64d1c32b43ea5c6d5e98e))
* no.2157826 新增事项关联id获取逻辑 ([0259b2e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0259b2e6e22fa9d23e291d11c274e603488f875a))
* no.2157826 新增事项关联id获取逻辑 ([3447c0f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3447c0fce6e2715d91f3ff9516cf22e1f535cc43))
* no.2157826 新增保存方式设置图标 ([b2ffbc7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b2ffbc78e695e4a0aba8a8a215404592bd9f544c))
* no.2157826 新增排序功能 ([4118481](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/41184819fea19db3fc810b3fe01a9a8cdc849eb3))
* no.2157826 新增过滤多余数据(未设置的类型不可展示)逻辑 ([e01cb21](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e01cb21c99cf4518ad0e65f37822d7fb729ca4b3))


### Performance Improvements

* no.2178023 优化eb评论部分内容 ([96f0ebb](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/96f0ebbe53c29982a41e37fc94d200df0d23f448))
* no.2178023 优化eb评论部分内容 ([42b76ca](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/42b76ca76cd5fcec3b64e674981c93bd55a2e6d2))



## [0.1.4](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.3...v0.1.4) (2023-04-07)


### Performance Improvements

* no.2178023 优化eb评论部分内容 ([6697eec](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6697eec6d33c445f376d4924d94669dcce558ad4))



## [0.1.3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.2...v0.1.3) (2023-03-31)


### Features

* no.2110936 新增评论支持添加eb组件 ([e634a51](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e634a51239274bb48ede94f1f6545372256823c9))



## [0.1.2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/compare/v0.1.1...v0.1.2) (2023-03-03)


### Bug Fixes

* no.2112891 修复MenuDesign菜单停用仍然展示的问题 ([e725e57](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e725e573ad1a8d05da1fed0675cbcd7e26888176))
* no.2112891 修复代码报错问题 ([a10c517](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a10c517e90afa2a211a9bcc9719ad7a40fff5ac0))
* no.2112891 修复老数据兼容问题，页签停用未隐藏 ([70b8679](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/70b867990bfc1be9414b86b7776a4491e0124017))



## 0.1.1 (2022-11-25)


### Bug Fixes

* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述: 修复data数据部分丢失的问题 ([9c9ef47](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9c9ef4789cffd14a7724a005d5994bdb92215c8a))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述: 屏蔽非string类型页签的配置 ([b74832a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b74832a7b2b3ceca48408ec88de19b720518417d))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述: 调整cacheData至全局(缺少卸载，等可视化支持) ([91505cb](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/91505cb14b5e61181b6273e590849882e81c66f5))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述: 转业务mobx数据，解决拖拽排序报错问题 ([7e2ac7d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7e2ac7ddd2b45db9e3a9a3b4b6115aaac65c54f0))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述：menu-design 编辑弹框默认可拖拽 ([c4c273c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c4c273c223f4fd3ecd915b0ec3d42f0766e30314))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述：修复存在ecode复写代码的情况下，统一页签多次新增的问题 ([07374cd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/07374cdd5214ed66a4cc1173ccd0d16b98f7a239))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述：修复新增多次页签，非首次新增的页签无法展示MenuContent的问题 ([d10e67d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d10e67da352088933e8b2cda7a68fc83a903ef68))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 概述：去除页签设置提示及修复menucontent生成代码多了；的问题 ([fbbccd7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/fbbccd7f6c7e48d749c0cf6a222e6ffa0e840bfe))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856):修复排序失效的问题 ([7827fcb](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7827fcb70f72abe4d66bf491456ddcaaeab4c96d))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856):修复排序失效的问题 ([0bc768c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0bc768c1ccff9dd40af7d03c52b440bfe27ac4a2))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856):修复排序失效的问题 ([be9b257](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/be9b2576b55451a1b2dd6a7f00ed8fa451f6d126))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：修复BrowserPanel第二次选不中的问题 ([39e4739](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/39e4739de22c0aa83e7a10f86d85e5871a0c7b8c))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：修复MenuDesign单个事项权限解析错误的问题 ([debb78c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/debb78cc25ffeb7986dc5136266e1fcc037ca52d))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：修复MenuDesign单个事项权限解析错误的问题 ([0e0e088](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0e0e088b287372538eb4c340ceef180c3831b9cd))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：修复可视化编辑器关闭，未取消占位的问题 ([7a6e5ef](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7a6e5efa7006b8d81bea261c9027274cf09a3efb))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：屏蔽browser_panel底部按钮 ([c0214e3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c0214e3e0a633ad854de740f02414dda54634a7a))
* **202210128051:** 概述: Button组件修改属性配置 ([fac4b81](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/fac4b818aaf1ce05213de7fa516d7f407becb53c))
* **202210128051:** 概述: Button组件修改属性配置(去掉lib导出) ([784316d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/784316dbc1a994d6808b48a494c69f41a22f413c))
* 屏蔽模块数据及事项筛选权限 ([533078e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/533078ed6b9dc2ab05350a7acc62effb10ac3e17))
* 开放设置类型选项 ([a135d1d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a135d1d0209031103a2f7e3f5377dd7bdec69652))
* 概述: NewButton改为Ebbutton ([87642b3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/87642b355c495632d1a83bd01c552681dfd534e4))
* 概述: 按钮名称暂时统一为按钮 ([7fd6484](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7fd648428f9fb462d616d15f3c9c0d77e0d22037))


### Features

*  [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：更新md ([0b4742b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0b4742bdeb1b2de19c4630903e2bf8de1c465b58))
* [#179585](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/179585) 概述：【可视化】接入browser配置项 ([b38ec02](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b38ec02fbff8169aa14b5c4bb9dc70eb71b98c38))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856) 新增数据缓存机制 ([96027d3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/96027d3bb98017d2e5545a18b91cac53a89ded1b))
* [#1795856](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1795856)概述：新增页签排序功能 ([3c66a03](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3c66a0395f891ce64e088002444ad67ab5c3a487))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述: 去除console ([4d02f2c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4d02f2c2c826b8a35a7a78266cbd03960a87106c))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述: 提交BrowserPanel EB组件 ([1777ea7](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1777ea784dbbea7091db9b7e1bdf19dc151d3c4a))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述: 支持BrowserPanel模块类型配置数据回显 ([5f4a8bd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5f4a8bd254a3418a834192332963db84e701d58e))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：demo接入可视化编辑器 ([f2738fe](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f2738feace38b5be1680b77d538454e47f4b47a0))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：demo更新 ([427a963](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/427a96329c580515c9fb383aabd534a2f45bf895))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：demo更新 ([ec55462](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ec55462de02b0be331a41361cedcce0c0f1157de))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：demo更新 ([7c8ab91](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7c8ab919d8f69b3caafcfff192b21fc5b0a0c1e1))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：demo更新 ([6683c6e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6683c6ee275240f8867d44b9423abac658f07b68))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：优化Empty EB组件 ([aa6f611](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/aa6f611798e9e44e4b2dcb1b77fbc8f1dca97882))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：优化transform配置方案 ([a990a85](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a990a85e4ecf17e9062efb54c13367d5670dad08))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：提交图片 ([2430592](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/243059211b1add9e92726f04296837c81b19e0fc))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：新增Empty EB组件 ([8857b98](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8857b98046814db5cb20d9f187c7a06b0199b620))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：更新Demo文档 ([9ffb80d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9ffb80d1e61ff797f2fb6f7761656c8c0454b9bc))
* [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述：调整Menu接入Browser配置页面效果及优化设置页面 ([69bbe6c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/69bbe6c90f1e29a1fa596d82b7b7813b71601681))
* **202208317466:** menu-design add pageParam ([9054c8f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9054c8f23a5b8d15774956319a3b27744e5e83a7))
* **202208317466:** menu-design add pageParam ([2081045](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/20810451809fedce7681bbcb11ea4adbc2bf5e8d))
* **202208317466:** menu-design ebpage add conditions ([5229f7c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5229f7c741922624205fae8aa3a328410a2a8305))
* **202208317466:** menu-design style ([abc01b1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/abc01b1cddf905ee560f41357d72d2f8a67a51d7))
* **202208317466:** menu-design style ([c8189d5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c8189d5509833a72e85d1e5819738b90b368d423))
* **202208317466:** menu-design style ([cdc56fd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/cdc56fdc15924be656744a14b358d76415e419c5))
* **202208317466:** menu-design style ([f3944ad](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/f3944ad8935f9aa72b4d3414f209039f179477cc))
* **202208317466:** release 可 视化问题修复 ([2e69620](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2e696208cfdc7bdd63190b02a98c424dd08c59c9))
* **202208317466:** release 可视化问题修复 ([cc5b418](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/cc5b418ea24a83e5aa52199cc7e31b720e989ffb))
* **202208317466:** release 可视化问题修复 ([75d9967](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/75d99675f3cd1b4a8e9252f7affb7d862b2954c8))
* **202208317466:** 标 准页面可视化设计器 add ebuilder required ([a3acc8a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a3acc8a82774effbf18ff6bcc1ec2aed3e76aa48))
* **202208317466:** 标 准页面可视化设计器 add findfibers ([9066599](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9066599bf2285adaec95400425140ec51bb4dd22))
* **202208317466:** 标 准页面可视化设计器 add tojs ([9f56b5b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9f56b5bc52c17f6ccd20f4f66d7de8594f95035b))
* **202208317466:** 标 准页面可视化设计器 add tojs ([664ba74](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/664ba74a1ac54510d5b613cdea65af10d15999f0))
* **202208317466:** 标 准页面可视化设计器 bug fix ([1da1bf8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1da1bf8983d90362bf3b9008f26d2239c7d6d69d))
* **202208317466:** 标 准页面可视化设计器 bug fix ([6975fd1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6975fd16714615f37135dbe1137bf73d83b1d5d8))
* **202208317466:** 标 准页面可视化设计器 bug fix ([97e164d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/97e164d4afd4c44cd75211b917dc821f681087b5))
* **202208317466:** 标 准页面可视化设计器 bug fix ([244541e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/244541e9f82c25132922412e2651cd188b55b73d))
* **202208317466:** 标 准页面可视化设计器 bug fix ([8d33bb3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8d33bb3b78ef9b088a9c47611712ca0e5761b9a6))
* **202208317466:** 标 准页面可视化设计器 bug fix ([a74d6f1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a74d6f1e9b32c7ff53a320a6072522605313112e))
* **202208317466:** 标 准页面可视化设计器 bug fix ([b1ffd1f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b1ffd1f495a38c9d8994f2014d6edf9c62975e54))
* **202208317466:** 标 准页面可视化设计器 bug fix ([eeb246f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/eeb246ff10b3b63ad4e509a30b6ee56f1037bf47))
* **202208317466:** 标 准页面可视化设计器 bug fix ([280f01e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/280f01e6240c334abf3dfc1a258246ed9164f11b))
* **202208317466:** 标 准页面可视化设计器 checkAuth ([0643b56](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0643b563f267ac99b8d9c1235bf9ae47b5f1fb25))
* **202208317466:** 标 准页面可视化设计器 checkSingleAuth ([08aa405](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/08aa4052a5bab7acaf270be57a95a29abea1abfe))
* **202208317466:** 标 准页面可视化设计器 checkSingleAuth ([e21ccd5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e21ccd527a8ae087ca9c48487eb695c677d1f491))
* **202208317466:** 标 准页面可视化设计器 filter fixfix ([050d3ec](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/050d3ecd6c0414cc7b649ab73614f9449c1397ec))
* **202208317466:** 标 准页面可视化设计器 filter sort ([895f272](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/895f2729e9c952c089afc50a0adfbfa29dca7362))
* **202208317466:** 标 准页面可视化设计器 fix console error ([d2d4158](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d2d4158041fcf645d63ce53de71216cb74f3f7e9))
* **202208317466:** 标 准页面可视化设计器 fix sort and matterfilter ([239b047](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/239b047772ed1a9f42ba762692db5076df0a4bd9))
* **202208317466:** 标 准页面可视化设计器 fix sort and matterfilter ([c3533a2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c3533a2190227b75d7dc2be32b4acf96e7b30b54))
* **202208317466:** 标 准页面可视化设计器 fix sort and matterfilter ([30e5c53](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/30e5c53dd50e7424511152d25e5bd7645009f49f))
* **202208317466:** 标 准页面可视化设计器 fix 重命名报错 ([9e1df78](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9e1df78910e60a98578f6329a5eed212f737a602))
* **202208317466:** 标 准页面可视化设计器 fix 重命名报错 ([7c6f4ab](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7c6f4abc98530e9d6fae9add12dd842f9da14a8d))
* **202208317466:** 标 准页面可视化设计器 getcode reg ([ab4689a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ab4689ac9906a338372ca96b9bf96006bb14b1f5))
* **202208317466:** 标 准页面可视化设计器 inspect-disabled ([76d3e80](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/76d3e809186ebacdb982ff6c09f0cef24e387328))
* **202208317466:** 标 准页面可视化设计器 inspect-disabled ([6b91f5d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6b91f5d27b8f31f91cf5a8f0152f45ee9c8b1559))
* **202208317466:** 标 准页面可视化设计器 inspect-disabled ([c028989](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c028989a66e448b8583bcf4f250ca2143d072ea1))
* **202208317466:** 标 准页面可视化设计器 inspect-disabled ([49ce614](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/49ce614fc3b539f966ce36975e58ecd506ae69ee))
* **202208317466:** 标 准页面可视化设计器 inspect-disabled ([af6de6d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/af6de6d8a48bcb2e28a5b1be94aab8a9f04261e5))
* **202208317466:** 标 准页面可视化设计器 realtimerender add autoSelect ([c862eb3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c862eb3de4a29c82899dfedda11855c51b879d9b))
* **202208317466:** 标 准页面可视化设计器 style ([d824ee5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d824ee5cae009e458e594e7c2b0d20365f85b5ec))
* **202208317466:** 标 准页面可视化设计器 style ([0ff2cbe](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0ff2cbe3bb5b7a179f85d588e584a58e47d9745b))
* **202208317466:** 标 准页面可视化设计器 排序清楚isnew ([ebeb009](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ebeb009e1fd9f8c22148b27958677b3130e3f3fd))
* **202208317466:** 标 准页面可视化设计器 标记新增项 ([a0a709e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/a0a709eaaa7d8b7d1545652f7c258800c1aa1892))
* **202208317466:** 标 准页面可视化设计器 标记新增项 ([54bbc2d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/54bbc2dc66eef1ecc136e35ce23c0cceadb8fb0c))
* **202208317466:** 标 准页面可视化设计器 添加通用事项过滤和visualdata ([3ac26b4](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3ac26b4841b18bb502dccc6ef47364ee3a738ca9))
* **202208317466:** 标 准页面可视化设计器 添加通用事项过滤条件样式优化表格形式 ([854f8da](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/854f8dafe9a61f4a786cd3943a6cbb430ce43b1b))
* **202208317466:** 标 准页面可视化设计器 禁用设置不生效 ([6a09ec5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/6a09ec5e4de3693923e81e7f14ba6a51cfd8733b))
* **202208317466:** 标 准页面可视化设计器 通用筛选弹框设置宽度 ([20859e3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/20859e314062b748269e9a71db5c00551a0a79d9))
* **202208317466:** 标 准页面可视化设计器 问题记录8修复 ([336a07e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/336a07ed2d3c8694466d34e91407a1e9642c2f83))
* **202208317466:** 标 准页面可视化设计器 页面过滤条件优化成表格形式 ([4d0a75f](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4d0a75f66624689b40541055e1c5775317914600))
* **202208317466:** 标准页面可视化设计 add dataset id ([e363bbd](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e363bbd9bd9220d5f5fda82d65f78cc3484484f4))
* **202208317466:** 标准页面可视化设计 change comptype ([1e0e91b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1e0e91b14158108584726bdcf7c9588aff38f349))
* **202208317466:** 标准页面可视化设计 change dataid reg ([ac1ce5b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/ac1ce5b2a147905efd0cca1fc4f361489758cc4d))
* **202208317466:** 标准页面可视化设计 style fix ([4d10e5c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/4d10e5ca7acee0ee615e0f0b16cea32b9f846ea6))
* **202208317466:** 标准页面可视化设计 style fix ([3606266](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3606266d268c55480df0469f0d67ea1fbfb84c12))
* **202208317466:** 标准页面可视化设计器 bug fix ([16f317a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/16f317a8ee795cda0d10780a1b4231d94d8436bd))
* **202208317466:** 标准页面可视化设计器 bug fix ([8e9ff47](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8e9ff4783b5a93ff16f41750a25d9e6da2f68c68))
* **202208317466:** 标准页面可视化设计器 bug fix ([dad1394](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/dad1394a73436f0a0ca3fab2854bb9f1ee34e7b3))
* **202208317466:** 标准页面可视化设计器 change dataid ([b9f7893](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b9f78936b6e12f7050146829a1e554238cc5c969))
* **202208317466:** 标准页面可视化设计器 clear otherDatas ([78e782b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/78e782b02c79a3127ab40a395a56bd4140283a6e))
* **202208317466:** 标准页面可视化设计器 clear otherDatas ([320aa10](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/320aa10c72db0109c8a07d9e48e10ee537aefb5d))
* **202208317466:** 标准页面可视化设计器 default visual data ([e8392f2](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e8392f266af38ce36194be2d794368ac0812fea2))
* **202208317466:** 标准页面可视化设计器 fix delete page filter ([31ab6d4](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/31ab6d4193c8f7600f75de0139a50e4487af891d))
* **202208317466:** 标准页面可视化设计器 fix sort null node ([e11411c](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/e11411c2c2dc22ff26a63876f0807721d1681cf0))
* **202208317466:** 标准页面可视化设计器 fix sort null node ([9596324](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9596324566ec9acc34bcdb4b45c1368075473ed8))
* **202208317466:** 标准页面可视化设计器 visualdata在排序的时候取旧值 ([0fcae6d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/0fcae6d174851a806035e3f374688e3b9359c39c))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([5690ee9](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/5690ee985d2542fe94d4da9fae0ff2887b05a4b2))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([b3fd94d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b3fd94d3b3bbb5aa7c277b3564c546007fc3c7c8))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([78011bf](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/78011bf700edb855af0d302fe5f845975252923c))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([7d565e5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/7d565e58507b60428e6b49a0317adc76c1679e14))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([3df0d49](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/3df0d496225c41e15485dd731879ad5ccd2217b5))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([436c493](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/436c493264daac9d67849a6a060a47855e4f9970))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([2340381](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/2340381773d57a1ff18694410f8609cd71037128))
* **202208317466:** 标准页面可视化设计器 修复标签被覆盖bug ([59a74e5](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/59a74e501286209ec927af6a2578e8e1e6aeeb07))
* **202209067693:** 概述: Button添加transform方法 ([c83805e](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/c83805e086bffebec282f4d59d3883d60658ab90))
* **202209067693:** 概述: Button添加transform方法 ([91065de](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/91065de9b261bc3cd31d2aa43042b137466c8a68))
* feat: [#1876105](http://10.12.101.12/FRONTEND/weapp-ui-props-design/issues/1876105) 概述: 新增BrowserPanel demo ([d897630](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/d897630a968fd6e5a83b4d3cc71ad9245a4a4d5e))
* **help:** 满足help组件对接EB的需求 ([49962ea](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/49962eab08380deee77a7226842d80e849c4a86d))
* **help:** 满足help组件对接EB的需求 ([cb8c481](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/cb8c48100eff23bcf6d4208d284603e8486e8c24))
* menuDesign接入BrowserPanel配置 ([1a248cc](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/1a248ccf7241b8eba9d403403c36e1f0d20b8ead))
* 提交Menu EB组件 ([b22d14d](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b22d14d6b3c62514eab164b9e0808ef665a28168))
* 提交项目 ([07422e8](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/07422e813021969a3b6a559c74feafcd600d764d))
* 放开事项筛选 ([8ee27b3](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/8ee27b3fc03d7f2c466bec0c64f52f1bd2e220a0))
* 新增dialogProps配置 ([567f551](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/567f5510788c9c412dac3e75d0aedd963353baad))
* 新增onAdd、onClose、dialogProps ([9e3d8d6](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/9e3d8d658f5f67d324be27c88c010090040fd16a))
* 概述: NewButton的transform方法 ([b5ecb1a](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b5ecb1a0a762690d1f66126fc03219ecbbb59e51))
* 概述: 添加button config ([b683eb1](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/b683eb131a59fc909a34deb529fc18855bd6a696))
* 概述: 添加webpackChunkName ([dafcc4b](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/dafcc4b2ed539f21d6508d8ccf3bffb594962d02))
* 调整关联类型名称 ([145d5ad](http://10.12.101.12/FRONTEND/weapp-ui-props-design/commits/145d5ad42792ca90819a6d38fd376f2966747f44))



