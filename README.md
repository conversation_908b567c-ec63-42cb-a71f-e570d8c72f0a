# Getting Started

[Document](https://e-cloudstore.com/doc.html?appId=55f7cd63141a4a029e9259e5abaf737f)

### weapp-ui-props-design
- [1] 仓库处理weapp-ui库组件对应的可视化组件
- [2] 可视化组件分成两类：独立的EB组件、功能与目前已有的EB组件重复的配置组件
- [3] [可视化组件分类表](https://www.e-cology.com.cn/spa/custom/static/index.html#/main/cs/app/86421e44bf26440ca951b35c3ad1fa4c_baseTable?fileid=22385108&tosharerinfo=d6b83c1c-e62e-44a8-8716-41f692c9e253&tosharetype=group&_key=kv4h4w)
```
1.独立的EB组件：EB可视化设计器没有开发过的、设计器内有类似功能却不能通用的：如BrowserPanel
2.功能与目前已有的EB组件重复的配置组件：EB可视化已有相关功能的组件且与ui库提供的对应组件功能相同：如Button
3.对于1，本仓库忽略EB设计器已有的内容，对标UI库组件，重新开发对应的EB组件，需处理Config、View、Design，并通过sql脚本，接入EB设计器的UI可视化组件分类，确保可视化编辑、新建效果统一
4.对于2，本仓库仅提供Config，也无需通过sql脚本接入EB设计器。仅作为编辑标准页面的配置项。
5.可视化最终生效通过生存ecode代码实现，目前由weapp-designer仓库统一实现
6.对于1，开发者需重点关注component、eccoms.ts
7.对于2，开发者需重点关注component、compConfigs.ts
```

### 项目目录说明
- [1] components: 主要为EB组件
- [2] components-share: 公共组件
- [3] constants: 常量
- [4] pages: Demo
- [5] utils: 公共方法
- [6] ebcoms.ts : EB组件入口
- [7] index.ts: Demo入口
- [8] lib.tsx: 项目入口
- [9] ebConfig: 公共EB组件配置 
- [10] compConfigs.ts: EB配置组件入口(仅支持配置)

### 开发说明
- [1] ebcoms.ts 最终在lib内导出，开发EB组件关注components及eccoms.ts
- [2] transform.ts 最终在lib内导出，开发EB组件关注components及transform.ts
- [3] EB组件名字对标UI库 设置的 @refs
- [4] EB组件说明: Config(配置项)、Design(设计视图)、View(运行视图)、TransForm(组件props转EB组件config)
- [5] Ui库需对应处理`@title`，`@refs`，`@transform`, `@config`,具体见[参考文档](https://weapp.eteams.cn/sp/techdoc/view/722818617793552384/738612836087922688)
- [6] ebMiddleware 中间件：统一过滤EB config数据，用于Design、View
- [7] 新增EB组件需在系统中提交sql脚本，具体操作见[组件维护](https://weapp.eteams.cn/sp/techdoc/view/722818617793552384/726005526502023168)

[公共组件接入EB设计器规范](https://weapp.eteams.cn/sp/techdoc/view/722818617793552384/775408971933327360)

### 热调试说明
- [1] 本项目主要依赖（ebdcoms|ebdpage|ebddesigner|designer|designer-demo|ebdapp|ui|utils)，具体参见setupProxy文件（也可改用nginx等其他方式处理）

setupProxy配置
```
const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
  app.use(
    '/+(api|papi)/',
    createProxyMiddleware({
      target: 'https://weapp.yunteams.cn/',
      changeOrigin: true,
      secure: false,
      headers: {
        referer: 'https://weapp.yunteams.cn/',
      },
      logLevel: 'debug'
    })
  );
  /**
   * 默认请求和模块库走测试环境
   */
  app.use(
    '/build/+(ebdcoms|ebdpage|ebddesigner|designer|designer-demo|ebdapp|ui|utils)/',
    createProxyMiddleware({
      target: 'https://weapp.yunteams.cn/',
      changeOrigin: true,
      secure: false,
      headers: {
        referer: 'https://weapp.yunteams.cn/',
      },
      logLevel: 'debug'
    })
  );
};
```

- [2] 可视化设置界面(weapp-ebddesigner项目)，开发模式开放所有组件。weapp-ebddesigner项目以[window.location.hostname包含yunteams为标准](http://************/EBUILDER/weapp-designer/-/merge_requests/2076/diffs#diff-content-7b3d875ccf0ec08a504075e0fdf0ab8c160a240e)。
- [3] 基于2，开发本地可通过配置host处理，例配置host： 127.0.0.1 develop.yunteams.com